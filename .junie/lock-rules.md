# Command Rules

## Overview

This document outlines the rules and patterns for implementing locking using internal lockinghandler library.

### Key Principles

1. **Public constant naming**: Every module has exactly one string constant in `LockModules` file.
2. **LockNames constant**: Every module has its specific `LockValues` file with string constants.
3. **Lock usage**: Lock constants are used in `@Lock` annotation, module param is used from LockModules and lockName from LockValues file. 

## Package Structure

```
com.cleevio.gimee
└── application
    ├── common
    │   └── constant    # LockModules constants
    └── module
        └── example     # Module-specific locks
            └── consant # Lock constants definitions
```

## Naming Conventions

- **LockModules**: In LockModules constant vals are named as upper case name of module, its string values is upper case name of module with "_MODULE" suffix (e.g., `const val CHALLENGE = "CHALLENGE_MODULE"` - where challenge is name of module))
- **Module specific LockValues file naming**: Kotlin file name in module is created from module name with "LockValues" suffix (e.g., `ChallengeLockValues.kt`)
- **LockName**: LockNames are places in LockValues files. LockNames are named as upper case name of action and module (e.g., `const val CREATE_CHALLENGE = "CREATE_CHALLENGE"`)
  - LockName should be unique in whole project
  - LockName should be descriptive and reflect the purpose of the lock
  - LockName should be in UPPERCASE with underscores separating words

## Implementation Patterns

### Usage Definition

- **@Lock** annotation is used on method:
  - that needs to be locked
  - is transactional (has `@Transactional` annotation)
  - in CommandHandler
  - only @Lock annotation in added above method no more code is needed
- **lockName** is used in `@Lock` annotation as `lockName` parameter.
  - LockName should be created based on action that CommandHandler provides (eg. `CreateChallengeCommandHandler` should have `lockName = CREATE_CHALLENGE`, `UpdateChallengeCommandHandler` should have `lockName = UPDATE_CHALLENGE` )

```kotlin
@Transactional
@Lock(module = CHALLENGE, lockName = CREATE_CHALLENGE)
```

