# JOOQ Rules

## Overview

This document outlines the rules and patterns for using JOOQ in our projects. It serves as a guide for developers to understand how to work with JOOQ for database access.

## JOOQ Architecture

JOOQ (Java Object Oriented Querying) is used as the primary database access technology in our projects. It provides type-safe SQL query construction through generated code that matches our database schema.

### Key Principles

1. **Type Safety**: Use JOOQ's generated classes for type-safe SQL queries.
2. **Clean Architecture**: JOOQ implementations are placed in the adapter layer as "out adapters".
3. **Separation of Concerns**: Database access logic is separated from business logic.
4. **Code Generation**: Database schema is reflected in generated JOOQ classes.
5. **Don't Compile**: Don't check if JOOQ compiles, contains all imports, or has unresolved references.

## Package Structure

```
com.cleevio.gimee
├── adapter
│   └── out
│       └── jooq           # JOOQ implementations of port interfaces
├── application
│   └── common
│       └── converter      # Custom JOOQ converters
└── jooq                   # Generated JOOQ classes
    ├── tables
    ├── keys
    └── indexes
```

## Naming Conventions

- **JOOQ Implementations**: Named after the port they implement with "Jooq" suffix (e.g., `GetUserJooq`, `SearchCoursesJooq`)
- **JOOQ Converters**: Named after the type they convert with "JooqConverter" suffix (e.g., `TraderTagListJooqConverter`)
- **Generated Tables**: Named after the database tables in uppercase (e.g., `APP_USER`, `COURSE`)

## Implementation Patterns

### Basic Query Pattern

```kotlin
@Component
class GetUserJooq(private val dslContext: DSLContext) : GetUserPortOut {

    override fun fetch(userId: UUID): GetUserQuery.Result = dslContext
        .select(
            APP_USER.ID,
            APP_USER.EMAIL,
            APP_USER.ROLE
        )
        .from(APP_USER)
        .where(APP_USER.ID.eq(userId))
        .fetchOne()
        ?.map {
            GetUserQuery.Result(
                userId = it[APP_USER.ID]!!,
                email = it[APP_USER.EMAIL]!!,
                role = it[APP_USER.ROLE]!!
            )
        }
        ?: throw UserNotFoundException("User '$userId' not found.")
}

```

### Basic Search Query Pattern

#### Sort only one table 

```kotlin
@Component
class SearchUserJooq(
	private val dslContext: DSLContext,
) : SearchUser {

	override fun fetch(query: SearchUserQuery.Query): SimplePage<SearchUserQuery.Result> {
		val conditions = listOf(
			query.filter.fullTextSearch?.let { USER.EMAIL.likeIgnoreCase("%$it%") },
		)

		val totalCount = dslContext.fetchCount(USER, conditions)

		return dslContext.select(
			USER.ID,
			USER.EMAIL,
			USER.CREATED_AT,
		)
			.from(USER)
			.where(conditions)
			.sort(query.sortOrDefault(), USER)
			.page(query.paginationOrDefault())
			.fetch()
			.map {
				SearchUserQuery.Result(
					userId = it[USER.ID]!!,
					email = it[USER.EMAIL]!!,
					createdAt = it[USER.CREATED_AT]!!,
				)
			}.let {
				SimplePage.of(
					content = it,
					totalCount = totalCount.toLong(),
				)
			}
	}
}

```

#### Sort one table plus aggregates

```kotlin
@Component
class SearchUserJooq(
	private val dslContext: DSLContext,
) : SearchUser {

	override fun fetch(query: SearchUserQuery.Query): SimplePage<SearchUserQuery.Result> {
		val conditions = listOf(
			query.filter.fullTextSearch?.let { USER.EMAIL.likeIgnoreCase("%$it%") },
		)

		val totalCount = dslContext.fetchCount(USER, conditions)

        val transactionCount = DSL.select(DSL.count())
			.from(TRANSACTION)
			.where(TRANSACTION.USER_ID.eq(USER.ID))
			.asField<Long>("transactionCount")
        
		val sortMap: Map<Enum<*>, Field<*>> = mapOf(
			SearchUserQuery.SortField.TRANSACTION_COUNT to transactionCount,
		)

		return dslContext.select(
			USER.ID,
			USER.EMAIL,
			USER.CREATED_AT,
			transactionCount,
		)
			.from(USER)
			.where(conditions)
			.sort(query.sortOrDefault(), USER, sortMap)
			.page(query.paginationOrDefault())
			.fetch()
			.map {
				SearchUserQuery.Result(
					userId = it[USER.ID]!!,
					email = it[USER.EMAIL]!!,
					createdAt = it[USER.CREATED_AT]!!,
					transactionCount = it[transactionCount]!!,
				)
			}.let {
				SimplePage.of(
					content = it,
					totalCount = totalCount.toLong(),
				)
			}
	}
}

```

#### Sort more tables

```kotlin
@Component
class SearchUserJooq(
	private val dslContext: DSLContext,
) : SearchUser {

	override fun fetch(query: SearchUserQuery.Query): SimplePage<SearchUserQuery.Result> {
		val conditions = listOf(
			query.filter.fullTextSearch?.let { USER.EMAIL.likeIgnoreCase("%$it%") },
		)

		val totalCount = dslContext.fetchCount(USER, conditions)

        val transactionCount = DSL.select(DSL.count())
			.from(TRANSACTION)
			.where(TRANSACTION.USER_ID.eq(USER.ID))
			.asField<Long>("transactionCount")
        
		val sortMap: Map<Enum<*>, Field<*>> = mapOf(
			SearchUserQuery.SortField.EMAIL to USER.EMAIL,
			SearchUserQuery.SortField.TRANSACTION_COUNT to transactionCount,
			SearchUserQuery.SortField.CREATED_AT to COMPANY.CREATED_AT,
		)

		return dslContext.select(
			USER.ID,
			USER.EMAIL,
			COMPANY.CREATED_AT,
			transactionCount,
		)
			.from(USER)
            .innerJoin(COMPANY).on(USER.ID.eq(COMPANY.ID))
			.where(conditions)
			.sort(query.sortOrDefault(), sortMap)
			.page(query.paginationOrDefault())
			.fetch()
			.map {
				SearchUserQuery.Result(
					userId = it[USER.ID]!!,
					email = it[USER.EMAIL]!!,
					createdAt = it[COMPANY.CREATED_AT]!!,
					transactionCount = it[transactionCount]!!,
				)
			}.let {
				SimplePage.of(
					content = it,
					totalCount = totalCount.toLong(),
				)
			}
	}
}

```

### Enums
If mapping to enums (e.g.`it[COUNTRY]!!`), add specific type to `pom.xml`. For example:
```
<forcedType>
    <userType>com.cleevio.gimee.domain.common.constant.Country
    </userType>
    <enumConverter>true</enumConverter>
    <includeExpression>
        .*\.COUNTRY
    </includeExpression>
</forcedType>
```

## Best Practices

1. **Handle Nullability**: Use the `!!` operator or safe calls (`?.`) when accessing record values.
2. **Map to Domain Objects**: Map JOOQ records to domain objects using dedicated mapping functions.
3. **Use Extension Functions**: Use the provided extension functions for common operations like pagination and text search.
4. **Implement Port Interfaces**: JOOQ implementations should implement port interfaces from the application layer.
5. **Use Custom Converters**: Create custom converters for mapping between database types and domain types.
6. **Keep Queries Readable**: Break down complex queries into smaller parts using variables and helper methods.
7. **Handle Exceptions**: Throw domain-specific exceptions when appropriate.
8. **Choose the Right Tool**: Finder services (e.g., UserFinderService) should be used only for simple queries. For queries that require SQL operations or involve multiple tables, JOOQ should be used.
9. **Don't Compile**: Don't check if JOOQ compiles, contains all imports, or has unresolved references.

## Examples

### Example 1: Simple Query

```kotlin
@Component
class GetUserJooq(private val dslContext: DSLContext) : GetUserPortOut {

    override fun fetch(userId: UUID): GetUserQuery.Result = dslContext
        .select(
            APP_USER.ID,
            APP_USER.CREATED_AT,
            APP_USER.EMAIL,
            APP_USER.ROLE,
            STUDENT.ID,
        )
        .from(APP_USER)
        .leftJoin(STUDENT).on(APP_USER.ID.eq(STUDENT.ID))
        .where(APP_USER.ID.eq(userId))
        .fetchOne()
        ?.map {
            GetUserQuery.Result(
                userId = it[APP_USER.ID]!!,
                createdAt = it[APP_USER.CREATED_AT]!!,
                email = it[APP_USER.EMAIL]!!,
                role = it[APP_USER.ROLE]!!,
                onboardingFinished = it[STUDENT.ID] != null,
            )
        }
        ?: throw UserNotFoundException("User '$userId' not found.")
}

```

### Example 2: Complex with sub queries

```kotlin
@Component
class GetTelegramChatJooq(
	private val dslContext: DSLContext,
	private val clock: Clock,
) : GetTelegramChat {

	override fun fetch(chatId: UUID): GetTelegramChat.Result {
		val now = Instant.now(clock)

		// Channel rank
		val latestChatEvaluations = select(
			TELEGRAM_CHAT_EVALUATION.ID,
		)
			.distinctOn(TELEGRAM_CHAT_EVALUATION.EXTERNAL_CHAT_ID)
			.from(TELEGRAM_CHAT_EVALUATION)
			.orderBy(
				TELEGRAM_CHAT_EVALUATION.EXTERNAL_CHAT_ID,
				TELEGRAM_CHAT_EVALUATION.TOTAL_RETURN.desc(),
			)
			.asTable()

		val rankField = rank().over(orderBy(TELEGRAM_CHAT_EVALUATION.TOTAL_RETURN.desc())).`as`("rank")

		val chatRank = dslContext
			.select(
				TELEGRAM_CHAT_EVALUATION.EXTERNAL_CHAT_ID,
				rankField,
			)
			.from(TELEGRAM_CHAT_EVALUATION)
			.innerJoin(latestChatEvaluations)
			.on(TELEGRAM_CHAT_EVALUATION.ID.eq(latestChatEvaluations.field(TELEGRAM_CHAT_EVALUATION.ID)))
			.asTable()

		// Daily PnL
		val dailyPnLFraction = calculatePnLForRange(
			chatId = chatId,
			from = now.minusSeconds(24 * 60 * 60), // 1 day
			to = now,
		)

		// Hourly PnL
		val hourlyPnLFraction = calculatePnLForRange(
			chatId = chatId,
			from = now.minusSeconds(60 * 60), // 1 hour
			to = now,
		)

		// Root query
		return dslContext
			.select(
				TELEGRAM_CHAT.TITLE,
				TELEGRAM_CHAT.EXTERNAL_ID,
				chatRank.field(rankField),
				dailyPnLFraction,
				hourlyPnLFraction,
			)
			.from(TELEGRAM_CHAT)
			.leftJoin(chatRank)
			.on(TELEGRAM_CHAT.EXTERNAL_ID.eq(chatRank.field(TELEGRAM_CHAT_EVALUATION.EXTERNAL_CHAT_ID)))
			.where(TELEGRAM_CHAT.ID.eq(chatId))
			.fetchOne()
			?.map {
				GetTelegramChat.Result(
					chatName = it[TELEGRAM_CHAT.TITLE]!!,
					chatExternalId = it[TELEGRAM_CHAT.EXTERNAL_ID]!!,
					chatImageUrl = null, // no chat images yet
					chatRank = (it[chatRank.field(rankField)]),
					dailyPositionPnLFraction = it[dailyPnLFraction] ?: BigDecimal.ZERO,
					hourlyPositionPnLFraction = it[hourlyPnLFraction] ?: BigDecimal.ZERO,
				)
			} ?: throw TelegramChatNotFoundException("Telegram chat id=$chatId")
	}

	private fun calculatePnLForRange(
		chatId: UUID,
		from: Instant,
		to: Instant,
	): Field<BigDecimal> {
		val profitPercentage = round(
			avg(
				case_()
					.`when`(PAPER_TRADE.BUY_MARKET_CAP.eq(BigDecimal.ZERO), inline(null, DECIMAL))
					.otherwise(
						PAPER_TRADE.SELL_MARKET_CAP.minus(PAPER_TRADE.BUY_MARKET_CAP)
							.div(PAPER_TRADE.BUY_MARKET_CAP),
					),
			),
			inline(4),
		).`as`("profit_percentage")

		return dslContext
			.select(profitPercentage)
			.from(PAPER_TRADE)
			.innerJoin(CONTENT_EVALUATION).on(PAPER_TRADE.CONTENT_EVALUATION_ID.eq(CONTENT_EVALUATION.ID))
			.innerJoin(TELEGRAM_MESSAGE_EVALUATION)
			.on(CONTENT_EVALUATION.TELEGRAM_MESSAGE_EVALUATION_ID.eq(TELEGRAM_MESSAGE_EVALUATION.ID))
			.innerJoin(TELEGRAM_CHAT).on(TELEGRAM_MESSAGE_EVALUATION.EXTERNAL_CHAT_ID.eq(TELEGRAM_CHAT.EXTERNAL_ID))
			.where(
				PAPER_TRADE.STATE.eq(PaperTradeState.SOLD)
					.and(TELEGRAM_CHAT.ID.eq(chatId))
					.and(PAPER_TRADE.SELL_TIME.ge(from))
					.and(PAPER_TRADE.SELL_TIME.le(to)),
			)
			.groupBy(TELEGRAM_CHAT.EXTERNAL_ID)
			.asField("pnl")
	}
}

```

## Conclusion

Following these JOOQ rules and patterns ensures that database access code is type-safe, maintainable, and consistent across the project. It also facilitates onboarding of new developers by providing clear guidelines for working with JOOQ.
Don't check if JOOQ compiles, contains all imports, or has unresolved references.
