package com.cleevio.fortraders.adapter.output.calendly

import com.cleevio.fortraders.ConnectorTest
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.mockserver.model.HttpRequest
import org.mockserver.model.HttpResponse

class CalendlyConnectorTest : ConnectorTest({
    val underTest = CalendlyConnector(
        baseUrl = baseUrl,
        apiToken = "calendly-api-token-123"
    )

    "should correctly get scheduled event external ID" {
        client.`when`(
            HttpRequest.request()
                .withPath("/scheduled_events/AAAAAAAAAAAAAAAA")
                .withMethod("GET")
        ).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withHeader("Content-Type", "application/json")
                .withBody(
                    """
                    {
                        "resource": {
                            "calendar_event": {
                                "external_id": "external-calendar-event-123"
                            }
                        }
                    }
                    """.trimIndent()
                )
        )

        val result = underTest.getScheduledEventExternalId("AAAAAAAAAAAAAAAA")

        result shouldBe "external-calendar-event-123"

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/scheduled_events/AAAAAAAAAAAAAAAA"
            it.method shouldBe "GET"
            it.containsHeader("Authorization", "Bearer calendly-api-token-123") shouldBe true
        }
    }
})
