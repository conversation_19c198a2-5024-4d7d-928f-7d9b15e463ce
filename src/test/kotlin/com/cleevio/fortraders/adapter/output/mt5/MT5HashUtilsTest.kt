package com.cleevio.fortraders.adapter.output.mt5

import com.cleevio.fortraders.UnitTest
import io.kotest.data.row
import io.kotest.datatest.withData
import io.kotest.matchers.shouldBe

class MT5HashUtilsTest : UnitTest({

    "should correctly has server random answer" - {
        withData(
            nameFn = { it.toString() },
            // example from docs
            row("Password1", "73007dc7184747ce0f7c98516ef1c851", "77fe51827f7fa69dd80fbec9aa33f1bb"),
            row("Password1", "4e8bedee96d34603827a708e28a0ef74", "bc8f56e3e5188c8f2ad598872917598d"),
            row("hunter2", "4f781a5206c3306be7dfe08f19aaa12c", "5e0506c357cf00e6befaa2496e505f05")
        ) { (password, serverRandom, expected) ->
            hashServerRandomAnswer(
                password = password,
                serverRandom = serverRandom
            ) shouldBe expected
        }
    }
})
