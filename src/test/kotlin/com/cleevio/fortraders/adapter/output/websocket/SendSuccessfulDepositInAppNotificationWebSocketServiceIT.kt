package com.cleevio.fortraders.adapter.output.websocket

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.adapter.output.websocket.constant.WebSocketMessageType
import com.cleevio.fortraders.adapter.output.websocket.dto.WebSocketDepositTransactionCompletedResponse
import com.cleevio.fortraders.adapter.output.websocket.dto.WebSocketMessage
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import io.kotest.assertions.throwables.shouldNotThrowAny
import io.mockk.every
import io.mockk.verify

class SendSuccessfulDepositInAppNotificationWebSocketServiceIT(
    private val underTest: SendSuccessfulDepositInAppNotificationWebSocketService,
) : IntegrationTest({

    "should send transaction deposit completed message to websocket" {
        underTest(
            userId = "3fdd49d6-f90d-443d-b7b5-b8f70e523912".toUUID(),
            transactionId = "89769162-2dba-4aa3-bd86-640b4777c498".toUUID(),
            amount = 100.5.toBigDecimal(),
            type = TransactionType.DEPOSIT
        )

        verify {
            simpMessagingTemplateMock.convertAndSendToUser(
                "3fdd49d6-f90d-443d-b7b5-b8f70e523912",
                "/queue/info",
                WebSocketMessage(
                    type = WebSocketMessageType.DEPOSIT_TRANSACTION_COMPLETED,
                    data = WebSocketDepositTransactionCompletedResponse(
                        transactionId = "89769162-2dba-4aa3-bd86-640b4777c498".toUUID(),
                        amount = 100.5.toBigDecimal(),
                        type = TransactionType.DEPOSIT
                    )
                )
            )
        }
    }

    "should not throw when sending fails" {
        every { simpMessagingTemplateMock.convertAndSendToUser(any(), any(), any<String>()) } throws Exception()

        shouldNotThrowAny {
            underTest(
                userId = "3fdd49d6-f90d-443d-b7b5-b8f70e523912".toUUID(),
                transactionId = "89769162-2dba-4aa3-bd86-640b4777c498".toUUID(),
                amount = 100.5.toBigDecimal(),
                type = TransactionType.DEPOSIT
            )
        }

        verify { simpMessagingTemplateMock.convertAndSendToUser(any(), any(), any<String>()) }
    }
})
