package com.cleevio.fortraders.adapter.output.dxtrader

import com.cleevio.fortraders.ConnectorTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.bodyShouldBeJson
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.mockserver.model.HttpRequest
import org.mockserver.model.HttpResponse
import org.mockserver.model.MediaType

class DxTradeAdministrativeConnectorTest : ConnectorTest({
    val underTest = DxTradeAdministrativeConnector(
        baseUrl = baseUrl,
        user = "dx-trade-user",
        password = "dx-trade-password",
        domain = "domain",
        proxyConfig = null
    )

    "should correctly receive all emails" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withBody(GET_REGISTERED_CLIENTS_RESPONSE, MediaType.APPLICATION_JSON)
        )

        val result = underTest.getRegisteredEmails()

        result shouldBe setOf("bro6", "9876")

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/dxweb/rest/api/register/client"
            it.method shouldBe "GET"
            it.assertHeaders()
            it.queryStringParameterList shouldHaveSize 1
            it.queryStringParameterList[0].let { param ->
                param.name.value shouldBe "types"
                param.values shouldHaveSize 1
                param.values[0] shouldBe "CLIENT"
            }
        }
    }

    "should correctly register client" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.registerClient(
            email = "<EMAIL>",
            userFullName = "John Doe",
            password = "password123",
            dxFeedAccountId = "e177649e-d489-442b-93fa-3204778680aa".toUUID()
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/dxweb/rest/api/register/client"
            it.method shouldBe "POST"
            it.assertHeaders()

            it.bodyShouldBeJson(
                """
                {
                    "domain": "domain",
                    "login": "<EMAIL>",
                    "email": "<EMAIL>",
                    "fullName": "John Doe",
                    "password": "password123",
                    "type": "CLIENT",
                    "status": "ONLINE",
                    "dxFeedAccountId": "e177649e-d489-442b-93fa-3204778680aa"
                }
                """.trimIndent()
            )
        }
    }

    "should correctly get groups" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withBody(GET_GROUPS_RESPONSE, MediaType.APPLICATION_JSON)
        )

        val result = underTest.getGroups()

        result shouldBe listOf("100k_funded", "50k_funded", "150k_funded", "100k_challenge", "50k_challenge", "150k_challenge")

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/dxweb/rest/api/register/category"
            it.method shouldBe "GET"
            it.assertHeaders()
        }
    }

    "should correctly create account" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.createAccount(
            accountId = "123456",
            email = "<EMAIL>",
            startingBalance = 100.5.toBigDecimal(),
            tradingGroup = "100k_funded"
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/dxweb/rest/api/register/client/domain/<EMAIL>/account"
            it.method shouldBe "POST"
            it.assertHeaders()
            it.queryStringParameterList shouldHaveSize 1
            it.queryStringParameterList[0].let { param ->
                param.name.value shouldBe "accountAccess"
                param.values shouldHaveSize 1
                param.values[0] shouldBe "TRADE"
            }

            it.bodyShouldBeJson(
                """
                {
                    "clearingCode": "domain",
                    "accountCode": "123456",
                    "status": "FULL_TRADING",
                    "accountCashType": "MARGIN",
                    "accountType": "LIVE",
                    "currency": "USD",
                    "balance": 100.5,
                    "categories": [{
                        "category": "DxBroRiskControl",
                        "value": "100k_funded"
                    }]
                }
                """.trimIndent()
            )
        }
    }

    "should correctly deposit" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.deposit(
            accountId = "123456",
            amount = 100.5.toBigDecimal(),
            comment = "Deposit",
            transferId = "7c149e7a-98cf-4d73-82be-c84871420221".toUUID()
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/dxweb/rest/api/register/account/domain/123456/deposit"
            it.method shouldBe "POST"
            it.assertHeaders()

            it.bodyShouldBeJson(
                """
                {
                    "amount": 100.5,
                    "currency": "USD",
                    "description": "Deposit",
                    "transferId": "7c149e7a-98cf-4d73-82be-c84871420221"
                }
                """.trimIndent()
            )
        }
    }

    "should correctly withdraw" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.withdraw(
            accountId = "123456",
            amount = 100.5.toBigDecimal(),
            comment = "Withdraw",
            transferId = "7c149e7a-98cf-4d73-82be-c84871420221".toUUID()
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/dxweb/rest/api/register/account/domain/123456/withdrawal"
            it.method shouldBe "POST"
            it.assertHeaders()

            it.bodyShouldBeJson(
                """
                {
                    "amount": 100.5,
                    "currency": "USD",
                    "description": "Withdraw",
                    "transferId": "7c149e7a-98cf-4d73-82be-c84871420221"
                }
                """.trimIndent()
            )
        }
    }

    "should correctly restrict account" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.restrictAccount(accountId = "123456")

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/dxweb/rest/api/register/account/domain/123456"
            it.method shouldBe "PUT"
            it.assertHeaders()

            it.bodyShouldBeJson(
                """
                {
                    "status": "CLOSE_ONLY"
                }
                """.trimIndent()
            )
        }
    }

    "should correctly activate account" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.activateAccount(accountId = "123456")

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/dxweb/rest/api/register/account/domain/123456"
            it.method shouldBe "PUT"
            it.assertHeaders()

            it.bodyShouldBeJson(
                """
                {
                    "status": "FULL_TRADING"
                }
                """.trimIndent()
            )
        }
    }
})

private fun HttpRequest.assertHeaders() {
    containsHeader("Accept", "application/json") shouldBe true
    // dx-trade-user@domain:dx-trade-password
    containsHeader("Authorization", "Basic ZHgtdHJhZGUtdXNlckBkb21haW46ZHgtdHJhZGUtcGFzc3dvcmQ=") shouldBe true
}

private const val GET_REGISTERED_CLIENTS_RESPONSE = """
[
    {
        "domain": "default",
        "login": "bro6",
        "accounts": [],
        "email": "<EMAIL>",
        "brokerCode": "root_broker",
        "type": "CLIENT",
        "status": "OFFLINE",
        "passwordExpiry": 0,
        "passwordReset": false,
        "createdDateTime": "2024-09-04T21:21:39.625Z",
        "groups": []
    },
    {
        "domain": "default",
        "login": "9876",
        "accounts": [],
        "fullName": "Adam",
        "email": "<EMAIL>",
        "brokerCode": "root_broker",
        "type": "CLIENT",
        "status": "OFFLINE",
        "passwordExpiry": 0,
        "passwordReset": false,
        "createdDateTime": "2024-10-01T13:50:53.404Z",
        "groups": []
    }
]
"""
private const val GET_GROUPS_RESPONSE = """
[
    {
        "category": "DxBroRiskControl",
        "values": [
            {
                "value": "100k_funded"
            },
            {
                "value": "50k_funded"
            },
            {
                "value": "150k_funded"
            },
            {
                "value": "100k_challenge"
            },
            {
                "value": "50k_challenge"
            },
            {
                "value": "150k_challenge"
            }
        ]
    },
    {
        "category": "RankedGroupCommissions",
        "values": [
            {
                "value": "ROOT"
            }
        ]
    }
]
"""
