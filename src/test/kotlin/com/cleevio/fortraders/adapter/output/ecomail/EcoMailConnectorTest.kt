package com.cleevio.fortraders.adapter.output.ecomail

import com.cleevio.fortraders.ConnectorTest
import com.cleevio.fortraders.application.module.marketing.port.output.CreateOrderInMarketingPlatform
import com.cleevio.fortraders.bodyShouldBeJson
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.mockserver.model.HttpRequest
import org.mockserver.model.HttpResponse
import java.time.Instant

class EcoMailConnectorTest : ConnectorTest({
    val underTest = EcoMailConnector(
        baseUrl = baseUrl,
        apiKey = "ecomail-api-key",
        mainListId = 10,
        waitingListId = 11
    )

    "should correctly create or update list member" {
        client.`when`(
            HttpRequest.request()
        ).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.createOrUpdateListMember(
            email = "<EMAIL>",
            firstName = "John",
            lastName = "Doe",
            streetAddress = "street 1",
            city = "City",
            countryIsoCode = "CZ",
            postCode = "12345",
            phoneNumber = "123456789",
            numberOfActiveChallenges = 10,
            userOrdersRevenueTotal = 100.5.toBigDecimal(),
            paidOrdersCount = 1,
            firstPaidOrderCreatedAt = null,
            latestPaidOrderCreatedAt = Instant.parse("2024-01-01T23:55:22.134Z"),
            unpaidOrdersCount = 2,
            firstUnpaidOrderCreatedAt = Instant.parse("2025-01-01T23:55:22.134Z"),
            latestUnpaidOrderCreatedAt = null
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/lists/10/subscribe"
            it.method shouldBe "POST"
            it.containsHeader("key", "ecomail-api-key") shouldBe true

            it.bodyShouldBeJson(
                """
                {
                    "subscriber_data": {
                        "name": "John",
                        "surname": "Doe",
                        "email": "<EMAIL>",
                        "city": "City",
                        "street": "street 1",
                        "zip": "12345",
                        "country": "CZ",
                        "phone": "123456789",
                        "custom_fields": {
                            "paid_orders_count": {
                                "type": "int",
                                "value": 1
                            },
                            "paid_orders_sum": {
                                "type": "int",
                                "value": 100
                            },
                            "first_paid_order_date": {
                                "type": "date",
                                "value": null
                            },
                            "latest_paid_order_date": {
                                "type": "date",
                                "value": "2024-01-01 23:55"
                            },
                            "unpaid_orders_count": {
                                "type": "int",
                                "value": 2
                            },
                            "first_unpaid_order_date": {
                                "type": "date",
                                "value": "2025-01-01 23:55"
                            },
                            "latest_unpaid_order_date": {
                                "type": "date",
                                "value": null
                            },
                            "number_of_active_challenges": {
                                "type": "int",
                                "value": 10
                            }
                        },
                        "source": "API"
                    },
                    "update_existing": true
                }
                """.trimIndent()
            )
        }
    }

    "should correctly create waiting list member" {
        client.`when`(
            HttpRequest.request()
        ).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.createWaitingListMember(
            email = "<EMAIL>",
            countryIsoCode = "CZ"
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/lists/11/subscribe"
            it.method shouldBe "POST"
            it.containsHeader("key", "ecomail-api-key") shouldBe true

            it.bodyShouldBeJson(
                """
                {
                    "subscriber_data": {
                        "email": "<EMAIL>",
                        "country": "CZ",
                        "source": "API"
                    },
                    "update_existing": false
                }
                """.trimIndent()
            )
        }
    }
    "should correctly create or update transaction" {
        client.`when`(
            HttpRequest.request()
        ).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        val orderItems = listOf(
            CreateOrderInMarketingPlatform.OrderItem(
                code = "item-123",
                title = "Test Product",
                category = "Test Category",
                price = 50.0.toBigDecimal(),
                amount = 2
            ),
            CreateOrderInMarketingPlatform.OrderItem(
                code = "item-456",
                title = "Another Product",
                category = "Another Category",
                price = 25.5.toBigDecimal(),
                amount = 1
            )
        )

        val timestamp = Instant.parse("2024-02-15T10:30:00Z")

        underTest.createOrUpdateTransaction(
            orderId = "order-123",
            email = "<EMAIL>",
            shop = "test-shop",
            amount = 125.5.toBigDecimal(),
            tax = 20.0.toBigDecimal(),
            shipping = 5.0.toBigDecimal(),
            city = "Test City",
            county = "Test County",
            country = "CZ",
            timestamp = timestamp,
            status = "completed",
            items = orderItems
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/tracker/transaction"
            it.method shouldBe "POST"
            it.containsHeader("key", "ecomail-api-key") shouldBe true

            it.bodyShouldBeJson(
                """
                {
                    "transaction": {
                        "order_id": "order-123",
                        "email": "<EMAIL>",
                        "shop": "test-shop",
                        "amount": 125,
                        "tax": 20,
                        "shipping": 5,
                        "city": "Test City",
                        "county": "Test County",
                        "country": "CZ",
                        "timestamp": ${timestamp.epochSecond},
                        "status": "completed"
                    },
                    "transaction_items": [
                        {
                            "code": "item-123",
                            "title": "Test Product",
                            "category": "Test Category",
                            "price": 50,
                            "amount": 2
                        },
                        {
                            "code": "item-456",
                            "title": "Another Product",
                            "category": "Another Category",
                            "price": 25,
                            "amount": 1
                        }
                    ]
                }
                """.trimIndent()
            )
        }
    }
})
