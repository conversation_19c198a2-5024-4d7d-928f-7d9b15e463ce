package com.cleevio.fortraders.adapter.output.ctrader

import com.cleevio.fortraders.ConnectorTest
import com.cleevio.fortraders.bodyShouldBeJson
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.mockserver.model.HttpRequest
import org.mockserver.model.HttpResponse
import org.mockserver.model.MediaType
import java.time.Instant

class CTraderConnectorTest : ConnectorTest({
    val underTest = CTraderConnector(
        baseUrl = baseUrl,
        accessToken = "access-token",
        environment = "demo",
        brokerName = "broker-name",
        proxyConfig = null
    )

    "should correctly create user" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withBody(CREATE_USER_RESPONSE, MediaType.APPLICATION_JSON)
        )

        val result = underTest.createUser(
            email = "<EMAIL>"
        )
        result shouldBe "9064592"

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/cid/ctid/create"
            it.method shouldBe "POST"
            it.assertHeadersAndQueryParams()
            it.containsHeader("Content-Type", "application/json") shouldBe true
            it.bodyShouldBeJson(
                """
                {
                    "email": "<EMAIL>"
                }
                """
            )
        }
    }

    "should correctly create account" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withBody(CREATE_ACCOUNT_RESPONSE, MediaType.APPLICATION_JSON)
        )

        val result = underTest.createAccount(
            name = "Adam Masaryk",
            password = "password",
            leverage = 125,
            tradingGroup = "cleevio",
            startingBalance = 1000.5.toBigDecimal()
        )
        result shouldBe "4249100"

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/webserv/traders"
            it.method shouldBe "POST"
            it.assertHeadersAndQueryParams()
            it.containsHeader("Content-Type", "application/json") shouldBe true

            it.bodyShouldBeJson(
                """
                {
                    "name": "Adam Masaryk",
                    "accessRights": "FULL_ACCESS",
                    "accountType": "HEDGED",
                    "balance": 100050,
                    "brokerName": "broker-name",
                    "depositCurrency": "USD",
                    "groupName": "cleevio",
                    "hashedPassword": "5f4dcc3b5aa765d61d8327deb882cf99",
                    "leverageInCents": 12500,
                    "totalMarginCalculationType": "MAX",
                    "swapFree": false
                }
                """
            )
        }
    }

    "should correctly link account to user" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.linkAccountToCTID(
            ctID = "4249100",
            accountId = "*********",
            password = "password"
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/cid/ctid/link"
            it.method shouldBe "POST"
            it.assertHeadersAndQueryParams()
            it.containsHeader("Content-Type", "application/json") shouldBe true

            it.bodyShouldBeJson(
                """
                {
                    "traderLogin": "*********",
                    "traderPasswordHash": "5f4dcc3b5aa765d61d8327deb882cf99",
                    "userId": "4249100",
                    "brokerName": "broker-name",
                    "environmentName": "demo"
                }
                """
            )
        }
    }

    "should correctly restrict account" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.restrictAccount(
            accountId = "*********"
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/webserv/traders/*********"
            it.method shouldBe "PATCH"
            it.assertHeadersAndQueryParams()
            it.containsHeader("Content-Type", "application/json") shouldBe true

            it.bodyShouldBeJson(
                """
                {
                    "accessRights": "CLOSE_ONLY"
                }
                """
            )
        }
    }

    "should correctly activate account" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.activateAccount(
            accountId = "*********"
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/webserv/traders/*********"
            it.method shouldBe "PATCH"
            it.assertHeadersAndQueryParams()
            it.containsHeader("Content-Type", "application/json") shouldBe true

            it.bodyShouldBeJson(
                """
                {
                    "accessRights": "FULL_ACCESS"
                }
                """
            )
        }
    }

    "should correctly update account group" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.updateTradingGroup(
            accountId = "*********",
            tradingGroup = "new-group"
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/webserv/traders/*********"
            it.method shouldBe "PATCH"
            it.assertHeadersAndQueryParams()
            it.containsHeader("Content-Type", "application/json") shouldBe true

            it.bodyShouldBeJson(
                """
                {
                    "groupName": "new-group"
                }
                """
            )
        }
    }

    "should correctly get account reports" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withBody(GET_ACCOUNT_REPORTS_RESPONSE, MediaType.APPLICATION_JSON)
        )

        val result = underTest.getAccountReports(
            accountIds = setOf("4249153")
        )
        result shouldHaveSize 1
        result[0].let {
            it.accountId shouldBe "4249153"
            it.balance shouldBeEqualComparingTo 25000.00.toBigDecimal()
            it.equity shouldBeEqualComparingTo 24995.62.toBigDecimal()
            it.createdAt shouldBe Instant.parse("2025-01-20T11:42:04.767Z")
        }

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/webserv/traders/"
            it.method shouldBe "GET"
            it.assertHeadersAndQueryParams()

            it.queryStringParameterList.firstOrNull { queryParam -> queryParam.name.value == "from" }.let { from ->
                from.shouldNotBeNull()
                from.values shouldHaveSize 1
                from.values[0] shouldBe "1970-01-01T00:00:00.000Z"
            }
            it.queryStringParameterList.firstOrNull { queryParam -> queryParam.name.value == "to" }.let { to ->
                to.shouldNotBeNull()
                to.values shouldHaveSize 1
                to.values[0] shouldBe "2099-12-31T23:59:59.999Z"
            }
            it.queryStringParameterList.firstOrNull { queryParam -> queryParam.name.value == "fields" }.let { fields ->
                fields.shouldNotBeNull()
                fields.values shouldHaveSize 1
                fields.values[0] shouldBe "login,balance,equity,registrationTimestamp"
            }
        }
    }

    "should correctly get groups" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withBody(GET_GROUPS_RESPONSE, MediaType.APPLICATION_JSON)
        )

        val result = underTest.getGroups()
        result shouldContainExactlyInAnyOrder listOf(
            "BILLCT01",
            "BILLCT02_s",
            "BILLCT03_s",
            "BILLCT04_s"
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/webserv/tradergroups"
            it.method shouldBe "GET"
            it.assertHeadersAndQueryParams()
        }
    }

    "should correctly get all open positions" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withBody(GET_OPEN_POSITIONS_RESPONSE, MediaType.TEXT_PLAIN)
        )

        val result = underTest.getNumberOfOpenPositionsByAccountIds(
            accountIds = setOf("4241549", "4240300")
        )
        result shouldBe mapOf(
            "4241549" to 1,
            "4240300" to 1
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/webserv/openPositions"
            it.method shouldBe "GET"
            it.assertHeadersAndQueryParams()
        }
    }

    "should correctly get open positions" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withBody(GET_OPEN_POSITIONS_RESPONSE, MediaType.TEXT_PLAIN)
        )

        val result = underTest.getOpenPositionsByAccountId(
            accountId = "4241549"
        )
        result shouldHaveSize 4

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/webserv/openPositions"
            it.method shouldBe "GET"
            it.assertHeadersAndQueryParams()

            it.queryStringParameterList.firstOrNull { queryParam -> queryParam.name.value == "login" }.let { login ->
                login.shouldNotBeNull()
                login.values shouldHaveSize 1
                login.values[0] shouldBe "4241549"
            }
        }
    }

    "should correctly deposit account" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.deposit(
            accountId = "*********",
            amount = 100.5.toBigDecimal(),
            comment = "Deposit"
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/webserv/traders/*********/changebalance"
            it.method shouldBe "POST"
            it.assertHeadersAndQueryParams()
            it.containsHeader("Content-Type", "application/json") shouldBe true

            it.bodyShouldBeJson(
                """
                {
                    "login": "*********",
                    "preciseAmount": 100.5,
                    "type": "DEPOSIT",
                    "externalNote": "Deposit"
                }
                """
            )
        }
    }

    "should correctly withdraw account" {
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        underTest.withdraw(
            accountId = "*********",
            amount = 100.5.toBigDecimal(),
            comment = "Withdraw"
        )

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/v2/webserv/traders/*********/changebalance"
            it.method shouldBe "POST"
            it.assertHeadersAndQueryParams()
            it.containsHeader("Content-Type", "application/json") shouldBe true

            it.bodyShouldBeJson(
                """
                {
                    "login": "*********",
                    "preciseAmount": 100.5,
                    "type": "WITHDRAW",
                    "externalNote": "Withdraw"
                }
                """
            )
        }
    }
})

private fun HttpRequest.assertHeadersAndQueryParams() {
    containsHeader("Accept", "application/json") shouldBe true
    queryStringParameterList.firstOrNull { it.name.value == "token" }.let { token ->
        token.shouldNotBeNull()
        token.values shouldHaveSize 1
        token.values[0] shouldBe "access-token"
    }
}

private const val CREATE_USER_RESPONSE = """
{
    "userId": 9064592
}
"""
private const val CREATE_ACCOUNT_RESPONSE = """
{
    "login": 4249100
}
"""
private const val GET_ACCOUNT_REPORTS_RESPONSE = """
{
    "trader": [
        {
            "login": 4249153,
            "balance": 2500000,
            "registrationTimestamp": *************,
            "equity": 2499562
        },
        {
            "login": 4249152,
            "balance": 10000,
            "registrationTimestamp": *************,
            "equity": 10000
        }
    ],
    "hasMore": false
}
"""
private const val GET_GROUPS_RESPONSE = """
{
    "traderGroup": [
        {
            "id": 1176,
            "name": "BILLCT01",
            "description": "Billions Club (Cleevio)"
        },
        {
            "id": 1177,
            "name": "BILLCT02_s",
            "description": "Billions Club (Cleevio)"
        },
        {
            "id": 1178,
            "name": "BILLCT03_s",
            "description": "Billions Club (Cleevio)"
        },
        {
            "id": 1452,
            "name": "BILLCT04_s",
            "description": "Billions Club (Cleevio)"
        }
    ]
}
"""
private const val GET_OPEN_POSITIONS_RESPONSE =
    """login,positionId,openTimestamp,entryPrice,direction,volume,symbol,commission,swap,bookType,stake,spreadBetting,usedMargin
4241549,13435073,2025-01-20T05:32:05.677,1.96536,BUY,120000.00,GBPAUD,1.80,-1.28,BOOK_B,0.00,false,1171.92
4240300,13500647,2025-01-21T11:00:40.274,2719.31,SELL,50.00,XAUUSD,0.75,0.00,BOOK_A,0.00,false,13596.55
4241951,13435053,2025-01-20T05:28:53.920,0.94067,BUY,120000.00,EURCHF,1.80,6.75,BOOK_B,0.00,false,989.05
4241951,13435054,2025-01-20T05:28:56.408,1.48877,BUY,120000.00,EURCAD,1.80,-3.24,BOOK_B,0.00,false,989.05"""
