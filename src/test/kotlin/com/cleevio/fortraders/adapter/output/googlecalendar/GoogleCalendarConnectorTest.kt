package com.cleevio.fortraders.adapter.output.googlecalendar

import com.cleevio.fortraders.UnitTest
import com.google.api.services.calendar.Calendar
import com.google.api.services.calendar.model.ConferenceData
import com.google.api.services.calendar.model.EntryPoint
import com.google.api.services.calendar.model.Event
import com.google.api.services.calendar.model.EventAttachment
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class GoogleCalendarConnectorTest : UnitTest({

    val mockCalendarService = mockk<Calendar>()
    val mockEvents = mockk<Calendar.Events>()
    val mockGet = mockk<Calendar.Events.Get>()
    val underTest = GoogleCalendarConnector(mockCalendarService)

    beforeTest {
        every { mockCalendarService.events() } returns mockEvents
        every { mockEvents.get("primary", any()) } returns mockGet
        every { mockGet.setFields("id,conferenceData,hangoutLink,attachments") } returns mockGet
    }

    "should return recording URL from conference data video entry point" {
        val mockEvent = mockk<Event>()
        val mockConferenceData = mockk<ConferenceData>()
        val mockEntryPoint = mockk<EntryPoint>()

        every { mockGet.execute() } returns mockEvent
        every { mockEvent.conferenceData } returns mockConferenceData
        every { mockConferenceData.entryPoints } returns listOf(mockEntryPoint)
        every { mockEntryPoint.entryPointType } returns "video"
        every { mockEntryPoint.uri } returns "https://zoom.us/j/123456789"
        every { mockEvent.hangoutLink } returns null
        every { mockEvent.attachments } returns null

        val result = underTest.getEventRecording("external-event-123")

        result shouldBe "https://zoom.us/j/123456789"

        verify {
            mockEvents.get("primary", "external-event-123")
            mockGet.fields = "id,conferenceData,hangoutLink,attachments"
            mockGet.execute()
        }
    }

    "should return hangout link when no conference data video entry point" {
        val mockEvent = mockk<Event>()
        val mockConferenceData = mockk<ConferenceData>()
        val mockEntryPoint = mockk<EntryPoint>()

        every { mockGet.execute() } returns mockEvent
        every { mockEvent.conferenceData } returns mockConferenceData
        every { mockConferenceData.entryPoints } returns listOf(mockEntryPoint)
        every { mockEntryPoint.entryPointType } returns "phone"
        every { mockEvent.hangoutLink } returns "https://meet.google.com/xyz-abc-def"
        every { mockEvent.attachments } returns null

        val result = underTest.getEventRecording("external-event-456")

        result shouldBe "https://meet.google.com/xyz-abc-def"
    }

    "should return attachment file URL when no conference data or hangout link" {
        val mockEvent = mockk<Event>()
        val mockAttachment = mockk<EventAttachment>()

        every { mockGet.execute() } returns mockEvent
        every { mockEvent.conferenceData } returns null
        every { mockEvent.hangoutLink } returns null
        every { mockEvent.attachments } returns listOf(mockAttachment)
        every { mockAttachment.mimeType } returns "video/mp4"
        every { mockAttachment.fileUrl } returns "https://drive.google.com/file/d/recording123"

        val result = underTest.getEventRecording("external-event-789")

        result shouldBe "https://drive.google.com/file/d/recording123"
    }

    "should return null when no recording sources available" {
        val mockEvent = mockk<Event>()

        every { mockGet.execute() } returns mockEvent
        every { mockEvent.conferenceData } returns null
        every { mockEvent.hangoutLink } returns null
        every { mockEvent.attachments } returns null

        val result = underTest.getEventRecording("external-event-empty")

        result shouldBe null
    }
})
