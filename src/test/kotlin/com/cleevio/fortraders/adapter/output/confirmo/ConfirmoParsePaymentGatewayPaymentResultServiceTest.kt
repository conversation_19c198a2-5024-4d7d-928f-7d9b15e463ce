package com.cleevio.fortraders.adapter.output.confirmo

import com.cleevio.fortraders.UnitTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import io.kotest.data.row
import io.kotest.datatest.withData
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe

class ConfirmoParsePaymentGatewayPaymentResultServiceTest : UnitTest({

    val underTest = ConfirmoParsePaymentGatewayPaymentResultService()

    "should correctly parse payment result" - {
        withData(
            row("prepared", TransactionStatus.REQUESTED),
            row("active", TransactionStatus.PROCESSING),
            row("expired", TransactionStatus.CANCELLED),
            row("confirming", TransactionStatus.PROCESSING),
            row("error", TransactionStatus.ERROR),
            row("paid", TransactionStatus.COMPLETED)
        ) { (result, expectedStatus) ->
            underTest(
                paymentReference = "9324eda5-ca63-4231-a75b-3a68c5caffd9",
                result = result
            ).let {
                it.shouldNotBeNull()
                it.transactionId shouldBe "9324eda5-ca63-4231-a75b-3a68c5caffd9".toUUID()
                it.transactionStatus shouldBe expectedStatus
            }
        }
    }

    "should return null for unknown payment result" {
        underTest(
            paymentReference = "9324eda5-ca63-4231-a75b-3a68c5caffd9",
            result = "unknown"
        ).shouldBeNull()
    }
})
