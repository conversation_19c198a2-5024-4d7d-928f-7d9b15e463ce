package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.trade.query.AdminGetCopyTradesQuery
import com.cleevio.fortraders.jsonContent
import io.mockk.every
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.web.servlet.get
import java.time.Instant

@WebMvcTest(AdminTradesController::class)
class AdminTradesControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns admin.jwt
    }

    "should correctly access admin get copy trades endpoint" {
        every { queryBus(any<AdminGetCopyTradesQuery>()) } returns listOf(
            AdminGetCopyTradesQuery.Result(
                copier = AdminGetCopyTradesQuery.CopyTradeUserDetail(
                    tradingAccountId = "98dfe949-debd-447f-b33e-679bd9d31b72".toUUID(),
                    accountId = "1",
                    startingBalance = 1000.toBigDecimal(),
                    email = "<EMAIL>"
                ),
                copiedFrom = AdminGetCopyTradesQuery.CopyTradeUserDetail(
                    tradingAccountId = "0e74d303-1f74-45cf-9e2c-60503c9fac9d".toUUID(),
                    accountId = "2",
                    startingBalance = 2000.toBigDecimal(),
                    email = "<EMAIL>"
                ),
                copiedTrades = listOf(
                    AdminGetCopyTradesQuery.CopyTradeDetail(
                        symbol = "BTC/USD",
                        copierTradeId = "98dfe949-debd-447f-b33e-679bd9d31b72".toUUID(),
                        sourceTradeId = "0e74d303-1f74-45cf-9e2c-60503c9fac9d".toUUID(),
                        copierVolume = 100.toBigDecimal(),
                        sourceVolume = 200.toBigDecimal(),
                        copierOpenPrice = 1000.toBigDecimal(),
                        sourceOpenPrice = 2000.toBigDecimal(),
                        copierClosePrice = 3000.toBigDecimal(),
                        sourceClosePrice = 4000.toBigDecimal(),
                        copierOpenTradeTime = Instant.parse("2021-01-01T00:00:00Z"),
                        copierCloseTradeTime = Instant.parse("2021-02-02T00:00:00Z"),
                        sourceOpenTradeTime = Instant.parse("2021-03-03T00:00:00Z"),
                        sourceCloseTradeTime = Instant.parse("2021-04-04T00:00:00Z"),
                        openTimeDiffSeconds = 1,
                        closeTimeDiffSeconds = 2,
                        volumeDiff = 101.toBigDecimal(),
                        openPriceDiffPct = 5.toBigDecimal(),
                        closePriceDiffPct = 6.toBigDecimal()
                    )
                )
            )
        )

        mockMvc.get("/admin-app/trades/copy-trades") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                [{
                    "copier": {
                        "tradingAccountId": "98dfe949-debd-447f-b33e-679bd9d31b72",
                        "accountId": "1",
                        "startingBalance": 1000,
                        "email": "<EMAIL>"
                    },
                    "copiedFrom": {
                        "tradingAccountId": "0e74d303-1f74-45cf-9e2c-60503c9fac9d",
                        "accountId": "2",
                        "startingBalance": 2000,
                        "email": "<EMAIL>"
                    },
                    "copiedTrades": [{
                        "symbol": "BTC/USD",
                        "copierTradeId": "98dfe949-debd-447f-b33e-679bd9d31b72",
                        "sourceTradeId": "0e74d303-1f74-45cf-9e2c-60503c9fac9d",
                        "copierVolume": 100,
                        "sourceVolume": 200,
                        "copierOpenPrice": 1000,
                        "sourceOpenPrice": 2000,
                        "copierClosePrice": 3000,
                        "sourceClosePrice": 4000,
                        "copierOpenTradeTime": "2021-01-01T00:00:00Z",
                        "copierCloseTradeTime": "2021-02-02T00:00:00Z",
                        "sourceOpenTradeTime": "2021-03-03T00:00:00Z",
                        "sourceCloseTradeTime": "2021-04-04T00:00:00Z",
                        "openTimeDiffSeconds": 1,
                        "closeTimeDiffSeconds": 2,
                        "volumeDiff": 101,
                        "openPriceDiffPct": 5,
                        "closePriceDiffPct": 6
                    }]
                }]
                """.trimIndent()
            )
        }

        verify {
            queryBus(any<AdminGetCopyTradesQuery>())
            jwtDecoder.decode(admin.accessToken)
        }
    }
})
