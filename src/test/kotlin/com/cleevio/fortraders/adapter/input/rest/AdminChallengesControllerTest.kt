package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.addJsonContent
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.challenge.command.AdminCreateChallengeCommand
import com.cleevio.fortraders.application.module.challenge.command.AdminPatchChallengeCommand
import com.cleevio.fortraders.application.module.challenge.query.AdminGetChallengeQuery
import com.cleevio.fortraders.application.module.challenge.query.AdminSearchChallengesQuery
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeState
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanCategory
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanState
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepDailyDrawdownType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepDailyPauseType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepMaxDrawdownType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.ChallengeStepSettingType
import com.cleevio.fortraders.domain.model.challengestepsettingmovement.constant.ChallengeStepSettingMovementType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.jsonContent
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.patch
import org.springframework.test.web.servlet.post
import java.time.Instant

@WebMvcTest(AdminChallengesController::class)
class AdminChallengesControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns admin.jwt
    }

    "should correctly access search challenges EP" {
        every { queryBus(any<AdminSearchChallengesQuery>()) } returns PageImpl(
            listOf(
                AdminSearchChallengesQuery.Result(
                    id = "db22f458-033d-42f6-bcbb-74cc4be53e79".toUUID(),
                    name = "Mission impossible",
                    startingBalance = 150.5.toBigDecimal(),
                    state = ChallengeState.DISABLED,
                    type = ChallengeType.FOREX,
                    createdAt = Instant.parse("2022-12-24T18:15:30Z"),
                    updatedAt = Instant.parse("2023-11-19T15:18:39Z"),
                    plans = listOf(
                        AdminSearchChallengesQuery.ChallengePlanDetail(
                            id = "88865c3b-65a2-4791-a24a-8e78145f8a3b".toUUID(),
                            category = ChallengePlanCategory.PRO,
                            title = "Plan 1",
                            steps = 2
                        )
                    )
                )
            ),
            PageRequest.of(1, 10),
            2
        )

        mockMvc.post("/admin-app/challenges/search?page=1&size=10") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "challengeStates": ["DISABLED", "ENABLED"],
                    "challengeTypes": ["FOREX"],
                    "challengePlanStates": ["DRAFT", "PUBLISHED"],
                    "createdAtFrom": "2019-12-24T18:15:30Z",
                    "createdAtTo": "2020-12-24T19:18:20Z"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                	"content": [{
                		"id": "db22f458-033d-42f6-bcbb-74cc4be53e79",
                        "name": "Mission impossible",
                		"startingBalance": 150.5,
                		"state": "DISABLED",
                        "type": "FOREX",
                		"createdAt": "2022-12-24T18:15:30Z",
                		"updatedAt": "2023-11-19T15:18:39Z",
                        "plans": [{
                            "id": "88865c3b-65a2-4791-a24a-8e78145f8a3b",
                            "category": "PRO",
                            "title": "Plan 1",
                            "steps": 2
                        }]
                	}],
                	"currentPage": 1,
                	"pageSize": 10,
                	"totalElements": 11,
                	"totalPages": 2
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                AdminSearchChallengesQuery(
                    PageRequest.of(1, 10),
                    AdminSearchChallengesQuery.Filter(
                        challengeStates = setOf(ChallengeState.DISABLED, ChallengeState.ENABLED),
                        challengeTypes = setOf(ChallengeType.FOREX),
                        challengePlanStates = setOf(ChallengePlanState.DRAFT, ChallengePlanState.PUBLISHED),
                        createdAtFrom = Instant.parse("2019-12-24T18:15:30Z"),
                        createdAtTo = Instant.parse("2020-12-24T19:18:20Z")
                    )
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access get challenge EP" {
        every { queryBus(any<AdminGetChallengeQuery>()) } returns AdminGetChallengeQuery.Result(
            id = "f14777fa-c31a-4bdc-9129-94a50741e77c".toUUID(),
            name = "Mission impossible",
            startingBalance = 100000L.toBigDecimal(),
            state = ChallengeState.ENABLED,
            type = ChallengeType.FOREX,
            createdAt = Instant.parse("2022-12-24T18:15:30Z"),
            updatedAt = Instant.parse("2022-12-25T11:12:22Z"),
            plans = listOf(
                AdminGetChallengeQuery.AdminChallengePlanResult(
                    id = "4d9c50d5-2590-4f11-b52d-6442e6578ab3".toUUID(),
                    category = ChallengePlanCategory.PRO,
                    stepCount = 3,
                    externalProductId = 123,
                    state = ChallengePlanState.DRAFT,
                    title = "One-Step",
                    basePrice = 299L.toBigDecimal(),
                    isSubscription = true,
                    platforms = listOf(PlatformType.META_TRADER_5),
                    capTrailingDrawdownAfterPayout = true,
                    minimumPayoutLimit = 68.9.toBigDecimal(),
                    maximumPayoutLimit = 212.5.toBigDecimal(),
                    createdAt = Instant.parse("2022-09-11T02:17:30Z"),
                    updatedAt = Instant.parse("2022-09-11T02:17:44Z"),
                    steps = listOf(
                        AdminGetChallengeQuery.AdminChallengeStepResult(
                            id = "b14ffa89-37b0-4a2c-b9f5-45583be183d8".toUUID(),
                            number = 1,
                            type = ChallengeStepType.FUNDED,
                            maxDrawdownType = ChallengeStepMaxDrawdownType.TRAILING,
                            dailyDrawdownType = ChallengeStepDailyDrawdownType.EQUITY_OR_BALANCE_BASED,
                            dailyPauseType = ChallengeStepDailyPauseType.CLOSE_ONLY,
                            leverage = 100,
                            inactivityPeriodDays = 5,
                            minProfitableTradingDays = 3,
                            maxTradingDays = 7,
                            groupMt5 = "group MT 5",
                            groupTradeLocker = "group Trade Locker",
                            groupCTrader = "group CTrader",
                            groupDxTrade = "group DxTrade",
                            consistencyTarget = 50,
                            createdAt = Instant.parse("2022-09-11T23:09:59Z"),
                            updatedAt = Instant.parse("2023-01-02T16:15:01Z"),
                            settings = listOf(
                                AdminGetChallengeQuery.AdminChallengeStepSettingResult(
                                    id = "00181783-28a6-468d-8ee7-bad3b94383f2".toUUID(),
                                    type = ChallengeStepSettingType.PROFIT_TARGET,
                                    useMovementsFromOrder = false,
                                    isVisibleInConfigurator = true,
                                    movements = listOf(
                                        AdminGetChallengeQuery.AdminChallengeStepSettingMovementResult(
                                            id = "5d9656f7-ad9e-4238-be5b-f46e42a01a80".toUUID(),
                                            type = ChallengeStepSettingMovementType.PERCENTAGE,
                                            percentageValue = 7,
                                            listValue = null,
                                            movementPercentageValue = 3,
                                            movementAbsoluteValue = 15.3.toBigDecimal(),
                                            isDefault = false
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
        )

        mockMvc.get("/admin-app/challenges/b67d8028-c088-488b-9fd4-bf8f8077e6b2") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "id": "f14777fa-c31a-4bdc-9129-94a50741e77c",
                    "name": "Mission impossible",
                    "startingBalance": 100000,
                    "state": "ENABLED",
                    "type": "FOREX",
                    "createdAt": "2022-12-24T18:15:30Z",
                    "updatedAt": "2022-12-25T11:12:22Z",
                    "plans": [
                        {
                            "id": "4d9c50d5-2590-4f11-b52d-6442e6578ab3",
                            "category": "PRO",
                            "stepCount": 3,
                            "externalProductId": 123,
                            "state": "DRAFT",
                            "title": "One-Step",
                            "basePrice": 299,
                            "isSubscription": true,
                            "platforms": [
                                "META_TRADER_5"
                            ],
                            "capTrailingDrawdownAfterPayout": true,
                            "minimumPayoutLimit": 68.9,
                            "maximumPayoutLimit": 212.5,
                            "createdAt": "2022-09-11T02:17:30Z",
                            "updatedAt": "2022-09-11T02:17:44Z",
                            "steps": [
                                {
                                    "id": "b14ffa89-37b0-4a2c-b9f5-45583be183d8",
                                    "number": 1,
                                    "type": "FUNDED",
                                    "maxDrawdownType": "TRAILING",
                                    "dailyDrawdownType": "EQUITY_OR_BALANCE_BASED",
                                    "dailyPauseType": "CLOSE_ONLY",
                                    "leverage": 100,
                                    "inactivityPeriodDays": 5,
                                    "minProfitableTradingDays": 3,
                                    "maxTradingDays": 7,
                                    "groupMt5": "group MT 5",
                                    "groupTradeLocker": "group Trade Locker",
                                    "groupCTrader": "group CTrader",
                                    "groupDxTrade": "group DxTrade",
                                    "consistencyTarget": 50,
                                    "createdAt": "2022-09-11T23:09:59Z",
                                    "updatedAt": "2023-01-02T16:15:01Z",
                                    "settings": [
                                        {
                                            "id": "00181783-28a6-468d-8ee7-bad3b94383f2",
                                            "type": "PROFIT_TARGET",
                                            "useMovementsFromOrder": false,
                                            "isVisibleInConfigurator": true,
                                            "movements": [
                                                {
                                                    "id": "5d9656f7-ad9e-4238-be5b-f46e42a01a80",
                                                    "type": "PERCENTAGE",
                                                    "percentageValue": 7,
                                                    "listValue": null,
                                                    "movementPercentageValue": 3,
                                                    "movementAbsoluteValue": 15.3,
                                                    "isDefault": false
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(AdminGetChallengeQuery(challengeId = "b67d8028-c088-488b-9fd4-bf8f8077e6b2".toUUID()))
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access create challenge EP" {
        every { commandBus(any<AdminCreateChallengeCommand>()) } just runs

        mockMvc.post("/admin-app/challenges") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "name": "Mission impossible",
                    "startingBalance": 992.2,
                    "type": "FOREX"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminCreateChallengeCommand(
                    name = "Mission impossible",
                    startingBalance = 992.2.toBigDecimal(),
                    type = ChallengeType.FOREX
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access patch challenge EP" {
        every { commandBus(any<AdminPatchChallengeCommand>()) } just runs

        mockMvc.patch("/admin-app/challenges/7b96eae4-3989-4fab-abfa-7f9e6cf32f8a") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "name": "Mission impossible",
                    "startingBalance": 992.2,
                    "state": "ENABLED",
                    "type": "CRYPTO"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminPatchChallengeCommand(
                    challengeId = "7b96eae4-3989-4fab-abfa-7f9e6cf32f8a".toUUID(),
                    name = "Mission impossible",
                    startingBalance = 992.2.toBigDecimal(),
                    state = ChallengeState.ENABLED,
                    type = ChallengeType.CRYPTO
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }
})
