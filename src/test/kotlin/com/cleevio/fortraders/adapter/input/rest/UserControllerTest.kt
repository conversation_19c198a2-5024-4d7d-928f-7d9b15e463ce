package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.addJsonContent
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.user.command.DeleteUserProfileImageCommand
import com.cleevio.fortraders.application.module.user.command.PatchUserCommand
import com.cleevio.fortraders.application.module.user.command.RequestUserKycVerificationCommand
import com.cleevio.fortraders.application.module.user.command.UpdateUserDetailsCommand
import com.cleevio.fortraders.application.module.user.command.UpdateUserLastLoginIpCommand
import com.cleevio.fortraders.application.module.user.command.UploadUserProfileImageCommand
import com.cleevio.fortraders.application.module.user.query.GetUserDetailsQuery
import com.cleevio.fortraders.application.module.user.query.GetUserStatsQuery
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentReward
import com.cleevio.fortraders.domain.model.tournamentuser.constant.TournamentUserState
import com.cleevio.fortraders.domain.model.user.constant.PreferredLanguage
import com.cleevio.fortraders.domain.model.user.constant.UserKycState
import com.cleevio.fortraders.domain.model.user.constant.UserRole
import com.cleevio.fortraders.jsonContent
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.multipart
import org.springframework.test.web.servlet.patch
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.nio.charset.Charset
import java.time.Instant

@WebMvcTest(UserController::class)
class UserControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns user.jwt
    }

    "should correctly access get user details EP" {
        val countryId = "75e1b567-c748-4c49-b7fa-b2789361ba5e".toUUID()

        every { queryBus(any<GetUserDetailsQuery>()) } returns GetUserDetailsQuery.Result(
            id = "efa91293-858c-42e7-a9b4-************".toUUID(),
            role = UserRole.ADMIN,
            email = "<EMAIL>",
            walletBalance = 105.5.toBigDecimal(),
            firstName = "Johny",
            lastName = "Cash",
            kycState = UserKycState.PENDING,
            preferredLanguage = PreferredLanguage.ENGLISH,
            contact = GetUserDetailsQuery.ContactDetail(
                streetAddress = "Obchodní 123",
                city = "Prague",
                postCode = "150 00",
                phonePrefix = "+421",
                phoneNumber = "9112345624",
                country = GetUserDetailsQuery.CountryDetail(
                    id = countryId,
                    name = "Czechia",
                    isoCode = "CZ"
                )
            ),
            profileImage = GetUserDetailsQuery.ProfileImageDetail(
                url = "https://cdn.fortraders.devel.cleevio.dev/123e4567-e89b-12d3-a456-************.png",
                fileId = "db31a96f-d8bb-4e3f-ac8d-daa2003deb8c".toUUID()
            ),
            supportChatSignature = "123456"
        )

        mockMvc.get("/web-app/users/me") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                        "id": "efa91293-858c-42e7-a9b4-************",
                        "role": "ADMIN",
                        "email": "<EMAIL>",
                        "walletBalance": 105.5,
                        "firstName": "Johny",
                        "lastName": "Cash",
                        "kycState": "PENDING",
                        "preferredLanguage": "ENGLISH",
                        "contact": {
                            "streetAddress": "Obchodní 123",
                            "city": "Prague",
                            "postCode": "150 00",
                            "phonePrefix": "+421",
                            "phoneNumber": "9112345624",
                            "country": {
                                "id": "$countryId",
                                "name": "Czechia",
                                "isoCode": "CZ"
                            }
                        },
                        "profileImage": {
                            "url": "https://cdn.fortraders.devel.cleevio.dev/123e4567-e89b-12d3-a456-************.png",
                            "fileId": "db31a96f-d8bb-4e3f-ac8d-daa2003deb8c"
                        },
                        "supportChatSignature": "123456"
                    }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                GetUserDetailsQuery(
                    userId = user.appUserId
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }

    "should correctly access update user last login IP EP" {
        every { commandBus(any<UpdateUserLastLoginIpCommand>()) } just runs

        mockMvc.put("/web-app/users/me/last-login-ip") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
            addJsonContent(
                """
                    {
                        "lastLoginIp": "127.0.0.1"
                    }
                """.trimIndent()

            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                UpdateUserLastLoginIpCommand(
                    userId = user.appUserId,
                    lastLoginIp = "127.0.0.1"
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }

    "should correctly access patch user EP for preferred language update" {
        every { commandBus(any<PatchUserCommand>()) } just runs

        mockMvc.patch("/web-app/users/me") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
            addJsonContent(
                """
                    {
                        "preferredLanguage": "SPANISH"
                    }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                PatchUserCommand(
                    userId = user.appUserId,
                    preferredLanguage = PreferredLanguage.SPANISH
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }

    "should correctly access update user details EP" {
        val countryId = "75e1b567-c748-4c49-b7fa-b2789361ba5e".toUUID()

        every { commandBus(any<UpdateUserDetailsCommand>()) } returns UpdateUserDetailsCommand.Result(
            email = "<EMAIL>",
            firstName = "Johny",
            lastName = "Cash",
            countryName = "Czechia",
            countryId = countryId,
            streetAddress = "Obchodní 123",
            city = "Prague",
            postCode = "150 00",
            phonePrefix = "+420",
            phoneNumber = "777888999"
        )

        mockMvc.put("/web-app/users/me") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
            addJsonContent(
                """
                    {
                        "firstName": "Johny",
                        "lastName": "Cash",
                        "countryId": "$countryId",
                        "streetAddress": "Obchodní 123",
                        "city": "Prague",
                        "postCode": "150 00",
                        "phonePrefix": "+420",
                        "phoneNumber": "777888999"
                    }
                """.trimIndent()

            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                        "email": "<EMAIL>",
                        "firstName": "Johny",
                        "lastName": "Cash",
                        "countryName": "Czechia",
                        "countryId": "$countryId",
                        "streetAddress": "Obchodní 123",
                        "city": "Prague",
                        "postCode": "150 00",
                        "phonePrefix": "+420",
                        "phoneNumber": "777888999"
                    }
                """.trimIndent()
            )
        }

        verify {
            commandBus(
                UpdateUserDetailsCommand(
                    userId = user.appUserId,
                    firstName = "Johny",
                    lastName = "Cash",
                    countryId = countryId,
                    streetAddress = "Obchodní 123",
                    city = "Prague",
                    postCode = "150 00",
                    phonePrefix = "+420",
                    phoneNumber = "777888999"
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }

    "should correctly access get user stats EP" {
        every { queryBus(any<GetUserStatsQuery>()) } returns GetUserStatsQuery.Result(
            stats = GetUserStatsQuery.StatsDetail(
                id = "6f340878-45b0-48e2-86f7-a8f2f07f69af".toUUID(),
                firstName = "Peter",
                lastName = "Pan",
                profileImageUrl = "https://example.com/user_profile_picture/511a0b06-4bac-4e7b-9ed6-fa9df3d9bd38.jpg",
                tournaments = GetUserStatsQuery.TournamentStatsDetail(
                    totalCount = 3,
                    moneyPrizeSum = 5500.toBigDecimal(),
                    podiums = 3
                ),
                trades =
                GetUserStatsQuery.TradeStatsDetail(
                    totalCount = 4,
                    profitableCount = 2,
                    unprofitableCount = 2,
                    averageProfit = 375.toBigDecimal()
                ),
                instruments =
                GetUserStatsQuery.InstrumentStatsDetail(
                    mostTraded = listOf("USD-ETH", "AAPL", "SPY")
                )
            ),
            tournaments = GetUserStatsQuery.TournamentsDetail(
                active = listOf(
                    GetUserStatsQuery.TournamentDetail(
                        id = "86dadbad-f3f8-4df8-bc9b-b5b7434a3c37".toUUID(),
                        name = "TournamentOne",
                        reward = TournamentReward.MONEY,
                        initialPrizePool = 10000.toBigDecimal(),
                        entryFee = 25.toBigDecimal(),
                        startsAt = Instant.parse("2024-03-25T15:27:56.415953Z"),
                        endsAt = Instant.parse("2024-03-25T15:36:56.415953Z"),
                        coverUrl = "https://example.com/tournament_cover/f3f1036f-5ecc-42df-b260-997270aca1e6.png",
                        user = GetUserStatsQuery.UserDetail(
                            state = TournamentUserState.VALID,
                            profit = 150.toBigDecimal()
                        )
                    )
                ),
                past = listOf(
                    GetUserStatsQuery.TournamentDetail(
                        id = "a6653306-83ee-44ac-a1ed-5489cd15cc4d".toUUID(),
                        name = "TournamentThree",
                        reward = TournamentReward.CHALLENGES,
                        initialPrizePool = null,
                        entryFee = 100.toBigDecimal(),
                        startsAt = Instant.parse("2024-03-25T15:16:56.415953Z"),
                        endsAt = Instant.parse("2024-03-25T15:25:56.415953Z"),
                        coverUrl = "https://example.com/tournament_cover/f3f1036f-5ecc-42df-b260-997270aca1e6.png",
                        user = GetUserStatsQuery.UserDetail(
                            state = TournamentUserState.BREACHED,
                            profit = (-500).toBigDecimal()
                        )
                    )
                )
            )
        )

        mockMvc.get("/web-app/users/me/stats") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                        "stats": {
                            "id": "6f340878-45b0-48e2-86f7-a8f2f07f69af",
                            "firstName": "Peter",
                            "lastName": "Pan",
                            "profileImageUrl": "https://example.com/user_profile_picture/511a0b06-4bac-4e7b-9ed6-fa9df3d9bd38.jpg",
                            "tournaments": {
                                "totalCount": 3,
                                "moneyPrizeSum": 5500,
                                "podiums": 3
                            },
                            "trades": {
                                "totalCount": 4,
                                "profitableCount": 2,
                                "unprofitableCount": 2,
                                "averageProfit": 375
                            },
                            "instruments": {
                                "mostTraded": [
                                    "USD-ETH",
                                    "AAPL",
                                    "SPY"
                                ]
                            }
                        },
                        "tournaments": {
                            "active": [
                                {
                                    "id": "86dadbad-f3f8-4df8-bc9b-b5b7434a3c37",
                                    "name": "TournamentOne",
                                    "reward": "MONEY",
                                    "initialPrizePool": 10000,
                                    "entryFee": 25,
                                    "startsAt": "2024-03-25T15:27:56.415953Z",
                                    "endsAt": "2024-03-25T15:36:56.415953Z",
                                    "coverUrl": "https://example.com/tournament_cover/f3f1036f-5ecc-42df-b260-997270aca1e6.png",
                                    "user": {
                                        "state": "VALID",
                                        "profit": 150
                                    }
                                }
                            ],
                            "past": [
                                {
                                    "id": "a6653306-83ee-44ac-a1ed-5489cd15cc4d",
                                    "name": "TournamentThree",
                                    "reward": "CHALLENGES",
                                    "initialPrizePool": null,
                                    "entryFee": 100,
                                    "startsAt": "2024-03-25T15:16:56.415953Z",
                                    "endsAt": "2024-03-25T15:25:56.415953Z",
                                    "coverUrl": "https://example.com/tournament_cover/f3f1036f-5ecc-42df-b260-997270aca1e6.png",
                                    "user": {
                                        "state": "BREACHED",
                                        "profit": -500
                                    }
                                }
                            ]
                        }
                    }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                GetUserStatsQuery(
                    userId = user.appUserId
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }

    "should correctly access upload user profile image EP" {
        every { commandBus(any<UploadUserProfileImageCommand>()) } returns UploadUserProfileImageCommand.Result(
            profileImageUrl = "https://example.com/profile-image.jpg"
        )

        val profileImageMockFile = MockMultipartFile(
            "file",
            "image.png",
            MediaType.IMAGE_PNG_VALUE,
            "test-image-content".toByteArray(Charset.forName("UTF-8"))
        )

        mockMvc.multipart("/web-app/users/me/profile-image") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
            file(profileImageMockFile)
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "profileImageUrl": "https://example.com/profile-image.jpg"
                }
                """.trimIndent()
            )
        }

        verify {
            commandBus(
                UploadUserProfileImageCommand(
                    userId = user.appUserId,
                    file = profileImageMockFile
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }

    "should correctly access delete user profile image EP" {
        every { commandBus(any<DeleteUserProfileImageCommand>()) } just runs

        mockMvc.delete("/web-app/users/me/profile-image") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                DeleteUserProfileImageCommand(
                    userId = user.appUserId
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }

    "should correctly access request user KYC verification EP" {
        every { commandBus(any<RequestUserKycVerificationCommand>()) } returns RequestUserKycVerificationCommand.Result(
            url = "https://example.com/kyc-verification"
        )

        mockMvc.post("/web-app/users/me/kyc") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "url": "https://example.com/kyc-verification"
                }
                """.trimIndent()
            )
        }

        verify {
            commandBus(
                RequestUserKycVerificationCommand(
                    userId = user.appUserId
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }
})
