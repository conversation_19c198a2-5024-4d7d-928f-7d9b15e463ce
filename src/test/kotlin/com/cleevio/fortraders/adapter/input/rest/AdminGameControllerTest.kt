package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.addJsonContent
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.game.command.AdminCreateGameCommand
import com.cleevio.fortraders.application.module.game.command.AdminUpdateGameCommand
import com.cleevio.fortraders.application.module.game.query.AdminGetGameQuery
import com.cleevio.fortraders.application.module.game.query.AdminSearchGamesQuery
import com.cleevio.fortraders.domain.model.game.constant.GameState
import com.cleevio.fortraders.domain.model.game.constant.GameType
import com.cleevio.fortraders.domain.model.instrument.constant.InstrumentType
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentPrizePoolType
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentReward
import com.cleevio.fortraders.jsonContent
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.Instant
import java.util.UUID

@WebMvcTest(AdminGameController::class)
class AdminGameControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns admin.jwt
    }

    "should correctly access create game EP" {
        every { commandBus(any<AdminCreateGameCommand>()) } just runs

        mockMvc.post("/admin-app/games") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "logoFileId" : "123e4567-e89b-12d3-a456-************",
                    "type" : "CASUAL",
                    "state" : "ENABLED",
                    "name" : "New Stock Game",
                    "description" : "Game description"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminCreateGameCommand(
                    logoFileId = ("123e4567-e89b-12d3-a456-************").toUUID(),
                    type = GameType.CASUAL,
                    state = GameState.ENABLED,
                    name = "New Stock Game",
                    description = "Game description"
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access update game EP" {
        every { commandBus(any<AdminUpdateGameCommand>()) } just runs

        mockMvc.put("/admin-app/games/f14777fa-c31a-4bdc-9129-94a50741e77c") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "state" : "DISABLED",
                    "name" : "Updated Stock Game",
                    "description" : "Updated game description",
                    "logoFileId" : "32bcc464-e423-4ca0-ac0a-3e22864dd636"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminUpdateGameCommand(
                    id = "f14777fa-c31a-4bdc-9129-94a50741e77c".toUUID(),
                    state = GameState.DISABLED,
                    name = "Updated Stock Game",
                    description = "Updated game description",
                    logoFileId = "32bcc464-e423-4ca0-ac0a-3e22864dd636".toUUID()
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access search game EP" {

        val gameId = "db22f458-033d-42f6-bcbb-74cc4be53e79".toUUID()
        val logoUrl = "https://cdn.fortraders.devel.cleevio.dev/4a74c585-7982-4faf-902a-f85d854b7e33.png"
        every { queryBus(any<AdminSearchGamesQuery>()) } returns PageImpl(
            listOf(
                AdminSearchGamesQuery.Result(
                    id = gameId,
                    type = GameType.CASUAL,
                    state = GameState.ENABLED,
                    name = "New Stock Game",
                    description = "Game description",
                    logoUrl = logoUrl,
                    createdAt = Instant.parse("2022-12-24T18:15:30Z"),
                    updatedAt = Instant.parse("2023-11-19T15:18:39Z")
                )
            ),
            PageRequest.of(1, 10),
            2
        )

        mockMvc.post("/admin-app/games/search?page=1&size=10") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "states": ["DISABLED", "ENABLED"],
                    "createdAtFrom": "2019-12-24T18:15:30Z",
                    "createdAtTo": "2020-12-24T19:18:20Z"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "content": [
                        {
                            "id": "$gameId",
                            "type": "CASUAL",
                            "state": "ENABLED",
                            "name": "New Stock Game",
                            "description": "Game description",
                            "logoUrl": "$logoUrl",
                            "createdAt": "2022-12-24T18:15:30Z",
                            "updatedAt": "2023-11-19T15:18:39Z"
                        }
                    ],
                    "currentPage": 1,
                    "pageSize": 10,
                    "totalElements": 11,
                    "totalPages": 2
                }
                """.trimIndent()
            )

            verify {
                queryBus(
                    AdminSearchGamesQuery(
                        PageRequest.of(1, 10),
                        AdminSearchGamesQuery.Filter(
                            states = setOf(GameState.DISABLED, GameState.ENABLED),
                            createdAtFrom = Instant.parse("2019-12-24T18:15:30Z"),
                            createdAtTo = Instant.parse("2020-12-24T19:18:20Z")
                        )
                    )
                )
                jwtDecoder.decode(admin.accessToken)
            }
        }
    }

    "should correctly access get game EP" {
        val gameId = "db22f458-033d-42f6-bcbb-74cc4be53e79".toUUID()
        val logoUrl = "https://cdn.fortraders.devel.cleevio.dev/123e4567-e89b-12d3-a456-************.png"
        val instrumentOneId = "e21c758e-d459-4ae3-8da2-5dc45411d778".toUUID()
        val tournamentOneId = "085153ca-e02a-4396-a11a-b0b134b94991".toUUID()
        val coverUrl = "https://cdn.fortraders.devel.cleevio.dev/4a74c585-7982-4faf-902a-f85d854b7e33.png"
        val createdAt = Instant.parse("2022-12-24T18:15:30Z")
        val updatedAt = Instant.parse("2023-01-24T18:15:30Z")
        val startsAt = Instant.parse("2023-05-24T18:15:30Z")
        val endsAt = Instant.parse("2023-06-24T18:15:30Z")

        val instruments = listOf(
            AdminGetGameQuery.InstrumentDetails(
                id = instrumentOneId,
                type = InstrumentType.FOREX,
                name = "Instrument1",
                ticker = "USD-EUR",
                spreadPercentage = 0.5.toBigDecimal(),
                createdAt = createdAt,
                updatedAt = updatedAt
            ),
            AdminGetGameQuery.InstrumentDetails(
                id = UUID.randomUUID(),
                type = InstrumentType.STOCK,
                name = "Instrument2",
                ticker = "AAPL",
                spreadPercentage = 2.2.toBigDecimal(),
                createdAt = createdAt,
                updatedAt = updatedAt
            )
        )

        val tournaments = listOf(
            AdminGetGameQuery.TournamentDetails(
                id = tournamentOneId,
                name = "Forex tournament",
                reward = TournamentReward.MONEY,
                initialPrizePool = 1000.5.toBigDecimal(),
                prizePoolType = TournamentPrizePoolType.SINGLE_TIER,
                entryFee = 5.5.toBigDecimal(),
                buyInsLimit = 150,
                maxDrawdown = 25,
                rules = "These are the rules of the tournament.",
                startsAt = startsAt,
                endsAt = endsAt,
                leverageEnabled = true,
                coverUrl = coverUrl,
                createdAt = createdAt,
                updatedAt = updatedAt
            )
        )

        every { queryBus(any<AdminGetGameQuery>()) } returns
            AdminGetGameQuery.Result(
                id = gameId,
                type = GameType.CASUAL,
                state = GameState.ENABLED,
                name = "New Stock Game",
                description = "Game description",
                logoFileId = "771f4152-7a39-48ae-a2a6-196d35c96233".toUUID(),
                logoUrl = logoUrl,
                createdAt = createdAt,
                updatedAt = updatedAt,
                instruments = instruments,
                tournaments = tournaments
            )

        mockMvc.get("/admin-app/games/$gameId") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "id": "$gameId",
                    "type": "CASUAL",
                    "state": "ENABLED",
                    "name": "New Stock Game",
                    "description": "Game description",
                    "logoFileId": "771f4152-7a39-48ae-a2a6-196d35c96233",
                    "logoUrl": "$logoUrl",
                    "createdAt": "$createdAt",
                    "updatedAt": "$updatedAt",
                    "instruments": [
                        {
                            "type": "FOREX",
                            "name": "Instrument1",
                            "ticker": "USD-EUR",
                            "spreadPercentage": 0.5,
                            "createdAt": "$createdAt",
                            "updatedAt": "$updatedAt"
                        },
                        {
                            "type": "STOCK",
                            "name": "Instrument2",
                            "ticker": "AAPL",
                            "spreadPercentage": 2.2,
                            "createdAt": "$createdAt",
                            "updatedAt": "$updatedAt"
                        }
                    ],
                    "tournaments": [
                        {
                            "coverUrl": "$coverUrl",
                            "name": "Forex tournament",
                            "reward": "MONEY",
                            "initialPrizePool": 1000.5,
                            "prizePoolType": "SINGLE_TIER",
                            "entryFee": 5.5,
                            "buyInsLimit": 150,
                            "maxDrawdown": 25,
                            "rules": "These are the rules of the tournament.",
                            "startsAt": "$startsAt",
                            "endsAt": "$endsAt",
                            "leverageEnabled": true,
                            "createdAt": "$createdAt",
                            "updatedAt": "$updatedAt"
                        }
                    ]
                }
                """.trimIndent()
            )

            verify {
                queryBus(AdminGetGameQuery(gameId))
                jwtDecoder.decode(admin.accessToken)
            }
        }
    }
})
