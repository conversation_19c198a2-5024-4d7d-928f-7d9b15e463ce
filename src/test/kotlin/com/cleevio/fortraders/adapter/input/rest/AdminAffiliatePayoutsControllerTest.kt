package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.addJsonContent
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.affiliatepayout.command.AdminUpdateAffiliatePayoutStateCommand
import com.cleevio.fortraders.application.module.affiliatepayout.query.AdminSearchAffiliatePayoutsQuery
import com.cleevio.fortraders.domain.model.affiliate.constant.AffiliateState
import com.cleevio.fortraders.domain.model.affiliatepayout.constant.AffiliatePayoutState
import com.cleevio.fortraders.jsonContent
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.Instant

@WebMvcTest(AdminAffiliatePayoutsController::class)
class AdminAffiliatePayoutsControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns admin.jwt
    }

    "should correctly access search affiliate payouts EP" {
        every { queryBus(any<AdminSearchAffiliatePayoutsQuery>()) } returns PageImpl(
            listOf(
                AdminSearchAffiliatePayoutsQuery.Result(
                    id = "db22f458-033d-42f6-bcbb-74cc4be53e79".toUUID(),
                    state = AffiliatePayoutState.APPROVED,
                    createdAt = Instant.parse("2022-12-24T18:15:30Z"),
                    updatedAt = Instant.parse("2023-11-19T15:18:39Z"),
                    amount = 123.4.toBigDecimal(),
                    user = AdminSearchAffiliatePayoutsQuery.UserDetail(
                        id = "88865c3b-65a2-4791-a24a-8e78145f8a3b".toUUID(),
                        email = "<EMAIL>",
                        firstName = "John",
                        lastName = "Doe",
                        blacklisted = false,
                        affiliate = AdminSearchAffiliatePayoutsQuery.AffiliateDetail(
                            id = "f6087d00-6deb-49cd-83ba-06aa9c818ca8".toUUID(),
                            state = AffiliateState.ACTIVE,
                            commissionBalance = 150.5.toBigDecimal()
                        )
                    )
                )
            ),
            PageRequest.of(1, 10),
            2
        )

        mockMvc.post("/admin-app/affiliate-payouts/search?page=1&size=10") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "states": ["APPROVED", "REQUESTED"],
                    "createdAtFrom": "2019-12-24T18:15:30Z",
                    "createdAtTo": "2020-12-24T19:18:20Z",
                    "fulltext": "john"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                	"content": [{
                		"id": "db22f458-033d-42f6-bcbb-74cc4be53e79",
                		"state": "APPROVED",
                		"amount": 123.4,
                		"createdAt": "2022-12-24T18:15:30Z",
                		"updatedAt": "2023-11-19T15:18:39Z",
                		"user": {
                			"id": "88865c3b-65a2-4791-a24a-8e78145f8a3b",
                			"email": "<EMAIL>",
                 			"firstName": "John",
                            "lastName": "Doe",
                            "blacklisted": false,
                            "affiliate": {
                                "id": "f6087d00-6deb-49cd-83ba-06aa9c818ca8",
                                "state": "ACTIVE",
                                "commissionBalance": 150.5
                            }
                        }
                	}],
                	"currentPage": 1,
                	"pageSize": 10,
                	"totalElements": 11,
                	"totalPages": 2
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                AdminSearchAffiliatePayoutsQuery(
                    pageable = PageRequest.of(1, 10),
                    filter = AdminSearchAffiliatePayoutsQuery.Filter(
                        states = setOf(AffiliatePayoutState.REQUESTED, AffiliatePayoutState.APPROVED),
                        createdAtFrom = Instant.parse("2019-12-24T18:15:30Z"),
                        createdAtTo = Instant.parse("2020-12-24T19:18:20Z"),
                        fulltext = "john"
                    )
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access update payout state EP" {
        every { commandBus(any<AdminUpdateAffiliatePayoutStateCommand>()) } just runs

        mockMvc.put("/admin-app/affiliate-payouts/aa628336-2fd2-4a0b-9666-7dc4e227eab6/state/APPROVED") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "internalNote": "Internal note",
                    "externalNote": "External note"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminUpdateAffiliatePayoutStateCommand(
                    affiliatePayoutId = "aa628336-2fd2-4a0b-9666-7dc4e227eab6".toUUID(),
                    affiliatePayoutState = AffiliatePayoutState.APPROVED,
                    internalNote = "Internal note",
                    externalNote = "External note"
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }
})
