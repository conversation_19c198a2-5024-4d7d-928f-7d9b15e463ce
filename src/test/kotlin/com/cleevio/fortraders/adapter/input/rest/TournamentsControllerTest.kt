package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.tournament.query.GetTournamentGameplayQuery
import com.cleevio.fortraders.application.module.tournament.query.GetTournamentQuery
import com.cleevio.fortraders.application.module.tournamentuser.command.JoinUserToTournamentCommand
import com.cleevio.fortraders.domain.model.game.constant.GameType
import com.cleevio.fortraders.domain.model.instrument.constant.InstrumentType
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentPhase
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentReward
import com.cleevio.fortraders.domain.model.tournamenttrade.constant.TournamentTradeType
import com.cleevio.fortraders.domain.model.tournamentuser.constant.TournamentUserState
import com.cleevio.fortraders.jsonContent
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import java.time.Instant

@WebMvcTest(TournamentsController::class)
class TournamentsControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns user.jwt
    }

    "should correctly access join user to tournament EP" {
        every { commandBus(any<JoinUserToTournamentCommand>()) } just runs

        mockMvc.post(
            "/web-app/games/db22f458-033d-42f6-bcbb-74cc4be53e79/tournaments/4930717e-259c-4f6f-9f8b-414a844851ff/join"
        ) {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                JoinUserToTournamentCommand(
                    userId = user.appUserId,
                    tournamentId = "4930717e-259c-4f6f-9f8b-414a844851ff".toUUID()
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }

    "should correctly access get tournament EP" - {
        val gameId = "db22f458-033d-42f6-bcbb-74cc4be53e79".toUUID()
        val userId = "337655a0-7b54-44e8-9307-8bda9f03817f".toUUID()
        val logoUrl = "https://cdn.fortraders.devel.cleevio.dev/123e4567-e89b-12d3-a456-************.png"
        val coverUrl = "https://cdn.fortraders.devel.cleevio.dev/4a74c585-7982-4faf-902a-f85d854b7e33.png"
        val profileImageUrl = "https://cdn.fortraders.devel.cleevio.dev/337655a0-7b54-44e8-9307-8bda9f03817f.png"
        val tournamentId = "085153ca-e02a-4396-a11a-b0b134b94991".toUUID()
        val startsAt = Instant.parse("2023-05-24T18:15:30Z")
        val endsAt = Instant.parse("2023-06-24T18:15:30Z")

        val instruments = listOf(
            GetTournamentQuery.InstrumentDetail(
                type = InstrumentType.STOCK,
                ticker = "AAPL"
            ),
            GetTournamentQuery.InstrumentDetail(
                type = InstrumentType.FOREX,
                ticker = "USD-EUR"
            )
        )

        val prizes = listOf(
            GetTournamentQuery.PrizeDetail(
                number = 1,
                prize = 1800.toBigDecimal()
            ),
            GetTournamentQuery.PrizeDetail(
                number = 2,
                prize = 1400.toBigDecimal()
            )
        )

        val game = GetTournamentQuery.GameDetail(
            id = gameId,
            type = GameType.CASUAL,
            name = "New casual game",
            logoUrl = logoUrl
        )

        val players = listOf(
            GetTournamentQuery.PlayerDetail(
                id = userId,
                firstName = "John",
                lastName = "Poor",
                state = TournamentUserState.VALID,
                profileImageUrl = profileImageUrl,
                profit = 55.5.toBigDecimal(),
                profitPercentage = 5.2.toBigDecimal(),
                prize = GetTournamentQuery.PrizeDetail(
                    number = 2,
                    prize = 1400.toBigDecimal()
                )
            )
        )

        val userDetail = GetTournamentQuery.UserDetail(
            profit = 55.5.toBigDecimal(),
            profitPercentage = 5.2.toBigDecimal(),
            state = TournamentUserState.VALID,
            prize = GetTournamentQuery.PrizeDetail(
                number = 1,
                prize = 1800.toBigDecimal()
            )
        )

        every { queryBus(any<GetTournamentQuery>()) } returns GetTournamentQuery.Result(
            id = tournamentId,
            name = "Forex tournament",
            reward = TournamentReward.MONEY,
            initialPrizePool = 1000.5.toBigDecimal(),
            entryFee = 5.5.toBigDecimal(),
            maxDrawdown = 25,
            rules = "These are the rules of the tournament.",
            startsAt = startsAt,
            endsAt = endsAt,
            leverageEnabled = true,
            coverUrl = coverUrl,
            phase = TournamentPhase.FINISHED,
            buyInsLimitExceeded = false,
            instruments = instruments,
            prizes = prizes,
            game = game,
            players = players,
            user = userDetail
        )

        mockMvc.get("/web-app/games/$gameId/tournaments/$tournamentId") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                        "id": "$tournamentId",
                        "name": "Forex tournament",
                        "reward": "MONEY",
                        "initialPrizePool": 1000.5,
                        "entryFee": 5.5,
                        "maxDrawdown": 25,
                        "rules": "These are the rules of the tournament.",
                        "startsAt": "$startsAt",
                        "endsAt": "$endsAt",
                        "leverageEnabled": true,
                        "coverUrl": "$coverUrl",
                        "phase": "FINISHED",
                        "buyInsLimitExceeded": false,
                        "instruments": [
                            {
                                "type": "STOCK",
                                "ticker": "AAPL"
                            },
                            {
                                "type": "FOREX",
                                "ticker": "USD-EUR"
                            }
                        ],
                        "prizes": [
                            {
                                "number": 1,
                                "prize": 1800
                            },
                            {
                                "number": 2,
                                "prize": 1400
                            }
                        ],
                        "game": {
                            "id": "$gameId",
                            "type": "CASUAL",
                            "name": "New casual game",
                            "logoUrl": "$logoUrl"
                        },
                        "players": [
                            {
                                "id": "$userId",
                                "firstName": "John",
                                "lastName": "Poor",
                                "state": "VALID",
                                "profileImageUrl": "$profileImageUrl",
                                "profit": 55.5,
                                "profitPercentage": 5.2,
                                "prize": {
                                    "number": 2,
                                    "prize": 1400
                                }
                            }
                        ],
                        "user": {
                            "profit": 55.5,
                            "profitPercentage": 5.2,
                            "state": "VALID",
                            "prize": {
                                "number": 1,
                                "prize": 1800
                            }
                        }
                    }
                """.trimIndent()
            )
        }

        verify {
            queryBus(GetTournamentQuery(tournamentId = tournamentId, userId = user.appUserId))
            jwtDecoder.decode(user.accessToken)
        }

        "should call without authentication" {
            mockMvc.get("/web-app/games/$gameId/tournaments/$tournamentId") {
                addAcceptHeader(ApiVersion.VERSION_1_JSON)
            }.andExpect {
                status { isOk() }
            }

            verify {
                queryBus(GetTournamentQuery(tournamentId = tournamentId, userId = null))
            }
        }
    }

    "should correctly access get tournament gameplay EP" {
        val gameId = "db22f458-033d-42f6-bcbb-74cc4be53e79".toUUID()
        val tournamentId = "085153ca-e02a-4396-a11a-b0b134b94991".toUUID()

        val prizes = listOf(
            GetTournamentGameplayQuery.PrizeDetail(
                number = 1,
                prize = 1800.toBigDecimal()
            ),
            GetTournamentGameplayQuery.PrizeDetail(
                number = 2,
                prize = 1400.toBigDecimal()
            )
        )

        val game = GetTournamentGameplayQuery.GameDetail(
            id = gameId,
            type = GameType.CASUAL,
            name = "New casual game",
            logoUrl = "https://cdn.fortraders.devel.cleevio.dev/123e4567-e89b-12d3-a456-************.png"
        )

        val instruments = listOf(
            GetTournamentGameplayQuery.InstrumentDetail(
                id = "3fa585af-6ebf-472f-aeea-9d69418cbaeb".toUUID(),
                type = InstrumentType.CRYPTO,
                ticker = "SQL-USD",
                bidPrice = 4816.7.toBigDecimal(),
                askPrice = 4815.6.toBigDecimal(),
                timestamp = 1691622800L
            )
        )

        val leaderboard = listOf(
            GetTournamentGameplayQuery.LeaderboardDetail(
                id = "337655a0-7b54-44e8-9307-8bda9f03817f".toUUID(),
                firstName = "John",
                lastName = "Poor",
                profileImageUrl = "https://cdn.fortraders.devel.cleevio.dev/337655a0-7b54-44e8-9307-8bda9f03817f.png",
                state = TournamentUserState.VALID,
                profit = 50.5.toBigDecimal(),
                profitPercentage = 10.5.toBigDecimal(),
                prize = GetTournamentGameplayQuery.PrizeDetail(
                    number = 1,
                    prize = 1800.toBigDecimal()
                )
            )
        )

        val openTrades = listOf(
            GetTournamentGameplayQuery.OpenTradeDetail(
                id = "1abd18bc-0634-42aa-9a97-bc6526b763ef".toUUID(),
                instrumentId = "3fa585af-6ebf-472f-aeea-9d69418cbaeb".toUUID(),
                type = TournamentTradeType.BUY,
                openPrice = 4817.7.toBigDecimal(),
                leverage = 1,
                profit = 30.5.toBigDecimal(),
                profitPercentage = 10.8.toBigDecimal()
            )
        )

        val closedTrades = listOf(
            GetTournamentGameplayQuery.ClosedTradeDetail(
                id = "69ce3c96-3115-459a-9191-31e5aa341174".toUUID(),
                instrumentId = "566c0752-adaf-482a-a9b2-6347e6d5f907".toUUID(),
                type = TournamentTradeType.BUY,
                openPrice = 4817.7.toBigDecimal(),
                closePrice = 4998.6.toBigDecimal(),
                leverage = 2,
                profitPercentage = 15.8.toBigDecimal(),
                createdAt = Instant.parse("2023-05-24T17:15:30Z"),
                closedAt = Instant.parse("2023-05-24T18:15:30Z")
            )
        )

        every { queryBus(any<GetTournamentGameplayQuery>()) } returns GetTournamentGameplayQuery.Result(
            id = tournamentId,
            name = "Forex tournament",
            reward = TournamentReward.MONEY,
            initialPrizePool = 1000.5.toBigDecimal(),
            entryFee = 33.3.toBigDecimal(),
            maxDrawdown = 25,
            rules = "These are the rules of the tournament.",
            startsAt = Instant.parse("2023-05-24T18:15:30Z"),
            endsAt = Instant.parse("2023-06-24T18:15:30Z"),
            leverageEnabled = true,
            coverUrl = "https://cdn.fortraders.devel.cleevio.dev/4a74c585-7982-4faf-902a-f85d854b7e33.png",
            phase = TournamentPhase.FINISHED,
            buyInsLimitExceeded = false,
            instruments = instruments,
            prizes = prizes,
            game = game,
            leaderboard = leaderboard,
            openTrades = openTrades,
            closedTrades = closedTrades,
            user = GetTournamentGameplayQuery.UserDetail(
                profit = 55.5.toBigDecimal(),
                state = TournamentUserState.VALID
            )
        )

        mockMvc.get("/web-app/games/$gameId/tournaments/$tournamentId/gameplay") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                        "id": "$tournamentId",
                        "name": "Forex tournament",
                        "reward": "MONEY",
                        "initialPrizePool": 1000.5,
                        "entryFee": 33.3,
                        "maxDrawdown": 25,
                        "rules": "These are the rules of the tournament.",
                        "startsAt": "2023-05-24T18:15:30Z",
                        "endsAt": "2023-06-24T18:15:30Z",
                        "leverageEnabled": true,
                        "coverUrl": "https://cdn.fortraders.devel.cleevio.dev/4a74c585-7982-4faf-902a-f85d854b7e33.png",
                        "phase": "FINISHED",
                        "buyInsLimitExceeded": false,
                        "instruments": [
                            {
                                "id": "3fa585af-6ebf-472f-aeea-9d69418cbaeb",
                                "type": "CRYPTO",
                                "ticker": "SQL-USD",
                                "bidPrice": 4816.7,
                                "askPrice": 4815.6,
                                "timestamp": 1691622800
                            }
                        ],
                        "prizes": [
                            {
                                "number": 1,
                                "prize": 1800
                            },
                            {
                                "number": 2,
                                "prize": 1400
                            }
                        ],
                        "game": {
                            "id": "$gameId",
                            "type": "CASUAL",
                            "name": "New casual game",
                            "logoUrl": "https://cdn.fortraders.devel.cleevio.dev/123e4567-e89b-12d3-a456-************.png"
                        },
                        "leaderboard": [
                            {
                                "id": "337655a0-7b54-44e8-9307-8bda9f03817f",
                                "firstName": "John",
                                "lastName": "Poor",
                                "profileImageUrl": "https://cdn.fortraders.devel.cleevio.dev/337655a0-7b54-44e8-9307-8bda9f03817f.png",
                                "state": "VALID",
                                "profit": 50.5,
                                "profitPercentage": 10.5,
                                "prize": {
                                    "number": 1,
                                    "prize": 1800
                                }
                            }
                        ],
                        "openTrades": [
                            {
                                "id": "1abd18bc-0634-42aa-9a97-bc6526b763ef",
                                "instrumentId": "3fa585af-6ebf-472f-aeea-9d69418cbaeb",
                                "type": "BUY",
                                "openPrice": 4817.7,
                                "leverage": 1,
                                "profit": 30.5,
                                "profitPercentage": 10.8
                            }
                        ],
                        "closedTrades": [
                            {
                                "id": "69ce3c96-3115-459a-9191-31e5aa341174",
                                "instrumentId": "566c0752-adaf-482a-a9b2-6347e6d5f907",
                                "type": "BUY",
                                "openPrice": 4817.7,
                                "closePrice": 4998.6,
                                "leverage": 2,
                                "profitPercentage": 15.8,
                                "createdAt": "2023-05-24T17:15:30Z",
                                "closedAt": "2023-05-24T18:15:30Z"
                            }
                        ],
                        "user": {
                            "profit": 55.5,
                            "state": "VALID"
                        }
                    }
                """.trimIndent()
            )

            verify {
                queryBus(GetTournamentGameplayQuery(userId = user.appUserId, tournamentId = tournamentId))
                jwtDecoder.decode(user.accessToken)
            }
        }
    }
})
