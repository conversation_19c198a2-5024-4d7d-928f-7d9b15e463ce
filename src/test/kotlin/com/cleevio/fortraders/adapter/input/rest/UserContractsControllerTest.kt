package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.application.common.dto.SimplePage
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.usercontract.query.GetUserContractsQuery
import com.cleevio.fortraders.domain.model.usercontract.constant.UserContractState
import com.cleevio.fortraders.jsonContent
import io.mockk.every
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.test.web.servlet.get
import java.time.Instant

@WebMvcTest(UserContractsController::class)
class UserContractsControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns user.jwt
    }

    "should restrict access to get contracts EP to unauthenticated user" {
        mockMvc.get("/web-app/users/me/contracts") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
        }.andExpect {
            status { isUnauthorized() }
        }
    }

    "should correctly access get contracts EP" {
        val contractId = "33ae914b-464c-4056-9d3f-8e2f6e20c371".toUUID()
        val orderId = "44ae914b-464c-4056-9d3f-8e2f6e20c371".toUUID()

        every { queryBus(any<GetUserContractsQuery>()) } returns GetUserContractsQuery.Result(
            contracts = SimplePage(
                content = listOf(
                    GetUserContractsQuery.ContractDetail(
                        id = contractId,
                        order = GetUserContractsQuery.OrderDetail(
                            id = orderId,
                            challengeId = "66ae914b-464c-4056-9d3f-8e2f6e20c371".toUUID(),
                            challengePlanId = "55ae914b-464c-4056-9d3f-8e2f6e20c371".toUUID(),
                            productName = "Test Product",
                            price = 90.toBigDecimal(),
                            createdAt = Instant.parse("2024-02-25T14:10:39.408Z")
                        ),
                        submissionId = 12345L,
                        embedUrl = "https://example.com/contract",
                        state = UserContractState.REQUESTED,
                        pdfFileUrl = null,
                        createdAt = Instant.parse("2024-02-26T14:10:39.408Z"),
                        updatedAt = Instant.parse("2024-02-27T13:00:09.632791Z")
                    )
                ),
                currentPage = 0,
                pageSize = 10,
                totalElements = 1,
                totalPages = 1
            )
        )

        mockMvc.get("/web-app/users/me/contracts?page=0&size=10") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(user.accessToken)
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "contracts": {
                        "content": [
                            {
                                "id": "33ae914b-464c-4056-9d3f-8e2f6e20c371",
                                "order": {
                                    "id": "44ae914b-464c-4056-9d3f-8e2f6e20c371",
                                    "challengeId": "66ae914b-464c-4056-9d3f-8e2f6e20c371",
                                    "challengePlanId": "55ae914b-464c-4056-9d3f-8e2f6e20c371",
                                    "productName": "Test Product",
                                    "price": 90,
                                    "createdAt": "2024-02-25T14:10:39.408Z"
                                },
                                "submissionId": 12345,
                                "embedUrl": "https://example.com/contract",
                                "state": "REQUESTED",
                                "pdfFileUrl": null,
                                "createdAt": "2024-02-26T14:10:39.408Z",
                                "updatedAt": "2024-02-27T13:00:09.632791Z"
                            }
                        ],
                        "currentPage": 0,
                        "pageSize": 10,
                        "totalElements": 1,
                        "totalPages": 1
                    }
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                GetUserContractsQuery(
                    pageable = PageRequest.of(0, 10),
                    userId = user.appUserId
                )
            )
            jwtDecoder.decode(user.accessToken)
        }
    }
})
