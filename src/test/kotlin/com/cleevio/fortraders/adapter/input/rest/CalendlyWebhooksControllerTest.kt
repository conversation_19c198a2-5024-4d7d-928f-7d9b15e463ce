package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.application.module.verificationcall.command.CancelVerificationCallCommand
import com.cleevio.fortraders.application.module.verificationcall.command.ConfirmVerificationCallCommand
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.post
import java.time.Instant
import java.util.UUID

@WebMvcTest(CalendlyWebhooksController::class)
class CalendlyWebhooksControllerTest : ControllerTest({

    "should process invitee.created event and confirm verification call" {
        every { commandBus(any<ConfirmVerificationCallCommand>()) } just runs

        val verificationCallId = UUID.randomUUID()
        val requestBody = """
            {
                "event": "invitee.created",
                "payload": {
                    "uri": "https://api.calendly.com/scheduled_events/AAAAAAAAAAAAAAAA/invitees/BBBBBBBBBBBBBBBB",
                    "tracking": {
                        "utm_content": "$verificationCallId"
                    },
                    "scheduled_event": {
                        "uri": "https://api.calendly.com/scheduled_events/AAAAAAAAAAAAAAAA",
                        "start_time": "2023-12-01T10:00:00.000000Z",
                        "end_time": "2023-12-01T10:30:00.000000Z",
                        "location": {
                            "join_url": "https://zoom.us/j/123456789",
                            "type": "zoom",
                            "status": "active"
                        }
                    }
                }
            }
        """.trimIndent()

        mockMvc.post("/webhooks/calendly/events") {
            contentType = MediaType.APPLICATION_JSON
            content = requestBody
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                ConfirmVerificationCallCommand(
                    verificationCallId = verificationCallId,
                    startTime = Instant.parse("2023-12-01T10:00:00.000000Z"),
                    endTime = Instant.parse("2023-12-01T10:30:00.000000Z"),
                    joinUrl = "https://zoom.us/j/123456789",
                    scheduledEventId = "AAAAAAAAAAAAAAAA"
                )
            )
        }
    }

    "should process invitee.canceled event and cancel verification call" {
        every { commandBus(any<CancelVerificationCallCommand>()) } just runs

        val verificationCallId = UUID.randomUUID()
        val requestBody = """
            {
                "event": "invitee.canceled",
                "payload": {
                    "uri": "https://api.calendly.com/scheduled_events/CCCCCCCCCCCCCCCC/invitees/DDDDDDDDDDDDDDDD",
                    "tracking": {
                        "utm_content": "$verificationCallId"
                    },
                    "scheduled_event": {
                        "uri": "https://api.calendly.com/scheduled_events/CCCCCCCCCCCCCCCC",
                        "start_time": "2023-12-01T14:00:00.000000Z",
                        "end_time": "2023-12-01T14:30:00.000000Z",
                        "location": {
                            "join_url": "https://zoom.us/j/987654321",
                            "type": "zoom",
                            "status": "active"
                        }
                    }
                }
            }
        """.trimIndent()

        mockMvc.post("/webhooks/calendly/events") {
            contentType = MediaType.APPLICATION_JSON
            content = requestBody
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                CancelVerificationCallCommand(
                    verificationCallId = verificationCallId
                )
            )
        }
    }
})
