package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.addJsonContent
import com.cleevio.fortraders.application.common.dto.SimplePage
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.report.query.AdminSearchAccountsReportQuery
import com.cleevio.fortraders.application.module.report.query.AdminSearchCountriesRevenueOverviewQuery
import com.cleevio.fortraders.application.module.report.query.AdminSearchInstantAccountPayoutsReportQuery
import com.cleevio.fortraders.application.module.report.query.AdminSearchOverviewQuery
import com.cleevio.fortraders.application.module.report.query.AdminSearchPayoutsReportQuery
import com.cleevio.fortraders.application.module.report.query.AdminSearchProductsRevenueOverviewQuery
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanCategory
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.jsonContent
import com.cleevio.fortraders.toLocalDate
import io.mockk.every
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.test.web.servlet.post
import java.time.Instant

@WebMvcTest(AdminReportsController::class)
class AdminReportsControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns admin.jwt
    }

    "should correctly search overview EP" {
        every { queryBus(any<AdminSearchOverviewQuery>()) } returns AdminSearchOverviewQuery.Result(
            activeAccounts = AdminSearchOverviewQuery.AccountsDetail(
                total = 1320,
                thisMonth = 1320,
                challenges = listOf(
                    AdminSearchOverviewQuery.ChallengeDetail(
                        id = "9a251c32-a35c-4863-bc9f-f0e80d621e18".toUUID(),
                        name = "$5k Challenge",
                        startingBalance = 5000L.toBigDecimal(),
                        type = ChallengeType.FOREX,
                        plansToDate = listOf(
                            AdminSearchOverviewQuery.ChallengePlanDetail(
                                title = "One-step plan",
                                steps = 1,
                                category = ChallengePlanCategory.STANDARD,
                                totalAccounts = 24,
                                stepNumber = 1,
                                stepType = ChallengeStepType.FUNDED,
                                date = null
                            ),
                            AdminSearchOverviewQuery.ChallengePlanDetail(
                                title = "Two-step plan",
                                steps = 2,
                                category = ChallengePlanCategory.PRO,
                                totalAccounts = 35,
                                stepNumber = 1,
                                stepType = ChallengeStepType.EVALUATION,
                                date = null
                            )
                        ),
                        plansDateRange = listOf(
                            AdminSearchOverviewQuery.ChallengePlanDetail(
                                title = "One-step plan",
                                steps = 1,
                                category = ChallengePlanCategory.STANDARD,
                                totalAccounts = 30,
                                stepNumber = 1,
                                stepType = ChallengeStepType.FUNDED,
                                date = "2024-08-01".toLocalDate()
                            ),
                            AdminSearchOverviewQuery.ChallengePlanDetail(
                                title = "Two-step plan",
                                steps = 2,
                                category = ChallengePlanCategory.PRO,
                                totalAccounts = 40,
                                stepNumber = 1,
                                stepType = ChallengeStepType.EVALUATION,
                                date = "2024-08-02".toLocalDate()
                            )
                        )
                    )
                )
            ),
            newUsers = AdminSearchOverviewQuery.NewUsersDetail(
                total = 1322,
                counts = listOf(
                    AdminSearchOverviewQuery.NewUsersCountDetail(
                        date = "2024-08-01".toLocalDate(),
                        count = 26
                    ),
                    AdminSearchOverviewQuery.NewUsersCountDetail(
                        date = "2024-08-02".toLocalDate(),
                        count = 31
                    )
                )
            ),
            newClients = AdminSearchOverviewQuery.NewClientsDetail(
                total = 2231,
                counts = listOf(
                    AdminSearchOverviewQuery.NewClientsCountDetail(
                        date = "2024-08-01".toLocalDate(),
                        count = 12
                    ),
                    AdminSearchOverviewQuery.NewClientsCountDetail(
                        date = "2024-08-02".toLocalDate(),
                        count = 13
                    )
                )
            ),
            newTradingAccounts = AdminSearchOverviewQuery.TradingAccountsDetail(
                total = 1322,
                counts = listOf(
                    AdminSearchOverviewQuery.TradingAccountCountDetail(
                        date = "2024-09-01".toLocalDate(),
                        count = 36
                    ),
                    AdminSearchOverviewQuery.TradingAccountCountDetail(
                        date = "2024-09-02".toLocalDate(),
                        count = 47
                    )
                )
            ),
            possiblePayouts = AdminSearchOverviewQuery.PayoutsDetail(
                total = 83456.5.toBigDecimal(),
                topTradingAccounts = listOf(
                    AdminSearchOverviewQuery.TopTradingAccountDetail(
                        id = "06a07241-3c0b-47db-b8fe-3737ed158056".toUUID(),
                        accountId = "12345",
                        platform = PlatformType.META_TRADER_5,
                        plan = "$100k - Two-step",
                        startingBalance = 100000L.toBigDecimal(),
                        equity = 110000L.toBigDecimal(),
                        profit = 10000L.toBigDecimal(),
                        profitAfterSplit = 95000L.toBigDecimal(),
                        profitSplit = 95,
                        nextPayoutAvailableAt = Instant.parse("2023-07-24T12:15:30Z"),
                        user = AdminSearchOverviewQuery.UserDetail(
                            id = "c0064b05-8f45-4343-b50c-e66de69e6a89".toUUID(),
                            firstName = "John",
                            lastName = "Trader",
                            email = "<EMAIL>"
                        )
                    )
                )
            ),
            orders = AdminSearchOverviewQuery.OrdersDetail(
                totalCount = 32983,
                totalTurnover = 83456.5.toBigDecimal(),
                countries = listOf(
                    AdminSearchOverviewQuery.CountryDetail(
                        id = "d6225e10-d467-4905-b7d2-d5c8232edc19".toUUID(),
                        name = "Czechia",
                        countToday = 21,
                        countThisWeek = 92,
                        countLastWeek = 113,
                        countLastMonth = 473,
                        countTotal = 2390,
                        turnoverToday = 7350.toBigDecimal(),
                        turnoverThisWeek = 3241.toBigDecimal(),
                        turnoverLastWeek = 39550.toBigDecimal(),
                        turnoverLastMonth = 165550.toBigDecimal(),
                        turnoverTotal = 836500.toBigDecimal()
                    )
                )
            ),
            trends = AdminSearchOverviewQuery.TrendsDetail(
                ordersTotalCount = 10,
                breachesTotalCount = 20,
                counts = listOf(
                    AdminSearchOverviewQuery.TrendCountDetail(
                        date = "2024-08-01".toLocalDate(),
                        ordersCount = 1,
                        breachesCount = 2
                    ),
                    AdminSearchOverviewQuery.TrendCountDetail(
                        date = "2024-08-02".toLocalDate(),
                        ordersCount = 3,
                        breachesCount = 4
                    )
                )
            ),
            fundedAccounts = AdminSearchOverviewQuery.FundedAccountsDetail(
                newAccountsTotalCount = 3241,
                breachedAccountsTotalCount = 6927,
                counts = listOf(
                    AdminSearchOverviewQuery.FundedAccountCountDetail(
                        date = "2024-08-01".toLocalDate(),
                        newAccountsCount = 234,
                        breachedAccountsCount = 2345
                    ),
                    AdminSearchOverviewQuery.FundedAccountCountDetail(
                        date = "2024-08-02".toLocalDate(),
                        newAccountsCount = 123,
                        breachedAccountsCount = 2354
                    )
                )
            ),
            instantAccounts = AdminSearchOverviewQuery.InstantAccountsDetail(
                newAccountsTotalCount = 241,
                breachedAccountsTotalCount = 927,
                counts = listOf(
                    AdminSearchOverviewQuery.InstantAccountCountDetail(
                        date = "2024-09-01".toLocalDate(),
                        newAccountsCount = 34,
                        breachedAccountsCount = 345
                    ),
                    AdminSearchOverviewQuery.InstantAccountCountDetail(
                        date = "2024-09-02".toLocalDate(),
                        newAccountsCount = 23,
                        breachedAccountsCount = 354
                    )
                )
            )
        )

        mockMvc.post("/admin-app/reports/overview?sort=property1,asc") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "platforms": [
                        "META_TRADER_5",
                        "TRADE_LOCKER"
                    ],
                    "newUsersFrom": "2024-08-01",
                    "newUsersTo": "2024-08-31",
                    "newClientsFrom": "2024-10-01",
                    "newClientsTo": "2024-10-29",
                    "newTradingAccountsFrom": "2024-09-01",
                    "newTradingAccountsTo": "2024-09-30",
                    "trendsOverviewFrom": "2024-11-01",
                    "trendsOverviewTo": "2024-11-29",
                    "fundedAccountsOverviewFrom": "2024-11-22",
                    "fundedAccountsOverviewTo": "2024-11-23",
                    "activeAccountsFrom": "2024-07-01",
                    "activeAccountsTo": "2024-07-31",
                    "activeAccountSelected": "2024-07-15",
                    "instantAccountsOverviewFrom": "2024-11-11",
                    "instantAccountsOverviewTo": "2024-11-12",
                    "challengeTypes": ["FOREX"]
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "activeAccounts": {
                        "total": 1320,
                        "thisMonth": 1320,
                        "challenges": [
                            {
                                "id": "9a251c32-a35c-4863-bc9f-f0e80d621e18",
                                "name": "$5k Challenge",
                                "startingBalance": 5000,
                                "type": "FOREX",
                                "plansToDate": [
                                    {
                                        "title": "One-step plan",
                                        "steps": 1,
                                        "category": "STANDARD",
                                        "stepNumber": 1,
                                        "stepType": "FUNDED",
                                        "totalAccounts": 24
                                    },
                                    {
                                        "title": "Two-step plan",
                                        "steps": 2,
                                        "category": "PRO",
                                        "stepNumber": 1,
                                        "stepType": "EVALUATION",
                                        "totalAccounts": 35
                                    }
                                ],
                                "plansDateRange": [
                                    {
                                        "title": "One-step plan",
                                        "steps": 1,
                                        "category": "STANDARD",
                                        "stepNumber": 1,
                                        "stepType": "FUNDED",
                                        "totalAccounts": 30
                                    },
                                    {
                                        "title": "Two-step plan",
                                        "steps": 2,
                                        "category": "PRO",
                                        "stepNumber": 1,
                                        "stepType": "EVALUATION",
                                        "totalAccounts": 40
                                    }
                                ]
                            }
                        ]
                    },
                    "newUsers": {
                        "total": 1322,
                        "counts": [
                            {
                                "date": "2024-08-01",
                                "count": 26
                            },
                            {
                                "date": "2024-08-02",
                                "count": 31
                            }
                        ]
                    },
                    "newClients": {
                        "total": 2231,
                        "counts": [
                            {
                                "date": "2024-08-01",
                                "count": 12
                            },
                            {
                                "date": "2024-08-02",
                                "count": 13
                            }
                        ]
                    },
                    "newTradingAccounts": {
                        "total": 1322,
                        "counts": [
                            {
                                "date": "2024-09-01",
                                "count": 36
                            },
                            {
                                "date": "2024-09-02",
                                "count": 47
                            }
                        ]
                    },
                    "possiblePayouts": {
                        "total": 83456.5,
                        "topTradingAccounts": [
                            {
                                "id": "06a07241-3c0b-47db-b8fe-3737ed158056",
                                "accountId": "12345",
                                "platform": "META_TRADER_5",
                                "plan": "$100k - Two-step",
                                "startingBalance": 100000,
                                "equity": 110000,
                                "profit": 10000,
                                "profitAfterSplit": 95000,
                                "profitSplit": 95,
                                "nextPayoutAvailableAt": "2023-07-24T12:15:30Z",
                                "user": {
                                    "id": "c0064b05-8f45-4343-b50c-e66de69e6a89",
                                    "firstName": "John",
                                    "lastName": "Trader",
                                    "email": "<EMAIL>"
                                }
                            }
                        ]
                    },
                    "orders": {
                        "totalCount": 32983,
                        "totalTurnover": 83456.5,
                        "countries": [
                            {
                                "id": "d6225e10-d467-4905-b7d2-d5c8232edc19",
                                "name": "Czechia",
                                "countToday": 21,
                                "countThisWeek": 92,
                                "countLastWeek": 113,
                                "countLastMonth": 473,
                                "countTotal": 2390,
                                "turnoverToday": 7350,
                                "turnoverThisWeek": 3241,
                                "turnoverLastWeek": 39550,
                                "turnoverLastMonth": 165550,
                                "turnoverTotal": 836500
                            }
                        ]
                    },
                    "trends": {
                        "ordersTotalCount": 10,
                        "breachesTotalCount": 20,
                        "counts": [
                            {
                                "date": "2024-08-01",
                                "ordersCount": 1,
                                "breachesCount": 2
                            },
                            {
                                "date": "2024-08-02",
                                "ordersCount": 3,
                                "breachesCount": 4
                            }
                        ]
                    },
                    "fundedAccounts": {
                        "newAccountsTotalCount": 3241,
                        "breachedAccountsTotalCount": 6927,
                        "counts": [
                            {
                                "date": "2024-08-01",
                                "newAccountsCount": 234,
                                "breachedAccountsCount": 2345
                            },
                            {
                                "date": "2024-08-02",
                                "newAccountsCount": 123,
                                "breachedAccountsCount": 2354
                            }
                        ]
                    },
                    "instantAccounts": {
                        "newAccountsTotalCount": 241,
                        "breachedAccountsTotalCount": 927,
                        "counts": [
                            {
                                "date": "2024-09-01",
                                "newAccountsCount": 34,
                                "breachedAccountsCount": 345
                            },
                            {
                                "date": "2024-09-02",
                                "newAccountsCount": 23,
                                "breachedAccountsCount": 354
                            }
                        ]
                    }                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                AdminSearchOverviewQuery(
                    sort = Sort.by(Sort.Direction.ASC, "property1"),
                    filter = AdminSearchOverviewQuery.Filter(
                        platforms = setOf(PlatformType.META_TRADER_5, PlatformType.TRADE_LOCKER),
                        newUsersFrom = "2024-08-01".toLocalDate(),
                        newUsersTo = "2024-08-31".toLocalDate(),
                        newClientsFrom = "2024-10-01".toLocalDate(),
                        newClientsTo = "2024-10-29".toLocalDate(),
                        newTradingAccountsFrom = "2024-09-01".toLocalDate(),
                        newTradingAccountsTo = "2024-09-30".toLocalDate(),
                        trendsOverviewFrom = "2024-11-01".toLocalDate(),
                        trendsOverviewTo = "2024-11-29".toLocalDate(),
                        fundedAccountsOverviewFrom = "2024-11-22".toLocalDate(),
                        fundedAccountsOverviewTo = "2024-11-23".toLocalDate(),
                        activeAccountsFrom = "2024-07-01".toLocalDate(),
                        activeAccountsTo = "2024-07-31".toLocalDate(),
                        activeAccountSelected = "2024-07-15".toLocalDate(),
                        instantAccountsOverviewFrom = "2024-11-11".toLocalDate(),
                        instantAccountsOverviewTo = "2024-11-12".toLocalDate(),
                        challengeTypes = setOf(ChallengeType.FOREX)
                    )
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly search account reports EP" {
        every { queryBus(any<AdminSearchAccountsReportQuery>()) } returns AdminSearchAccountsReportQuery.Result(
            challenges = listOf(
                AdminSearchAccountsReportQuery.ChallengeDetail(
                    id = "9a251c32-a35c-4863-bc9f-f0e80d621e18".toUUID(),
                    name = "$5k Challenge",
                    startingBalance = 5000L.toBigDecimal(),
                    type = ChallengeType.FOREX,
                    plans = listOf(
                        AdminSearchAccountsReportQuery.ChallengePlanDetail(
                            id = "ca1df21a-1ea0-400a-a8ba-eb131613a465".toUUID(),
                            title = "One-step",
                            stepsCount = 2,
                            category = ChallengePlanCategory.STANDARD,
                            steps = listOf(
                                AdminSearchAccountsReportQuery.ChallengeStepDetail(
                                    number = 1,
                                    type = ChallengeStepType.EVALUATION,
                                    dates = listOf(
                                        AdminSearchAccountsReportQuery.ChallengeStepDateDetail(
                                            date = "2024-08-01".toLocalDate(),
                                            accountsCreated = 1,
                                            accountsBreached = 2
                                        ),
                                        AdminSearchAccountsReportQuery.ChallengeStepDateDetail(
                                            date = "2024-08-02".toLocalDate(),
                                            accountsCreated = 3,
                                            accountsBreached = 4
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
        )

        mockMvc.post("/admin-app/reports/accounts") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "platforms": [
                        "META_TRADER_5",
                        "TRADE_LOCKER"
                    ],
                    "datesFrom": "2024-08-01",
                    "datesTo": "2024-08-31",
                    "challengeTypes": ["FOREX"]
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "challenges": [
                        {
                            "id": "9a251c32-a35c-4863-bc9f-f0e80d621e18",
                            "name": "$5k Challenge",
                            "startingBalance": 5000,
                            "type": "FOREX",
                            "plans": [
                                {
                                    "id": "ca1df21a-1ea0-400a-a8ba-eb131613a465",
                                    "title": "One-step",
                                    "stepsCount": 2,
                                    "category": "STANDARD",
                                    "steps": [
                                        {
                                            "number": 1,
                                            "type": "EVALUATION",
                                            "dates": [
                                                {
                                                    "date": "2024-08-01",
                                                    "accountsCreated": 1,
                                                    "accountsBreached": 2
                                                },
                                                {
                                                    "date": "2024-08-02",
                                                    "accountsCreated": 3,
                                                    "accountsBreached": 4
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                AdminSearchAccountsReportQuery(
                    filter = AdminSearchAccountsReportQuery.Filter(
                        platforms = setOf(PlatformType.META_TRADER_5, PlatformType.TRADE_LOCKER),
                        datesFrom = "2024-08-01".toLocalDate(),
                        datesTo = "2024-08-31".toLocalDate(),
                        challengeTypes = setOf(ChallengeType.FOREX)
                    )
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly search payouts reports EP" {
        every { queryBus(any<AdminSearchPayoutsReportQuery>()) } returns AdminSearchPayoutsReportQuery.Result(
            totalPossiblePayouts = 12345.67.toBigDecimal(),
            tradingAccounts = SimplePage(
                content = listOf(
                    AdminSearchPayoutsReportQuery.TradingAccountDetail(
                        id = "c6b4bf96-df74-4d99-94e3-f33b0153bfa0".toUUID(),
                        accountId = "*********",
                        platform = PlatformType.META_TRADER_5,
                        plan = "Plan 1",
                        challengePlanCategory = ChallengePlanCategory.STANDARD,
                        challengeType = ChallengeType.FOREX,
                        startingBalance = 111.1.toBigDecimal(),
                        equity = 222.2.toBigDecimal(),
                        profit = 333.3.toBigDecimal(),
                        profitAfterSplit = 55.5.toBigDecimal(),
                        profitSplit = 50,
                        nextPayoutAvailableAt = Instant.parse("2023-07-24T12:15:30Z"),
                        maxMargin = 30.toBigDecimal(),
                        minimumProfitCondition = AdminSearchPayoutsReportQuery.PayoutConditionDetail(
                            isFulfilled = true,
                            value = 333.3.toBigDecimal(),
                            target = 100.0.toBigDecimal()
                        ),
                        profitableTradingDaysCondition = AdminSearchPayoutsReportQuery.PayoutConditionDetail(
                            isFulfilled = true,
                            value = 5,
                            target = 5
                        ),
                        consistencyTargetCondition = AdminSearchPayoutsReportQuery.PayoutConditionDetail(
                            isFulfilled = true,
                            value = 10.toBigDecimal(),
                            target = 15.toBigDecimal()
                        ),
                        user = AdminSearchPayoutsReportQuery.UserDetail(
                            id = "2087463d-d2ec-4226-acc6-cba5ec1363dc".toUUID(),
                            firstName = "John",
                            lastName = "Trader",
                            email = "<EMAIL>",
                            blacklisted = false,
                            labels = listOf(
                                AdminSearchPayoutsReportQuery.LabelDetail(
                                    id = "436b1ca3-9abb-4213-8213-11f8f437a464".toUUID(),
                                    name = "Label 1"
                                )
                            )
                        )
                    )
                ),
                currentPage = 1,
                pageSize = 10,
                totalElements = 11,
                totalPages = 2
            )
        )

        mockMvc.post("/admin-app/reports/payouts?page=1&size=10") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "checkPayoutConditions": true,
                    "challengePlanIds": [
                        "ece5d771-dded-41c2-97e8-af3687e623ba"
                    ],
                    "nextPayoutAvailableAtFrom": "2024-08-01T10:10:10Z",
                    "nextPayoutAvailableAtTo": "2024-08-31T23:59:00Z",
                    "labels": ["436b1ca3-9abb-4213-8213-11f8f437a464"],
                    "challengeTypes": ["FOREX"]
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "totalPossiblePayouts": 12345.67,
                    "tradingAccounts": {
                        "content": [
                            {
                                "id": "c6b4bf96-df74-4d99-94e3-f33b0153bfa0",
                                "accountId": "*********",
                                "platform": "META_TRADER_5",
                                "plan": "Plan 1",
                                "challengePlanCategory": "STANDARD",
                                "challengeType": "FOREX",
                                "startingBalance": 111.1,
                                "equity": 222.2,
                                "profit": 333.3,
                                "profitAfterSplit": 55.5,
                                "profitSplit": 50,
                                "nextPayoutAvailableAt": "2023-07-24T12:15:30Z",
                                "maxMargin": 30,
                                "minimumProfitCondition": {
                                    "isFulfilled": true,
                                    "value": 333.3,
                                    "target": 100.0
                                },
                                "profitableTradingDaysCondition": {
                                    "isFulfilled": true,
                                    "value": 5,
                                    "target": 5
                                },
                                "consistencyTargetCondition": {
                                    "isFulfilled": true,
                                    "value": 10,
                                    "target": 15
                                },
                                "user": {
                                    "id": "2087463d-d2ec-4226-acc6-cba5ec1363dc",
                                    "firstName": "John",
                                    "lastName": "Trader",
                                    "email": "<EMAIL>",
                                    "blacklisted": false,
                                    "labels": [{
                                        "id": "436b1ca3-9abb-4213-8213-11f8f437a464",
                                        "name": "Label 1"
                                    }]
                                }
                            }
                        ],
                        "currentPage": 1,
                        "pageSize": 10,
                        "totalElements": 11,
                        "totalPages": 2
                    }
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                AdminSearchPayoutsReportQuery(
                    pageable = PageRequest.of(1, 10, Sort.by(Sort.Direction.DESC, "profitAfterSplit")),
                    filter = AdminSearchPayoutsReportQuery.Filter(
                        checkPayoutConditions = true,
                        challengePlanIds = setOf("ece5d771-dded-41c2-97e8-af3687e623ba".toUUID()),
                        nextPayoutAvailableAtTo = Instant.parse("2024-08-31T23:59:00Z"),
                        nextPayoutAvailableAtFrom = Instant.parse("2024-08-01T10:10:10Z"),
                        labels = setOf("436b1ca3-9abb-4213-8213-11f8f437a464".toUUID()),
                        challengeTypes = setOf(ChallengeType.FOREX)
                    )
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly search instant account payouts reports EP" {
        every {
            queryBus(
                any<AdminSearchInstantAccountPayoutsReportQuery>()
            )
        } returns AdminSearchInstantAccountPayoutsReportQuery.Result(
            totalPossiblePayouts = 12345.67.toBigDecimal(),
            tradingAccounts = SimplePage(
                content = listOf(
                    AdminSearchInstantAccountPayoutsReportQuery.TradingAccountDetail(
                        id = "c6b4bf96-df74-4d99-94e3-f33b0153bfa0".toUUID(),
                        accountId = "*********",
                        platform = PlatformType.META_TRADER_5,
                        plan = "Plan 1",
                        challengePlanCategory = ChallengePlanCategory.PRO,
                        challengeType = ChallengeType.FOREX,
                        startingBalance = 111.1.toBigDecimal(),
                        equity = 222.2.toBigDecimal(),
                        profit = 333.3.toBigDecimal(),
                        profitAfterSplit = 55.5.toBigDecimal(),
                        profitSplit = 50,
                        nextPayoutAvailableAt = Instant.parse("2023-07-24T12:15:30Z"),
                        maxMargin = 30.00.toBigDecimal(),
                        minimumProfitCondition = AdminSearchInstantAccountPayoutsReportQuery.PayoutConditionDetail(
                            isFulfilled = true,
                            value = 10.toBigDecimal(),
                            target = 10.toBigDecimal()
                        ),
                        consistencyTargetCondition = AdminSearchInstantAccountPayoutsReportQuery.PayoutConditionDetail(
                            isFulfilled = false,
                            value = 20.toBigDecimal(),
                            target = 50.toBigDecimal()
                        ),
                        profitableTradingDaysCondition = AdminSearchInstantAccountPayoutsReportQuery.PayoutConditionDetail(
                            isFulfilled = true,
                            value = 2,
                            target = 2
                        ),
                        payoutProbability = 1,
                        user = AdminSearchInstantAccountPayoutsReportQuery.UserDetail(
                            id = "2087463d-d2ec-4226-acc6-cba5ec1363dc".toUUID(),
                            firstName = "John",
                            lastName = "Trader",
                            email = "<EMAIL>",
                            blacklisted = false,
                            labels = listOf(
                                AdminSearchInstantAccountPayoutsReportQuery.LabelDetail(
                                    id = "436b1ca3-9abb-4213-8213-11f8f437a464".toUUID(),
                                    name = "Label 1"
                                )
                            )
                        )
                    )
                ),
                currentPage = 1,
                pageSize = 10,
                totalElements = 11,
                totalPages = 2
            )
        )

        mockMvc.post("/admin-app/reports/instant-account-payouts?page=1&size=10") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "challengePlanIds": [
                        "ece5d771-dded-41c2-97e8-af3687e623ba"
                    ],
                    "payoutProbabilityFrom": 1,
                    "payoutProbabilityTo": 10,
                    "labels": ["436b1ca3-9abb-4213-8213-11f8f437a464"],
                    "challengeTypes": ["FOREX"]
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "totalPossiblePayouts": 12345.67,
                    "tradingAccounts": {
                        "content": [
                            {
                                "id": "c6b4bf96-df74-4d99-94e3-f33b0153bfa0",
                                "accountId": "*********",
                                "platform": "META_TRADER_5",
                                "plan": "Plan 1",
                                "challengePlanCategory": "PRO",
                                "challengeType": "FOREX",
                                "startingBalance": 111.1,
                                "equity": 222.2,
                                "profit": 333.3,
                                "profitAfterSplit": 55.5,
                                "profitSplit": 50,
                                "nextPayoutAvailableAt": "2023-07-24T12:15:30Z",
                                "maxMargin": 30.00,
                                "user": {
                                    "id": "2087463d-d2ec-4226-acc6-cba5ec1363dc",
                                    "firstName": "John",
                                    "lastName": "Trader",
                                    "email": "<EMAIL>",
                                    "blacklisted": false,
                                    "labels": [{
                                        "id": "436b1ca3-9abb-4213-8213-11f8f437a464",
                                        "name": "Label 1"
                                    }]
                                },
                                "minimumProfitCondition": {
                                    "isFulfilled": true,
                                    "value": 10,
                                    "target": 10
                                },
                                "consistencyTargetCondition": {
                                    "isFulfilled": false,
                                    "value": 20,
                                    "target": 50
                                },
                                "profitableTradingDaysCondition": {
                                    "isFulfilled": true,
                                    "value": 2,
                                    "target": 2
                                },
                                "payoutProbability": 1
                            }
                        ],
                        "currentPage": 1,
                        "pageSize": 10,
                        "totalElements": 11,
                        "totalPages": 2
                    }
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                AdminSearchInstantAccountPayoutsReportQuery(
                    pageable = PageRequest.of(1, 10, Sort.by(Sort.Direction.DESC, "payoutProbability")),
                    filter = AdminSearchInstantAccountPayoutsReportQuery.Filter(
                        challengePlanIds = setOf("ece5d771-dded-41c2-97e8-af3687e623ba".toUUID()),
                        payoutProbabilityFrom = 1,
                        payoutProbabilityTo = 10,
                        labels = setOf("436b1ca3-9abb-4213-8213-11f8f437a464".toUUID()),
                        challengeTypes = setOf(ChallengeType.FOREX)
                    )
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly search countries revenue overview EP" {
        every {
            queryBus(
                any<AdminSearchCountriesRevenueOverviewQuery>()
            )
        } returns AdminSearchCountriesRevenueOverviewQuery.Result(
            revenueTotal = 43241.5.toBigDecimal(),
            bonusAmountsTotal = 1111.1.toBigDecimal(),
            amountsAfterSplitTotal = 2222.2.toBigDecimal(),
            payoutsTotal = 12345.67.toBigDecimal(),
            payoutsCount = 12,
            averagePayout = 123.67.toBigDecimal(),
            percentageOfPayoutsTotalFromRevenueTotal = 0.55.toBigDecimal(),
            countryRevenues = listOf(
                AdminSearchCountriesRevenueOverviewQuery.CountryRevenueDetail(
                    country = AdminSearchCountriesRevenueOverviewQuery.CountryDetail(
                        id = "c6b4bf96-df74-4d99-94e3-f33b0153bfa0".toUUID(),
                        name = "Czechia",
                        isoCode = "CZ"
                    ),
                    revenueTotal = 743.3.toBigDecimal(),
                    bonusAmountsTotal = 111.1.toBigDecimal(),
                    amountsAfterSplitTotal = 222.2.toBigDecimal(),
                    payoutsTotal = 628.2.toBigDecimal(),
                    payoutsCount = 93,
                    averagePayout = 91823.3.toBigDecimal(),
                    averageBonusAmount = 1.2.toBigDecimal(),
                    averageAfterSplitAmount = 2.4.toBigDecimal(),
                    percentageOfPayoutsTotalFromRevenueTotal = 0.11.toBigDecimal(),
                    percentageOfAllPayouts = 0.22.toBigDecimal()
                )
            ),
            averageBonusAmount = 92.6.toBigDecimal(),
            averageAfterSplitAmount = 185.2.toBigDecimal()
        )

        mockMvc.post("/admin-app/reports/countries-revenue-overview") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "dateFrom": "2024-08-01",
                    "dateTo": "2024-08-31",
                    "challengeTypes": ["FOREX"]
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "revenueTotal": 43241.5,
                    "bonusAmountsTotal": 1111.1,
                    "amountsAfterSplitTotal": 2222.2,
                    "payoutsTotal": 12345.67,
                    "payoutsCount": 12,
                    "averageBonusAmount": 92.6,
                    "averageAfterSplitAmount": 185.2,
                    "averagePayout": 123.67,
                    "percentageOfPayoutsTotalFromRevenueTotal": 0.55,
                    "countryRevenues": [
                        {
                            "country": {
                                "id": "c6b4bf96-df74-4d99-94e3-f33b0153bfa0",
                                "name": "Czechia",
                                "isoCode": "CZ"
                            },
                            "revenueTotal": 743.3,
                            "bonusAmountsTotal": 111.1,
                            "amountsAfterSplitTotal": 222.2,
                            "payoutsTotal": 628.2,
                            "payoutsCount": 93,
                            "averageBonusAmount": 1.2,
                            "averageAfterSplitAmount": 2.4,
                            "averagePayout": 91823.3,
                            "percentageOfPayoutsTotalFromRevenueTotal": 0.11,
                            "percentageOfAllPayouts": 0.22
                        }
                    ]
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                AdminSearchCountriesRevenueOverviewQuery(
                    filter = AdminSearchCountriesRevenueOverviewQuery.Filter(
                        dateFrom = "2024-08-01".toLocalDate(),
                        dateTo = "2024-08-31".toLocalDate(),
                        challengeTypes = setOf(ChallengeType.FOREX)
                    )
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly search products revenue overview EP" {
        every {
            queryBus(
                any<AdminSearchProductsRevenueOverviewQuery>()
            )
        } returns AdminSearchProductsRevenueOverviewQuery.Result(
            revenueTotal = 43241.5.toBigDecimal(),
            bonusAmountsTotal = 1111.1.toBigDecimal(),
            amountsAfterSplitTotal = 2222.2.toBigDecimal(),
            payoutsTotal = 12345.67.toBigDecimal(),
            payoutsCount = 12,
            averageBonusAmount = 92.6.toBigDecimal(),
            averageAfterSplitAmount = 185.2.toBigDecimal(),
            averagePayout = 123.67.toBigDecimal(),
            percentageOfPayoutsTotalFromRevenueTotal = 0.55.toBigDecimal(),
            productRevenues = listOf(
                AdminSearchProductsRevenueOverviewQuery.ProductRevenueDetail(
                    product = AdminSearchProductsRevenueOverviewQuery.ProductDetail(
                        startingBalance = 5000.toBigDecimal(),
                        steps = 2,
                        category = ChallengePlanCategory.STANDARD
                    ),
                    revenueTotal = 743.3.toBigDecimal(),
                    bonusAmountsTotal = 111.1.toBigDecimal(),
                    amountsAfterSplitTotal = 222.2.toBigDecimal(),
                    payoutsTotal = 628.2.toBigDecimal(),
                    payoutsCount = 93,
                    averageBonusAmount = 1.2.toBigDecimal(),
                    averageAfterSplitAmount = 2.4.toBigDecimal(),
                    averagePayout = 91823.3.toBigDecimal(),
                    percentageOfPayoutsTotalFromRevenueTotal = 0.11.toBigDecimal(),
                    percentageOfAllPayouts = 0.22.toBigDecimal()
                )
            )
        )

        mockMvc.post("/admin-app/reports/products-revenue-overview") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "dateFrom": "2024-08-01",
                    "dateTo": "2024-08-31",
                    "challengeTypes": ["FOREX"]
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "revenueTotal": 43241.5,
                    "bonusAmountsTotal": 1111.1,
                    "amountsAfterSplitTotal": 2222.2,
                    "payoutsTotal": 12345.67,
                    "payoutsCount": 12,
                    "averageBonusAmount": 92.6,
                    "averageAfterSplitAmount": 185.2,
                    "averagePayout": 123.67,
                    "percentageOfPayoutsTotalFromRevenueTotal": 0.55,
                    "productRevenues": [
                        {
                            "product": {
                                "startingBalance": 5000,
                                "steps": 2,
                                "category": "STANDARD"
                            },
                            "revenueTotal": 743.3,
                            "bonusAmountsTotal": 111.1,
                            "amountsAfterSplitTotal": 222.2,
                            "payoutsTotal": 628.2,
                            "payoutsCount": 93,
                            "averageBonusAmount": 1.2,
                            "averageAfterSplitAmount": 2.4,
                            "averagePayout": 91823.3,
                            "percentageOfPayoutsTotalFromRevenueTotal": 0.11,
                            "percentageOfAllPayouts": 0.22
                        }
                    ]
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                AdminSearchProductsRevenueOverviewQuery(
                    filter = AdminSearchProductsRevenueOverviewQuery.Filter(
                        dateFrom = "2024-08-01".toLocalDate(),
                        dateTo = "2024-08-31".toLocalDate(),
                        challengeTypes = setOf(ChallengeType.FOREX)
                    )
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }
})
