package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addAcceptHeader
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.addJsonContent
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.payout.command.AdminApprovePayoutCommand
import com.cleevio.fortraders.application.module.payout.command.AdminDeclinePayoutCommand
import com.cleevio.fortraders.application.module.payout.command.AdminPatchPayoutCommand
import com.cleevio.fortraders.application.module.payout.query.AdminGetPayoutQuery
import com.cleevio.fortraders.application.module.payout.query.AdminSearchPayoutsQuery
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.payout.constant.PayoutFinalState
import com.cleevio.fortraders.domain.model.payout.constant.PayoutKycState
import com.cleevio.fortraders.domain.model.payout.constant.PayoutRiskState
import com.cleevio.fortraders.domain.model.payout.constant.PayoutState
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.domain.model.user.constant.UserKycState
import com.cleevio.fortraders.domain.model.verificationcall.constant.VerificationCallState
import com.cleevio.fortraders.domain.model.verificationcall.constant.VerificationCallType
import com.cleevio.fortraders.jsonContent
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.patch
import org.springframework.test.web.servlet.post
import java.time.Instant
import java.util.Optional

@WebMvcTest(AdminPayoutsController::class)
class AdminPayoutsControllerTest : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns admin.jwt
    }

    "should correctly access search payouts EP" {
        every { queryBus(any<AdminSearchPayoutsQuery>()) } returns PageImpl(
            listOf(
                AdminSearchPayoutsQuery.ResultWithTotalPayouts(
                    result = AdminSearchPayoutsQuery.Result(
                        id = "867f1e86-c7be-49bf-853a-6ce140e2f6bc".toUUID(),
                        riskState = PayoutRiskState.PENDING,
                        kycState = PayoutKycState.ALREADY_DONE,
                        finalState = PayoutFinalState.PAY_AND_TERMINATE,
                        note = "Some note",
                        internalReason = "Some internal reason",
                        externalReason = "Some external reason",
                        state = PayoutState.REQUESTED,
                        requestedAmount = 15.5.toBigDecimal(),
                        totalAmount = 105.5.toBigDecimal(),
                        amountAfterSplit = 89.9.toBigDecimal(),
                        bonusAmount = 10.0.toBigDecimal(),
                        shouldCountdown = true,
                        maxMargin = 35.toBigDecimal(),
                        finalisedAt = Instant.parse("2021-12-24T18:15:30Z"),
                        createdAt = Instant.parse("2022-12-24T18:15:30Z"),
                        updatedAt = Instant.parse("2022-12-24T19:16:30Z"),
                        tradingAccount = AdminSearchPayoutsQuery.TradingAccountDetail(
                            id = "c6b4bf96-df74-4d99-94e3-f33b0153bfa0".toUUID(),
                            state = TradingAccountState.BREACHED,
                            accountId = "*********",
                            plan = "Best plan ever",
                            startingBalance = 123.4.toBigDecimal(),
                            breach = AdminSearchPayoutsQuery.BreachDetail(
                                breachedAt = Instant.parse("2023-12-24T18:15:30Z"),
                                breachType = BreachType.MANUAL_BREACH,
                                internalReason = "Internal reason",
                                externalReason = "External reason"
                            )
                        ),
                        order = AdminSearchPayoutsQuery.OrderDetail(
                            id = "7cdb22e5-82e2-402b-8e92-22a0fb5a2606".toUUID(),
                            profitSplit = 90,
                            user = AdminSearchPayoutsQuery.UserDetail(
                                id = "e0d64da6-c71f-4cca-b203-77497d46a497".toUUID(),
                                email = "<EMAIL>",
                                firstName = "John",
                                lastName = "Doe",
                                kycState = UserKycState.FAILED,
                                payoutsCount = 5,
                                payoutsTotal = 456.7.toBigDecimal(),
                                depositTransactionsTotal = 202.5.toBigDecimal(),
                                country = AdminSearchPayoutsQuery.CountryDetail(
                                    isoCode = "PL",
                                    name = "Poland"
                                )
                            ),
                            challenge = AdminSearchPayoutsQuery.ChallengeDetail(
                                id = "d88794de-6239-439a-8f5a-fb6f88e933b2".toUUID(),
                                name = "Challenge 1",
                                type = ChallengeType.FOREX
                            ),
                            discountCode = AdminSearchPayoutsQuery.DiscountCodeDetail(
                                id = "f8b2c0c4-1e6e-4e80-81e6-62b9c91e3d6b".toUUID(),
                                code = "DISCOUNT10"
                            ),
                            affiliateCode = AdminSearchPayoutsQuery.AffiliateCodeDetail(
                                id = "a9b2c0c4-1e6e-4e80-81e6-62b9c91e3d6b".toUUID(),
                                code = "AFFILIATE10"
                            )
                        ),
                        labels = listOf(
                            AdminSearchPayoutsQuery.LabelDetail(
                                id = "436b1ca3-9abb-4213-8213-11f8f437a464".toUUID(),
                                name = "Label 1"
                            )
                        ),
                        verificationCalls = listOf(
                            AdminSearchPayoutsQuery.VerificationCallDetail(
                                type = VerificationCallType.PAYOUT,
                                state = VerificationCallState.SCHEDULED,
                                startsAt = Instant.parse("2024-03-01T14:00:00Z"),
                                endsAt = Instant.parse("2024-03-01T14:30:00Z"),
                                meetingUrl = "https://meet.google.com/abc-def-ghi",
                                recordingUrl = null,
                                note = "Payout verification call",
                                supportUsers = setOf(
                                    "a1b2c3d4-e5f6-7890-abcd-ef*********0".toUUID(),
                                    "b2c3d4e5-f6a7-8901-bcde-f23456789012".toUUID()
                                )
                            )
                        )
                    ),
                    pendingPayoutsTotal = 123.toBigDecimal(),
                    pendingPayoutsCount = 3
                )
            ),
            PageRequest.of(1, 10),
            2
        )

        mockMvc.post("/admin-app/payouts/search?page=1&size=10") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "states": ["REQUESTED", "DECLINED"],
                    "createdAtFrom": "2019-12-24T18:15:30Z",
                    "createdAtTo": "2020-12-24T19:18:20Z",
                    "labels": ["436b1ca3-9abb-4213-8213-11f8f437a464"],
                    "fulltext": "Adam M",
                    "challengeTypes": ["FOREX"]
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "content": [
                        {
                            "result": {
                                "id": "867f1e86-c7be-49bf-853a-6ce140e2f6bc",
                                "riskState": "PENDING",
                                "kycState": "ALREADY_DONE",
                                "finalState": "PAY_AND_TERMINATE",
                                "state": "REQUESTED",
                                "note": "Some note",
                                "internalReason": "Some internal reason",
                                "externalReason": "Some external reason",
                                "requestedAmount": 15.5,
                                "totalAmount": 105.5,
                                "amountAfterSplit": 89.9,
                                "bonusAmount": 10.0,
                                "shouldCountdown": true,
                                "maxMargin": 35,
                                "finalisedAt": "2021-12-24T18:15:30Z",
                                "createdAt": "2022-12-24T18:15:30Z",
                                "updatedAt": "2022-12-24T19:16:30Z",
                                "tradingAccount": {
                                    "id": "c6b4bf96-df74-4d99-94e3-f33b0153bfa0",
                                    "state": "BREACHED",
                                    "accountId": "*********",
                                    "startingBalance": 123.4,
                                    "plan": "Best plan ever",
                                    "breach": {
                                        "breachedAt": "2023-12-24T18:15:30Z",
                                        "breachType": "MANUAL_BREACH",
                                        "internalReason": "Internal reason",
                                        "externalReason": "External reason"
                                    }
                                },
                                "order": {
                                    "id": "7cdb22e5-82e2-402b-8e92-22a0fb5a2606",
                                    "profitSplit": 90,
                                    "user": {
                                        "id": "e0d64da6-c71f-4cca-b203-77497d46a497",
                                        "email": "<EMAIL>",
                                        "firstName": "John",
                                        "lastName": "Doe",
                                        "kycState": "FAILED",
                                        "payoutsCount": 5,
                                        "payoutsTotal": 456.7,
                                        "depositTransactionsTotal": 202.5,
                                        "country": {
                                            "isoCode": "PL",
                                            "name": "Poland"
                                        }
                                    },
                                    "challenge": {
                                        "id": "d88794de-6239-439a-8f5a-fb6f88e933b2",
                                        "name": "Challenge 1",
                                        "type": "FOREX"
                                    },
                                    "discountCode": {
                                        "id": "f8b2c0c4-1e6e-4e80-81e6-62b9c91e3d6b",
                                        "code": "DISCOUNT10"
                                    },
                                    "affiliateCode": {
                                        "id": "a9b2c0c4-1e6e-4e80-81e6-62b9c91e3d6b",
                                        "code": "AFFILIATE10"
                                    }
                                },
                                "labels": [{
                                    "id": "436b1ca3-9abb-4213-8213-11f8f437a464",
                                    "name": "Label 1"
                                }],
                                "verificationCalls": [{
                                    "type": "PAYOUT",
                                    "state": "SCHEDULED",
                                    "startsAt": "2024-03-01T14:00:00Z",
                                    "endsAt": "2024-03-01T14:30:00Z",
                                    "meetingUrl": "https://meet.google.com/abc-def-ghi",
                                    "recordingUrl": null,
                                    "note": "Payout verification call",
                                    "supportUsers": ["a1b2c3d4-e5f6-7890-abcd-ef*********0", "b2c3d4e5-f6a7-8901-bcde-f23456789012"]
                                }]
                            },
                            "pendingPayoutsTotal": 123,
                            "pendingPayoutsCount": 3
                        }
                    ],
                    "currentPage": 1,
                    "pageSize": 10,
                    "totalElements": 11,
                    "totalPages": 2
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                AdminSearchPayoutsQuery(
                    pageable = PageRequest.of(1, 10),
                    filter = AdminSearchPayoutsQuery.Filter(
                        states = setOf(PayoutState.REQUESTED, PayoutState.DECLINED),
                        createdAtFrom = Instant.parse("2019-12-24T18:15:30Z"),
                        createdAtTo = Instant.parse("2020-12-24T19:18:20Z"),
                        labels = setOf("436b1ca3-9abb-4213-8213-11f8f437a464".toUUID()),
                        fulltext = "Adam M",
                        challengeTypes = setOf(ChallengeType.FOREX)
                    )
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access get payout EP" {
        every { queryBus(any<AdminGetPayoutQuery>()) } returns AdminGetPayoutQuery.Result(
            id = "867f1e86-c7be-49bf-853a-6ce140e2f6bc".toUUID(),
            state = PayoutState.REQUESTED,
            requestedAmount = 15.5.toBigDecimal(),
            totalAmount = 105.5.toBigDecimal(),
            amountAfterSplit = 89.9.toBigDecimal(),
            bonusAmount = 10.0.toBigDecimal(),
            createdAt = Instant.parse("2022-12-24T18:15:30Z"),
            updatedAt = Instant.parse("2022-12-24T19:16:30Z"),
            order = AdminGetPayoutQuery.OrderDetail(
                id = "7cdb22e5-82e2-402b-8e92-22a0fb5a2606".toUUID(),
                profitSplit = 90,
                user = AdminGetPayoutQuery.UserDetail(
                    id = "e0d64da6-c71f-4cca-b203-77497d46a497".toUUID(),
                    firstName = "John",
                    lastName = "Doe",
                    email = "<EMAIL>",
                    blacklisted = false,
                    payoutsTotal = 456.7.toBigDecimal(),
                    contact = AdminGetPayoutQuery.ContactDetail(
                        phonePrefix = "+420",
                        phoneNumber = "*********"
                    )
                ),
                challenge = AdminGetPayoutQuery.ChallengeDetail(
                    id = "d88794de-6239-439a-8f5a-fb6f88e933b2".toUUID(),
                    name = "Challenge 1",
                    type = ChallengeType.FOREX,
                    plan = AdminGetPayoutQuery.ChallengePlanDetail(
                        id = "daf56e0a-2ce1-4533-aad3-81c292a77d8b".toUUID(),
                        title = "Plan 1"
                    )
                )
            ),
            tradingAccount = AdminGetPayoutQuery.TradingAccountDetail(
                id = "c6b4bf96-df74-4d99-94e3-f33b0153bfa0".toUUID(),
                state = TradingAccountState.BREACHED,
                accountId = "*********",
                currentBalance = 123.4.toBigDecimal(),
                breach = AdminGetPayoutQuery.BreachDetail(
                    breachedAt = Instant.parse("2023-12-24T18:15:30Z"),
                    breachType = BreachType.MANUAL_BREACH,
                    internalReason = "Internal reason",
                    externalReason = "External reason"
                )
            )
        )

        mockMvc.get("/admin-app/payouts/aa628336-2fd2-4a0b-9666-7dc4e227eab6") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                    "id": "867f1e86-c7be-49bf-853a-6ce140e2f6bc",
                    "state": "REQUESTED",
                    "requestedAmount": 15.5,
                    "totalAmount": 105.5,
                    "amountAfterSplit": 89.9,
                    "bonusAmount": 10.0,
                    "createdAt": "2022-12-24T18:15:30Z",
                    "updatedAt": "2022-12-24T19:16:30Z",
                    "tradingAccount": {
                        "id": "c6b4bf96-df74-4d99-94e3-f33b0153bfa0",
                        "state": "BREACHED",
                        "accountId": "*********",
                        "currentBalance": 123.4,
                        "breach": {
                            "breachedAt": "2023-12-24T18:15:30Z",
                            "breachType": "MANUAL_BREACH",
                            "internalReason": "Internal reason",
                            "externalReason": "External reason"
                        }
                    },
                    "order": {
                        "id": "7cdb22e5-82e2-402b-8e92-22a0fb5a2606",
                        "profitSplit": 90,
                        "user": {
                            "id": "e0d64da6-c71f-4cca-b203-77497d46a497",
                            "firstName": "John",
                            "lastName": "Doe",
                            "email": "<EMAIL>",
                            "blacklisted": false,
                            "payoutsTotal": 456.7,
                            "contact": {
                                "phonePrefix": "+420",
                                "phoneNumber": "*********"
                            }
                        },
                        "challenge": {
                            "id": "d88794de-6239-439a-8f5a-fb6f88e933b2",
                            "name": "Challenge 1",
                            "type": "FOREX",
                            "plan": {
                                "id": "daf56e0a-2ce1-4533-aad3-81c292a77d8b",
                                "title": "Plan 1"
                            }
                        }
                    }
                }
                """.trimIndent()
            )
        }

        verify {
            queryBus(
                AdminGetPayoutQuery(
                    payoutId = "aa628336-2fd2-4a0b-9666-7dc4e227eab6".toUUID()
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access patch payout EP" {
        every { commandBus(any<AdminPatchPayoutCommand>()) } just runs

        mockMvc.patch("/admin-app/payouts/aa628336-2fd2-4a0b-9666-7dc4e227eab6") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "riskState": "PENDING",
                    "kycState": "ALREADY_DONE",
                    "finalState": "PAY_AND_TERMINATE",
                    "note": "Some note"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminPatchPayoutCommand(
                    payoutId = "aa628336-2fd2-4a0b-9666-7dc4e227eab6".toUUID(),
                    riskState = PayoutRiskState.PENDING,
                    kycState = PayoutKycState.ALREADY_DONE,
                    finalState = PayoutFinalState.PAY_AND_TERMINATE,
                    note = Optional.of("Some note")
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access approve payout EP" {
        every { commandBus(any<AdminApprovePayoutCommand>()) } just runs

        mockMvc.post("/admin-app/payouts/aa628336-2fd2-4a0b-9666-7dc4e227eab6/approve") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "amountAfterSplit": 92.2,
                    "internalReason": "Some internal reason",
                    "externalReason": "Some external reason",
                    "bonusAmount": 10.0
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminApprovePayoutCommand(
                    payoutId = "aa628336-2fd2-4a0b-9666-7dc4e227eab6".toUUID(),
                    amountAfterSplit = 92.2.toBigDecimal(),
                    internalReason = "Some internal reason",
                    externalReason = "Some external reason",
                    bonusAmount = 10.0.toBigDecimal()
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access decline payout EP" {
        every { commandBus(any<AdminDeclinePayoutCommand>()) } just runs

        mockMvc.post("/admin-app/payouts/aa628336-2fd2-4a0b-9666-7dc4e227eab6/decline") {
            addAcceptHeader(ApiVersion.VERSION_1_JSON)
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "internalReason": "Some internal reason",
                    "externalReason": "Some external reason"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminDeclinePayoutCommand(
                    payoutId = "aa628336-2fd2-4a0b-9666-7dc4e227eab6".toUUID(),
                    internalReason = "Some internal reason",
                    externalReason = "Some external reason"
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }
})
