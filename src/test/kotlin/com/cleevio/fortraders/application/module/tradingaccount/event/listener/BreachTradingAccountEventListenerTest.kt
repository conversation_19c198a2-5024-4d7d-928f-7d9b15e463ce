package com.cleevio.fortraders.application.module.tradingaccount.event.listener

import com.cleevio.fortraders.UnitTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.tradingaccount.TradingAccountFeedService
import com.cleevio.fortraders.application.module.tradingaccount.TradingAccountService
import com.cleevio.fortraders.application.module.tradingaccountpause.TradingAccountPauseMonitoringService
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.breach.event.BreachCreatedEvent
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify

class BreachTradingAccountEventListenerTest : UnitTest({

    val tradingAccountService = mockk<TradingAccountService>()
    val tradingAccountPauseMonitoringService = mockk<TradingAccountPauseMonitoringService>()
    val tradingAccountFeedService = mockk<TradingAccountFeedService>()
    val underTest = BreachTradingAccountEventListener(
        tradingAccountService = tradingAccountService,
        tradingAccountPauseMonitoringService = tradingAccountPauseMonitoringService,
        tradingAccountFeedService = tradingAccountFeedService
    )

    afterAny {
        confirmVerified(
            tradingAccountService,
            tradingAccountFeedService
        )
    }

    "should correctly handle breach created event" {
        every { tradingAccountService.breachAccount(any()) } just runs

        underTest.handleBreachCreatedEvent(
            BreachCreatedEvent(
                tradingAccountId = "cc976ec7-5c1d-40cc-96ed-73e3303f88f2".toUUID(),
                type = BreachType.MAX_DRAWDOWN
            )
        )

        verify {
            tradingAccountService.breachAccount(
                id = "cc976ec7-5c1d-40cc-96ed-73e3303f88f2".toUUID()
            )
        }
    }

    "should correctly handle async breach created event" {
        every { tradingAccountService.upgradeTradingAccountAfterBreach(any(), any()) } just runs
        every { tradingAccountFeedService.cancelFeedSubscriptionsAfterBreach(any(), any()) } just runs

        underTest.handleAsyncBreachCreatedEvent(
            BreachCreatedEvent(
                tradingAccountId = "cc976ec7-5c1d-40cc-96ed-73e3303f88f2".toUUID(),
                type = BreachType.MAX_DRAWDOWN
            )
        )

        verify {
            tradingAccountService.upgradeTradingAccountAfterBreach(
                id = "cc976ec7-5c1d-40cc-96ed-73e3303f88f2".toUUID(),
                breachType = BreachType.MAX_DRAWDOWN
            )
            tradingAccountFeedService.cancelFeedSubscriptionsAfterBreach(
                tradingAccountId = "cc976ec7-5c1d-40cc-96ed-73e3303f88f2".toUUID(),
                breachType = BreachType.MAX_DRAWDOWN
            )
        }
    }
})
