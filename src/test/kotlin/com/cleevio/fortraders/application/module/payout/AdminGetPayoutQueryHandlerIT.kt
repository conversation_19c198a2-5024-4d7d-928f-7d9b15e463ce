package com.cleevio.fortraders.application.module.payout

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.payout.query.AdminGetPayoutQuery
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.payout.constant.PayoutState
import com.cleevio.fortraders.domain.model.payout.exception.PayoutNotFoundException
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.truncatedShouldBe
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe

class AdminGetPayoutQueryHandlerIT : IntegrationTest({

    "should throw because payout does not exist" {
        val challengePlan = testDataHelper.getChallengePlan()
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id)
        val user = testDataHelper.getUser(firstName = "Johny", lastName = "Cash")
        val order = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            state = TradingAccountState.ACTIVE,
            currentBalance = 987.1.toBigDecimal()
        )
        testDataHelper.getPayout(
            tradingAccountId = tradingAccount.id,
            amount = 10.0.toBigDecimal(),
            state = PayoutState.REQUESTED
        )

        shouldThrowExactly<PayoutNotFoundException> {
            queryBus(
                AdminGetPayoutQuery(
                    payoutId = "5e0c875f-5c95-4767-8608-0a59f6448143".toUUID()
                )
            )
        }
    }

    "should correctly find payout" {
        val user1 = testDataHelper.getUser(index = 1, email = "<EMAIL>", firstName = "John", lastName = "Doe")
        val user2 = testDataHelper.getUser(index = 2, email = "<EMAIL>", firstName = "Jack", lastName = "Daniels")

        testDataHelper.getContact(userId = user1.id, phoneNumber = "*********", phonePrefix = "+420")
        testDataHelper.getContact(userId = user2.id, phoneNumber = "*********", phonePrefix = "+421")

        val challenge1 = testDataHelper.getChallenge(
            index = 1,
            name = "Challenge 1",
            startingBalance = 100.0.toBigDecimal(),
            type = ChallengeType.FOREX
        )
        val challenge2 = testDataHelper.getChallenge(
            index = 2,
            name = "Challenge 2",
            startingBalance = 200.0.toBigDecimal(),
            type = ChallengeType.CRYPTO
        )
        val challengePlan1 =
            testDataHelper.getChallengePlan(index = 1, challengeId = challenge1.id, steps = 2, title = "ChallengePlan 1")
        val challengePlan2 =
            testDataHelper.getChallengePlan(index = 2, challengeId = challenge2.id, steps = 2, title = "ChallengePlan 2")
        val challengeStep11 = testDataHelper.getChallengeStep(challengePlanId = challengePlan1.id, number = 1)
        val challengeStep12 = testDataHelper.getChallengeStep(challengePlanId = challengePlan1.id, number = 2)
        val challengeStep21 = testDataHelper.getChallengeStep(challengePlanId = challengePlan2.id, number = 1)
        testDataHelper.getChallengeStep(challengePlanId = challengePlan2.id, number = 2)

        val order1 = testDataHelper.getOrder(
            userId = user1.id,
            challengeId = challenge1.id,
            challengePlanId = challengePlan1.id
        )
        val order2 = testDataHelper.getOrder(
            userId = user2.id,
            challengeId = challenge2.id,
            challengePlanId = challengePlan2.id
        )

        val tradingAccount1 = testDataHelper.getTradingAccount(
            accountId = "1",
            orderId = order1.id,
            challengeStepId = challengeStep11.id,
            state = TradingAccountState.ACTIVE,
            currentBalance = 987.1.toBigDecimal(),
            profitSplit = 50,
            isFundedChallengeStep = true
        )
        val tradingAccount2 = testDataHelper.getTradingAccount(
            accountId = "2",
            orderId = order1.id,
            challengeStepId = challengeStep12.id,
            profitSplit = 50,
            isFundedChallengeStep = true
        )
        val tradingAccount3 = testDataHelper.getTradingAccount(
            accountId = "3",
            orderId = order2.id,
            challengeStepId = challengeStep21.id,
            profitSplit = 60,
            isFundedChallengeStep = true
        )

        val payout1 = testDataHelper.getPayout(
            tradingAccountId = tradingAccount1.id,
            amount = 10.0.toBigDecimal(),
            state = PayoutState.REQUESTED,
            profitSplit = 50,
            bonusAmount = 2.5.toBigDecimal()
        )
        testDataHelper.getPayout(
            tradingAccountId = tradingAccount2.id,
            amount = 20.0.toBigDecimal(),
            profitSplit = 10,
            state = PayoutState.APPROVED
        )
        testDataHelper.getPayout(
            tradingAccountId = tradingAccount3.id,
            amount = 30.0.toBigDecimal(),
            state = PayoutState.DECLINED
        )
        testDataHelper.getPayout(
            tradingAccountId = tradingAccount2.id,
            amount = 40.0.toBigDecimal(),
            profitSplit = 10,
            state = PayoutState.APPROVED
        )

        val result = queryBus(
            AdminGetPayoutQuery(
                payoutId = payout1.id
            )
        )

        result.id shouldBe payout1.id
        result.state shouldBe PayoutState.REQUESTED
        result.requestedAmount shouldBeEqualComparingTo 10.toBigDecimal()
        result.totalAmount shouldBeEqualComparingTo 7.5.toBigDecimal()
        result.amountAfterSplit shouldBeEqualComparingTo 5.0.toBigDecimal()
        result.bonusAmount!! shouldBeEqualComparingTo 2.5.toBigDecimal()
        result.createdAt truncatedShouldBe payout1.createdAt
        result.updatedAt truncatedShouldBe payout1.updatedAt
        result.tradingAccount.let { tradingAccount ->
            tradingAccount.id shouldBe tradingAccount1.id
            tradingAccount.state shouldBe TradingAccountState.ACTIVE
            tradingAccount.accountId shouldBe "1"
            tradingAccount.currentBalance shouldBeEqualComparingTo 987.1.toBigDecimal()
        }
        result.order.let { order ->
            order.id shouldBe order1.id
            order.profitSplit shouldBe 50
            order.user.let { user ->
                user.id shouldBe user1.id
                user.firstName shouldBe "John"
                user.lastName shouldBe "Doe"
                user.email shouldBe "<EMAIL>"
                user.blacklisted shouldBe false
                user.payoutsTotal shouldBeEqualComparingTo 6.0.toBigDecimal()
                user.contact.shouldNotBeNull().let { contact ->
                    contact.phoneNumber shouldBe "*********"
                    contact.phonePrefix shouldBe "+420"
                }
            }
            order.challenge.let { challenge ->
                challenge.id shouldBe challenge1.id
                challenge.name shouldBe "Challenge 1"
                challenge.type shouldBe ChallengeType.FOREX
                challenge.plan.let { challengePlan ->
                    challengePlan.id shouldBe challengePlan1.id
                    challengePlan.title shouldBe "ChallengePlan 1"
                }
            }
        }
    }
})
