package com.cleevio.fortraders.application.module.order

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.order.command.PayForFreeOrderCommand
import com.cleevio.fortraders.application.module.tradingaccount.port.output.dto.CreateTradingAccountResult
import com.cleevio.fortraders.domain.model.order.OrderRepository
import com.cleevio.fortraders.domain.model.order.constant.OrderState
import com.cleevio.fortraders.domain.model.order.exception.InvalidOrderStateException
import com.cleevio.fortraders.domain.model.order.exception.OrderNotFoundException
import com.cleevio.fortraders.domain.model.tradingaccount.TradingAccountRepository
import com.cleevio.fortraders.domain.model.transaction.TransactionRepository
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import com.cleevio.fortraders.domain.model.wallet.WalletRepository
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify
import java.math.BigDecimal

class PayForFreeOrderCommandHandlerIT(
    private val orderRepository: OrderRepository,
    private val walletRepository: WalletRepository,
    private val transactionRepository: TransactionRepository,
    private val tradingAccountRepository: TradingAccountRepository,
) : IntegrationTest({

    "should throw because order does not exist" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val order = testDataHelper.getOrder(userId = user1.id)

        shouldThrowExactly<OrderNotFoundException> {
            commandBus(
                PayForFreeOrderCommand(
                    userId = user2.id,
                    orderId = order.id
                )
            )
        }
    }

    "should throw because order has invalid state" {
        val user = testDataHelper.getUser(index = 1)
        val order = testDataHelper.getOrder(userId = user.id, state = OrderState.COMPLETED)

        shouldThrowExactly<InvalidOrderStateException> {
            commandBus(
                PayForFreeOrderCommand(
                    userId = user.id,
                    orderId = order.id
                )
            )
        }
    }

    "should correctly complete free order" {
        val country = testDataHelper.getCountry()
        val user = testDataHelper.getUser(
            index = 1,
            email = "<EMAIL>",
            firstName = "Chuck",
            lastName = "Norris"
        )
        val wallet = testDataHelper.getWallet(userId = user.id)
        testDataHelper.getContact(
            userId = user.id,
            countryId = country.id,
            streetAddress = "Hrdinská 25",
            city = "Prague",
            postCode = "120 00"
        )
        val (challengePlan, _, _) = prepareChallengePlanWithStepsAndSettings()
        val order = testDataHelper.getOrder(
            userId = user.id,
            price = BigDecimal.ZERO,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id
        )

        every {
            createMT5TradingAccountMock(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns Result.success(CreateTradingAccountResult("12345"))

        commandBus(
            PayForFreeOrderCommand(
                userId = user.id,
                orderId = order.id
            )
        )

        val wallets = walletRepository.findAll()
        wallets shouldHaveSize 1
        wallets[0].let {
            it.balance shouldBeEqualComparingTo BigDecimal.ZERO
            it.userId shouldBe user.id
        }

        val transactions = transactionRepository.findAll()
        transactions shouldHaveSize 1
        transactions[0].also {
            it.type shouldBe TransactionType.CHALLENGE_ORDER
            it.walletId shouldBe wallet.id
            it.orderId shouldBe order.id
            it.amount shouldBeEqualComparingTo BigDecimal.ZERO
            it.status shouldBe TransactionStatus.COMPLETED
        }

        val orders = orderRepository.findAll()
        orders shouldHaveSize 1
        orders[0].let {
            it.state shouldBe OrderState.COMPLETED
            it.paidAt.shouldNotBeNull()
        }

        tradingAccountRepository.count() shouldBe 1

        verify {
            createInvoicePdfMock(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
            createMT5TradingAccountMock(
                suggestedAccountId = any(),
                email = any(),
                firstName = any(),
                lastName = any(),
                name = any(),
                password = any(),
                leverage = any(),
                tradingGroup = any(),
                startingBalance = any(),
                feedAccountId = any()
            )
        }
    }
})
