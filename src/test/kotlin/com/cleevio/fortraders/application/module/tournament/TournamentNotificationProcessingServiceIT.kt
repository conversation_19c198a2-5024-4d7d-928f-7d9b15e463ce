package com.cleevio.fortraders.application.module.tournament

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.notification.TournamentNotificationProcessingService
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailType
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailVariables
import com.cleevio.fortraders.domain.model.tournamentuser.constant.TournamentUserState
import io.kotest.matchers.shouldBe
import io.mockk.verify
import java.time.Instant

class TournamentNotificationProcessingServiceIT(
    private val underTest: TournamentNotificationProcessingService,
) : IntegrationTest({

    "should do nothing because tournament has not ended" {
        val tournament = testDataHelper.getTournament()
        val user = testDataHelper.getUser(index = 1)
        testDataHelper.getWallet(userId = user.id)
        testDataHelper.getTournamentUser(
            tournamentId = tournament.id,
            userId = user.id,
            state = TournamentUserState.VALID
        )
        prepareEmailTemplates()

        underTest.processTournamentEnded(tournament.id)

        verify(exactly = 0) {
            sendEmailNotificationMock(any(), any(), any(), any(), any())
        }
    }

    "should correctly send tournament ended email notification to all users" {
        val tournament1 = testDataHelper.getTournament(
            name = "Tournament 1",
            endsAt = Instant.now().minusSeconds(1)
        )
        val tournament2 = testDataHelper.getTournament()

        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2, firstName = "John", lastName = "Doe", email = "<EMAIL>")
        val user3 = testDataHelper.getUser(index = 3)

        testDataHelper.getWallet(userId = user1.id)
        testDataHelper.getWallet(userId = user2.id)
        testDataHelper.getWallet(userId = user3.id)

        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user1.id,
            state = TournamentUserState.BREACHED
        )
        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user2.id,
            state = TournamentUserState.VALID
        )
        testDataHelper.getTournamentUser(
            tournamentId = tournament2.id,
            userId = user3.id,
            state = TournamentUserState.VALID
        )
        prepareEmailTemplates()

        underTest.processTournamentEnded(tournament1.id)

        verify {
            sendTournamentEndedInAppNotificationMock(
                tournamentId = tournament1.id
            )
            sendEmailNotificationMock(
                email = "<EMAIL>",
                type = MailType.TOURNAMENT_ENDED,
                subject = "Subject TOURNAMENT_ENDED",
                htmlBody = "Html body TOURNAMENT_ENDED",
                mailVariables = withArg {
                    it[MailVariables.USER_FIRST_NAME] shouldBe "John"
                    it[MailVariables.USER_LAST_NAME] shouldBe "Doe"
                    it[MailVariables.TOURNAMENT_NAME] shouldBe "Tournament 1"
                }
            )
        }
    }

    "should do nothing because tournament has not started" {
        val tournament = testDataHelper.getTournament()
        val user = testDataHelper.getUser(index = 1)
        testDataHelper.getWallet(userId = user.id)
        testDataHelper.getTournamentUser(
            tournamentId = tournament.id,
            userId = user.id,
            state = TournamentUserState.VALID
        )
        prepareEmailTemplates()

        underTest.processTournamentStarted(tournament.id)

        verify(exactly = 0) {
            sendEmailNotificationMock(any(), any(), any(), any(), any())
        }
    }

    "should correctly send tournament started email notification to all users" {
        val tournament1 = testDataHelper.getTournament(
            name = "Tournament 1",
            startsAt = Instant.now().minusSeconds(1)
        )
        val tournament2 = testDataHelper.getTournament()

        val user1 = testDataHelper.getUser(index = 1, firstName = "Alice", lastName = "Smith", email = "<EMAIL>")
        val user2 = testDataHelper.getUser(index = 2, firstName = "John", lastName = "Doe", email = "<EMAIL>")
        val user3 = testDataHelper.getUser(index = 3)

        testDataHelper.getWallet(userId = user1.id)
        testDataHelper.getWallet(userId = user2.id)
        testDataHelper.getWallet(userId = user3.id)

        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user1.id,
            state = TournamentUserState.BREACHED
        )
        testDataHelper.getTournamentUser(
            tournamentId = tournament1.id,
            userId = user2.id,
            state = TournamentUserState.VALID
        )
        testDataHelper.getTournamentUser(
            tournamentId = tournament2.id,
            userId = user3.id,
            state = TournamentUserState.VALID
        )
        prepareEmailTemplates()

        underTest.processTournamentStarted(tournament1.id)

        verify {
            sendTournamentStartedInAppNotificationMock(
                tournamentId = tournament1.id
            )
            sendEmailNotificationMock(
                email = "<EMAIL>",
                type = MailType.TOURNAMENT_STARTED,
                subject = "Subject TOURNAMENT_STARTED",
                htmlBody = "Html body TOURNAMENT_STARTED",
                mailVariables = withArg {
                    it[MailVariables.USER_FIRST_NAME] shouldBe "Alice"
                    it[MailVariables.USER_LAST_NAME] shouldBe "Smith"
                    it[MailVariables.TOURNAMENT_NAME] shouldBe "Tournament 1"
                }
            )
            sendEmailNotificationMock(
                email = "<EMAIL>",
                type = MailType.TOURNAMENT_STARTED,
                subject = "Subject TOURNAMENT_STARTED",
                htmlBody = "Html body TOURNAMENT_STARTED",
                mailVariables = withArg {
                    it[MailVariables.USER_FIRST_NAME] shouldBe "John"
                    it[MailVariables.USER_LAST_NAME] shouldBe "Doe"
                    it[MailVariables.TOURNAMENT_NAME] shouldBe "Tournament 1"
                }
            )
        }
    }
})
