package com.cleevio.fortraders.application.module.tradingaccountforreview

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.domain.model.tradingaccountforreview.TradingAccountForReviewRepository
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import java.time.Instant

class TradingAccountForReviewProcessingServiceIT(
    private val tradingAccountForReviewRepository: TradingAccountForReviewRepository,
    private val underTest: TradingAccountForReviewProcessingService,
) : IntegrationTest({

    "should not create trading account for review when breach type is final" {
        val user = testDataHelper.getUser()
        val challenge = testDataHelper.getChallenge()
        val challengePlan = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            type = ChallengeStepType.EVALUATION,
            number = 2
        )
        val order = testDataHelper.getOrder(userId = user.id, challengeId = challenge.id, challengePlanId = challengePlan.id)
        val tradingAccount = testDataHelper.getTradingAccount(
            accountId = "1",
            orderId = order.id,
            challengeStepId = challengeStep.id,
            state = TradingAccountState.BREACHED,
            isLastEvaluationStep = true
        )

        underTest.createTradingAccountForReviewIfChallengePassed(
            tradingAccountId = tradingAccount.id,
            breachType = BreachType.MAX_DRAWDOWN
        )

        tradingAccountForReviewRepository.findAll().shouldBeEmpty()
    }

    "should not create trading account for review when account is not in last evaluation step" {
        val user = testDataHelper.getUser()
        val challenge = testDataHelper.getChallenge()
        val challengePlan = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            type = ChallengeStepType.EVALUATION,
            number = 1
        )
        val order = testDataHelper.getOrder(userId = user.id, challengeId = challenge.id, challengePlanId = challengePlan.id)
        val tradingAccount = testDataHelper.getTradingAccount(
            accountId = "1",
            orderId = order.id,
            challengeStepId = challengeStep.id,
            state = TradingAccountState.BREACHED,
            isLastEvaluationStep = false
        )

        underTest.createTradingAccountForReviewIfChallengePassed(
            tradingAccountId = tradingAccount.id,
            breachType = BreachType.PROFIT_TARGET
        )

        tradingAccountForReviewRepository.findAll().shouldBeEmpty()
    }

    "should create trading account for review when account is in last evaluation step and breach type is non-final" {
        val user = testDataHelper.getUser()
        val challenge = testDataHelper.getChallenge()
        val challengePlan = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            type = ChallengeStepType.EVALUATION,
            number = 2
        )
        val order = testDataHelper.getOrder(userId = user.id, challengeId = challenge.id, challengePlanId = challengePlan.id)
        val tradingAccount = testDataHelper.getTradingAccount(
            accountId = "1",
            orderId = order.id,
            challengeStepId = challengeStep.id,
            state = TradingAccountState.BREACHED,
            isLastEvaluationStep = true
        )
        testDataHelper.getBreach(
            tradingAccountId = tradingAccount.id,
            breachedAt = Instant.now(),
            type = BreachType.PROFIT_TARGET
        )

        underTest.createTradingAccountForReviewIfChallengePassed(
            tradingAccountId = tradingAccount.id,
            breachType = BreachType.PROFIT_TARGET
        )

        val tradingAccountsForReview = tradingAccountForReviewRepository.findAll()
        tradingAccountsForReview shouldHaveSize 1
        tradingAccountsForReview[0].tradingAccountId shouldBe tradingAccount.id
    }

    "should not create trading account for review after payout if not instant funding" {
        val user = testDataHelper.getUser()
        val challenge = testDataHelper.getChallenge()
        val challengePlan = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            type = ChallengeStepType.FUNDED,
            number = 2
        )
        val order = testDataHelper.getOrder(userId = user.id, challengeId = challenge.id, challengePlanId = challengePlan.id)
        val tradingAccount = testDataHelper.getTradingAccount(
            accountId = "1",
            orderId = order.id,
            challengeStepId = challengeStep.id,
            state = TradingAccountState.BREACHED,
            isLastEvaluationStep = true
        )

        underTest.createTradingAccountForReviewAfterPayoutIfNotReviewedAlready(
            tradingAccountId = tradingAccount.id
        )

        tradingAccountForReviewRepository.findAll().shouldBeEmpty()
    }

    "should not create trading account for review after payout if already reviewed" {
        val user = testDataHelper.getUser()
        val challenge = testDataHelper.getChallenge()
        val challengePlan = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            type = ChallengeStepType.FUNDED,
            number = 1
        )
        val order = testDataHelper.getOrder(userId = user.id, challengeId = challenge.id, challengePlanId = challengePlan.id)
        val tradingAccount = testDataHelper.getTradingAccount(
            accountId = "1",
            orderId = order.id,
            challengeStepId = challengeStep.id,
            state = TradingAccountState.BREACHED,
            isLastEvaluationStep = true
        )
        testDataHelper.getTradingAccountForReview(tradingAccountId = tradingAccount.id)

        underTest.createTradingAccountForReviewAfterPayoutIfNotReviewedAlready(
            tradingAccountId = tradingAccount.id
        )

        val tradingAccountForReview = tradingAccountForReviewRepository.findAll()
        tradingAccountForReview shouldHaveSize 1
        tradingAccountForReview[0].tradingAccountId shouldBe tradingAccount.id
    }

    "should create trading account for review after payout" {
        val user = testDataHelper.getUser()
        val challenge = testDataHelper.getChallenge()
        val challengePlan = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            type = ChallengeStepType.FUNDED,
            number = 1
        )
        val order = testDataHelper.getOrder(userId = user.id, challengeId = challenge.id, challengePlanId = challengePlan.id)
        val tradingAccount = testDataHelper.getTradingAccount(
            accountId = "1",
            orderId = order.id,
            challengeStepId = challengeStep.id,
            state = TradingAccountState.BREACHED,
            isLastEvaluationStep = true
        )

        underTest.createTradingAccountForReviewAfterPayoutIfNotReviewedAlready(
            tradingAccountId = tradingAccount.id
        )

        val tradingAccountForReview = tradingAccountForReviewRepository.findAll()
        tradingAccountForReview shouldHaveSize 1
        tradingAccountForReview[0].tradingAccountId shouldBe tradingAccount.id
    }
})
