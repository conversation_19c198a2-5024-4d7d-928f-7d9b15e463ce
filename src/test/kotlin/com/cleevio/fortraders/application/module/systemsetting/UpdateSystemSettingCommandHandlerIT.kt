package com.cleevio.fortraders.application.module.systemsetting

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.systemsetting.command.AdminUpdateSystemSettingCommand
import com.cleevio.fortraders.domain.model.systemSetting.SystemSettingRepository
import com.cleevio.fortraders.domain.model.systemSetting.constant.SystemSettingType
import io.kotest.matchers.shouldBe

class UpdateSystemSettingCommandHandlerIT(
    private val systemSettingRepository: SystemSettingRepository,
) : IntegrationTest({

    "should update system setting value" {
        // given
        val systemSetting = testDataHelper.getSystemSetting(
            type = SystemSettingType.COPY_TRADES_MAX_VOLUME_DIFF,
            value = "10",
            description = "Maximum volume difference for copy trades"
        )

        val command = AdminUpdateSystemSettingCommand(
            type = systemSetting.type,
            value = "20"
        )

        // when
        commandBus(command)

        // then
        val updatedSystemSetting = systemSettingRepository.findByType(systemSetting.type)
        updatedSystemSetting?.value shouldBe "20"
    }
})
