package com.cleevio.fortraders.application.module.orderblacklist

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.orderblacklist.command.AdminRemoveEmailFromOrderBlacklistCommand
import com.cleevio.fortraders.domain.model.orderblacklist.OrderBlacklistRepository
import io.kotest.matchers.shouldBe

class AdminRemoveEmailFromOrderBlacklistCommandHandlerIT(
    private val orderBlacklistRepository: OrderBlacklistRepository,
) : IntegrationTest({

    "should do nothing if email is not in blacklist" {
        testDataHelper.getOrderBlacklist(email = "<EMAIL>")

        commandBus(
            AdminRemoveEmailFromOrderBlacklistCommand(
                email = "<EMAIL>"
            )
        )

        orderBlacklistRepository.count() shouldBe 1
    }

    "should remove email from blacklist" {
        testDataHelper.getOrderBlacklist(email = "<EMAIL>")

        commandBus(
            AdminRemoveEmailFromOrderBlacklistCommand(
                email = "<EMAIL>"
            )
        )

        orderBlacklistRepository.count() shouldBe 0
    }
})
