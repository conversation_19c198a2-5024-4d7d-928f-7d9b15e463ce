package com.cleevio.fortraders.application.module.user

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.user.command.DeleteUserProfileImageCommand
import com.cleevio.fortraders.domain.model.file.FileRepository
import com.cleevio.fortraders.domain.model.file.constant.FileType
import com.cleevio.fortraders.domain.model.user.UserRepository
import com.cleevio.fortraders.domain.model.user.exception.UserNotFoundException
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import java.util.UUID

class DeleteUserProfileImageCommandHandlerIT(
    private val fileRepository: FileRepository,
    private val userRepository: UserRepository,
) : IntegrationTest({

    "should throw because user does not exist" {
        testDataHelper.getUser()

        shouldThrowExactly<UserNotFoundException> {
            commandBus(
                DeleteUserProfileImageCommand(
                    userId = UUID.randomUUID()
                )
            )
        }
    }

    "should do nothing because user does not have profile image" {
        val user = testDataHelper.getUser(profileImageFileId = null)

        commandBus(
            DeleteUserProfileImageCommand(
                userId = user.id
            )
        )

        val users = userRepository.findAll()
        users shouldHaveSize 1
        users[0].let {
            it.id shouldBe user.id
            it.profileImageFileId.shouldBeNull()
        }
    }

    "should correctly remove user profile image & file" {
        val profileImage1 = testDataHelper.getFile(type = FileType.USER_PROFILE_PICTURE)
        val profileImage2 = testDataHelper.getFile(type = FileType.USER_PROFILE_PICTURE)
        val user1 = testDataHelper.getUser(index = 1, profileImageFileId = profileImage1.id)
        val user2 = testDataHelper.getUser(index = 2, profileImageFileId = profileImage2.id)
        fileRepository.count() shouldBe 2

        commandBus(
            DeleteUserProfileImageCommand(
                userId = user2.id
            )
        )

        val users = userRepository.findAll().sortedBy { it.createdAt }
        users shouldHaveSize 2
        users[0].let {
            it.id shouldBe user1.id
            it.profileImageFileId shouldBe profileImage1.id
        }
        users[1].let {
            it.id shouldBe user2.id
            it.profileImageFileId.shouldBeNull()
        }

        val files = fileRepository.findAll()
        files shouldHaveSize 1
        files[0].id shouldBe profileImage1.id
    }
})
