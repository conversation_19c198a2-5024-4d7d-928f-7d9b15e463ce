package com.cleevio.fortraders.application.module.affiliate

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.affiliate.command.AdminPatchAffiliateUserCommand
import com.cleevio.fortraders.domain.model.affiliate.AffiliateRepository
import com.cleevio.fortraders.domain.model.affiliate.constant.AffiliateState
import com.cleevio.fortraders.domain.model.affiliate.exception.AffiliateNotFoundException
import com.cleevio.fortraders.domain.model.affiliate.exception.AffiliateWithGivenCouponCodeAlreadyExistsException
import com.cleevio.fortraders.toOptional
import io.kotest.assertions.throwables.shouldNotThrowAny
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe

class AdminPatchAffiliateUserCommandHandlerIT(
    private val affiliateRepository: AffiliateRepository,
) : IntegrationTest({

    "should do nothing because user is not affiliate" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        testDataHelper.getAffiliate(userId = user1.id)

        shouldThrowExactly<AffiliateNotFoundException> {
            commandBus(
                AdminPatchAffiliateUserCommand(
                    userId = user2.id,
                    state = null,
                    couponCode = null,
                    commissionPercentage = null,
                    discountAmountPercentage = null,
                    note = null
                )
            )
        }
    }

    "should throw because given coupon code already exists" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        testDataHelper.getAffiliate(
            userId = user1.id,
            couponCode = "COUPON-1"
        )
        testDataHelper.getAffiliate(
            userId = user2.id,
            couponCode = "COUPON-2"
        )

        shouldThrowExactly<AffiliateWithGivenCouponCodeAlreadyExistsException> {
            commandBus(
                AdminPatchAffiliateUserCommand(
                    userId = user1.id,
                    state = null,
                    couponCode = "coupon-2",
                    commissionPercentage = null,
                    discountAmountPercentage = null,
                    note = null
                )
            )
        }
    }

    "should not throw when patching coupon code to same as old one" {
        val user = testDataHelper.getUser(index = 1)
        testDataHelper.getAffiliate(
            userId = user.id,
            couponCode = "COUPON-1"
        )

        shouldNotThrowAny {
            commandBus(
                AdminPatchAffiliateUserCommand(
                    userId = user.id,
                    state = null,
                    couponCode = "coupon-1",
                    commissionPercentage = null,
                    discountAmountPercentage = null,
                    note = null
                )
            )
        }
    }

    "should correctly patch properties" {
        val user = testDataHelper.getUser(index = 1)
        testDataHelper.getAffiliate(
            userId = user.id,
            couponCode = "COUPON-1",
            commissionPercentage = 10,
            discountAmountPercentage = 20,
            note = "note",
            state = AffiliateState.ACTIVE
        )

        shouldNotThrowAny {
            commandBus(
                AdminPatchAffiliateUserCommand(
                    userId = user.id,
                    state = AffiliateState.INACTIVE,
                    couponCode = "new-coupon",
                    commissionPercentage = 15,
                    discountAmountPercentage = 25,
                    note = "new-note".toOptional()
                )
            )
        }

        val affiliates = affiliateRepository.findAll()
        affiliates shouldHaveSize 1
        affiliates[0].let {
            it.userId shouldBe user.id
            it.couponCode shouldBe "NEW-COUPON"
            it.commissionPercentage shouldBe 15
            it.discountAmountPercentage shouldBe 25
            it.note shouldBe "new-note"
            it.state shouldBe AffiliateState.INACTIVE
        }
    }
})
