package com.cleevio.fortraders.application.module.user

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.contact.ContactFinderService
import com.cleevio.fortraders.application.module.user.command.AdminUpdateUserDetailsCommand
import com.cleevio.fortraders.domain.model.country.exception.CountryNotFoundException
import com.cleevio.fortraders.domain.model.user.exception.UserNotFoundException
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.shouldBe

class AdminUpdateUserDetailsCommandHandlerIT(
    private val userFinderService: UserFinderService,
    private val contactFinderService: ContactFinderService,
) : IntegrationTest({

    "should correctly create new contact in case user does not have any" {
        val user = testDataHelper.getUser(
            firstName = "Donnie",
            lastName = "Dark"
        )
        val slovakia = testDataHelper.getCountry(name = "Slovakia", isoCode = "SK")
        val czechia = testDataHelper.getCountry(name = "Czechia", isoCode = "CZ")

        val command = AdminUpdateUserDetailsCommand(
            userId = user.id,
            firstName = "Johny",
            lastName = "Cash",
            countryId = czechia.id,
            streetAddress = "Obchodní 123",
            city = "Prague",
            postCode = "15000",
            phonePrefix = "+420",
            phoneNumber = "731999888"
        )

        commandBus(command)

        userFinderService.getById(user.id).let {
            it.firstName shouldBe "Johny"
            it.lastName shouldBe "Cash"
        }

        contactFinderService.getByUserId(user.id).let {
            it.countryId shouldBe czechia.id
            it.phonePrefix shouldBe "+420"
            it.phoneNumber shouldBe "731999888"
            it.streetAddress shouldBe "Obchodní 123"
            it.city shouldBe "Prague"
            it.postCode shouldBe "15000"
        }
    }

    "should correctly update all user details and return result" {
        val user = testDataHelper.getUser(
            firstName = "Donnie",
            lastName = "Dark"
        )
        val slovakia = testDataHelper.getCountry(name = "Slovakia", isoCode = "SK")
        val czechia = testDataHelper.getCountry(name = "Czechia", isoCode = "CZ")

        testDataHelper.getContact(
            userId = user.id,
            countryId = slovakia.id
        )
        val command = AdminUpdateUserDetailsCommand(
            userId = user.id,
            firstName = "Johny",
            lastName = "Cash",
            countryId = czechia.id,
            streetAddress = "Obchodní 123",
            city = "Prague",
            postCode = "15000",
            phonePrefix = "+420",
            phoneNumber = "731999888"
        )

        commandBus(command)

        userFinderService.getById(user.id).let {
            it.firstName shouldBe "Johny"
            it.lastName shouldBe "Cash"
        }

        contactFinderService.getByUserId(user.id).let {
            it.countryId shouldBe czechia.id
            it.phonePrefix shouldBe "+420"
            it.phoneNumber shouldBe "731999888"
            it.streetAddress shouldBe "Obchodní 123"
            it.city shouldBe "Prague"
            it.postCode shouldBe "15000"
        }
    }

    "should throw if user does not exist" {
        shouldThrowExactly<UserNotFoundException> {
            commandBus(
                AdminUpdateUserDetailsCommand(
                    userId = "63afc0a4-a6bd-4d6c-97e6-c091271dc786".toUUID(),
                    firstName = "Johny",
                    lastName = "Cash",
                    countryId = testDataHelper.getCountry().id,
                    streetAddress = "Obchodní 123",
                    city = "Prague",
                    postCode = "15000",
                    phonePrefix = "+420",
                    phoneNumber = "731999888"
                )
            )
        }
    }

    "should throw if country does not exist" {
        shouldThrowExactly<CountryNotFoundException> {
            commandBus(
                AdminUpdateUserDetailsCommand(
                    userId = testDataHelper.getUser().id,
                    firstName = "Johny",
                    lastName = "Cash",
                    countryId = "63afc0a4-a6bd-4d6c-97e6-c091271dc786".toUUID(),
                    streetAddress = "Obchodní 123",
                    city = "Prague",
                    postCode = "15000",
                    phonePrefix = "+420",
                    phoneNumber = "731999888"
                )
            )
        }
    }
})
