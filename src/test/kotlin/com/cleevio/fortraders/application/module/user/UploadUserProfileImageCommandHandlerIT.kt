package com.cleevio.fortraders.application.module.user

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.user.command.UploadUserProfileImageCommand
import com.cleevio.fortraders.domain.model.file.FileRepository
import com.cleevio.fortraders.domain.model.file.constant.FileType
import com.cleevio.fortraders.domain.model.user.UserRepository
import com.cleevio.fortraders.domain.model.user.exception.UserNotFoundException
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import org.springframework.web.multipart.MultipartFile
import java.util.UUID

class UploadUserProfileImageCommandHandlerIT(
    private val fileRepository: FileRepository,
    private val userRepository: UserRepository,
) : IntegrationTest({

    val fileMock = mockk<MultipartFile>()

    beforeAny {
        every { fileMock.originalFilename } returns "profile.png"
        every { fileMock.inputStream } returns "profile.png".byteInputStream()
    }

    afterAny {
        confirmVerified(fileMock)
        clearMocks(fileMock)
    }

    "should throw because user does not exit" {
        testDataHelper.getUser()

        shouldThrowExactly<UserNotFoundException> {
            commandBus(
                UploadUserProfileImageCommand(
                    userId = UUID.randomUUID(),
                    file = fileMock
                )
            )
        }
    }

    "should correctly upload user profile picture" {
        val user1 = testDataHelper.getUser(index = 1, profileImageFileId = null)
        val user2 = testDataHelper.getUser(index = 2, profileImageFileId = null)

        val result = commandBus(
            UploadUserProfileImageCommand(
                userId = user1.id,
                file = fileMock
            )
        )

        val files = fileRepository.findAll()
        files shouldHaveSize 1
        files[0].let {
            it.type shouldBe FileType.USER_PROFILE_PICTURE
            it.extension shouldBe "png"
        }

        val users = userRepository.findAll().sortedBy { it.createdAt }
        users shouldHaveSize 2
        users[0].let {
            it.id shouldBe user1.id
            it.profileImageFileId shouldBe files[0].id
        }
        users[1].let {
            it.id shouldBe user2.id
            it.profileImageFileId.shouldBeNull()
        }

        result.profileImageUrl shouldBe "https://example.com/user_profile_picture/${files[0].id}.png"
    }

    "should correctly upload user profile picture and delete old one" {
        val profileImage1 = testDataHelper.getFile(type = FileType.USER_PROFILE_PICTURE)
        val profileImage2 = testDataHelper.getFile(type = FileType.USER_PROFILE_PICTURE)
        val user1 = testDataHelper.getUser(index = 1, profileImageFileId = profileImage1.id)
        val user2 = testDataHelper.getUser(index = 2, profileImageFileId = profileImage2.id)

        val result = commandBus(
            UploadUserProfileImageCommand(
                userId = user1.id,
                file = fileMock
            )
        )

        val files = fileRepository.findAll().sortedBy { it.createdAt }
        files shouldHaveSize 2
        files[0].id shouldBe profileImage2.id
        files[1].let {
            it.type shouldBe FileType.USER_PROFILE_PICTURE
            it.extension shouldBe "png"
        }

        val users = userRepository.findAll().sortedBy { it.createdAt }
        users shouldHaveSize 2
        users[0].let {
            it.id shouldBe user1.id
            it.profileImageFileId shouldBe files[1].id
        }
        users[1].let {
            it.id shouldBe user2.id
            it.profileImageFileId shouldBe profileImage2.id
        }

        result.profileImageUrl shouldBe "https://example.com/user_profile_picture/${files[1].id}.png"
    }
})
