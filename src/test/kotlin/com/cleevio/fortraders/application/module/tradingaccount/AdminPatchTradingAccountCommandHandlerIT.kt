package com.cleevio.fortraders.application.module.tradingaccount

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.tradingaccount.command.AdminPatchTradingAccountCommand
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.tradingaccount.TradingAccountRepository
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.domain.model.tradingaccount.exception.TradingAccountNotFoundException
import com.cleevio.fortraders.shouldNotBeNullAndBeEqualComparingTo
import com.cleevio.fortraders.toOptional
import com.cleevio.fortraders.truncatedShouldBe
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.mockk.verify
import java.time.Instant
import java.util.Optional
import java.util.UUID

class AdminPatchTradingAccountCommandHandlerIT(
    private val tradingAccountRepository: TradingAccountRepository,
) : IntegrationTest({

    "should throw because trading account doesn't exist" {
        shouldThrowExactly<TradingAccountNotFoundException> {
            commandBus(
                AdminPatchTradingAccountCommand(
                    tradingAccountId = UUID.randomUUID(),
                    tradingGroup = "newGroup",
                    leverage = 25,
                    inactivityPeriodDays = Optional.of(5),
                    profitTarget = 20,
                    dailyDrawdown = Optional.of(10),
                    maxDrawdown = 50,
                    profitSplit = 90,
                    minProfitableTradingDays = 3,
                    maxTradingDays = Optional.of(10),
                    consistencyTarget = Optional.of(8),
                    nextPayoutAvailableAt = Instant.parse("2021-01-01T00:00:00Z"),
                    capTrailingDrawdownAfterPayout = true,
                    minimumPayoutLimit = 68.9.toBigDecimal(),
                    maximumPayoutLimit = 200.2.toBigDecimal().toOptional()
                )
            )
        }
    }

    "should correctly patch trading account" {
        val challengePlan = testDataHelper.getChallengePlan()
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id, type = ChallengeStepType.FUNDED)
        val order = testDataHelper.getOrder(challengeId = challengePlan.challengeId, challengePlanId = challengePlan.id)
        val tradingAccount = testDataHelper.getTradingAccount(
            challengeStepId = challengeStep.id,
            accountId = "12345",
            platform = PlatformType.TRADE_LOCKER,
            orderId = order.id,
            startingBalance = 5000.toBigDecimal(),
            isFundedChallengeStep = true,
            minProfitableTradingDays = 3,
            maximumPayoutLimit = 200.2.toBigDecimal()
        )
        tradingAccountRepository.count() shouldBe 1

        commandBus(
            AdminPatchTradingAccountCommand(
                tradingAccountId = tradingAccount.id,
                tradingGroup = "newGroup",
                leverage = 25,
                inactivityPeriodDays = Optional.of(5),
                profitTarget = null,
                dailyDrawdown = Optional.of(10),
                maxDrawdown = 50,
                profitSplit = 90,
                minProfitableTradingDays = null,
                maxTradingDays = Optional.empty(),
                consistencyTarget = Optional.empty(),
                nextPayoutAvailableAt = Instant.parse("2021-01-01T00:00:00Z"),
                capTrailingDrawdownAfterPayout = true,
                minimumPayoutLimit = 68.9.toBigDecimal(),
                maximumPayoutLimit = Optional.empty()
            )
        )

        val tradingAccounts = tradingAccountRepository.findAll()
        tradingAccounts shouldHaveSize 1
        tradingAccounts[0].let {
            it.tradingGroup shouldBe "newGroup"
            it.leverage shouldBe 25
            it.inactivityPeriodDays shouldBe 5
            it.profitTarget shouldBe null
            it.dailyDrawdown shouldBe 10
            it.maxDrawdown shouldBe 50
            it.profitSplit shouldBe 90
            it.minProfitableTradingDays shouldBe 3
            it.maxTradingDays shouldBe null
            it.consistencyTarget.shouldBeNull()
            it.nextPayoutAvailableAt truncatedShouldBe Instant.parse("2021-01-01T00:00:00Z")
            it.capTrailingDrawdownAfterPayout shouldBe true
            it.minimumPayoutLimit shouldNotBeNullAndBeEqualComparingTo 68.9.toBigDecimal()
            it.maximumPayoutLimit.shouldBeNull()
        }

        verify {
            updateTradeLockerTradingGroupMock(
                accountId = "12345",
                tradingGroup = "newGroup"
            )
        }
    }
})
