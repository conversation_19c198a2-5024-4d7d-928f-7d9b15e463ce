package com.cleevio.fortraders.application.module.instrument

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.instrument.command.AdminCreateInstrumentCommand
import com.cleevio.fortraders.domain.model.instrument.InstrumentRepository
import com.cleevio.fortraders.domain.model.instrument.constant.InstrumentType
import com.cleevio.fortraders.domain.model.instrument.exception.InstrumentAlreadyExistsException
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import java.math.BigDecimal

class AdminCreateInstrumentCommandHandlerIT(
    private val instrumentRepository: InstrumentRepository,
) : IntegrationTest({

    "should correctly create instrument" {
        instrumentRepository.count() shouldBe 0

        commandBus(
            AdminCreateInstrumentCommand(
                type = InstrumentType.FOREX,
                name = "American dollar to Japanese yen",
                ticker = "USD-JPY",
                spreadPercentage = 10.5.toBigDecimal()
            )
        )

        val instruments = instrumentRepository.findAll()
        instruments shouldHaveSize 1
        instruments[0].let {
            it.type shouldBe InstrumentType.FOREX
            it.name shouldBe "American dollar to Japanese yen"
            it.ticker shouldBe "USD-JPY"
            it.spreadPercentage shouldBeEqualComparingTo 10.5.toBigDecimal()
        }
    }

    "should throw because instrument already exists with given type and tickers" {
        instrumentRepository.count() shouldBe 0

        val command = AdminCreateInstrumentCommand(
            type = InstrumentType.FOREX,
            name = "American dollar to Japanese yen",
            ticker = "USD-JPY",
            spreadPercentage = BigDecimal.ZERO
        )

        commandBus(command)
        val instruments = instrumentRepository.findAll()
        instruments shouldHaveSize 1
        instruments[0].let {
            it.type shouldBe InstrumentType.FOREX
            it.name shouldBe "American dollar to Japanese yen"
            it.ticker shouldBe "USD-JPY"
            it.spreadPercentage shouldBeEqualComparingTo BigDecimal.ZERO
        }

        shouldThrowExactly<InstrumentAlreadyExistsException> {
            commandBus(command.copy(name = "Another name"))
        }
    }
})
