package com.cleevio.fortraders.application.module.payout

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.payout.command.CreatePayoutCommand
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.challengestep.exception.ChallengeStepNotFundedException
import com.cleevio.fortraders.domain.model.order.constant.OrderState
import com.cleevio.fortraders.domain.model.order.constant.PayoutsType
import com.cleevio.fortraders.domain.model.order.exception.InvalidOrderStateException
import com.cleevio.fortraders.domain.model.order.exception.OrderNotFoundException
import com.cleevio.fortraders.domain.model.payout.PayoutRepository
import com.cleevio.fortraders.domain.model.payout.constant.PayoutState
import com.cleevio.fortraders.domain.model.payout.exception.PayoutAmountExceedsMaximumException
import com.cleevio.fortraders.domain.model.payout.exception.PayoutAmountLessThanAllowedMinimumException
import com.cleevio.fortraders.domain.model.payout.exception.PayoutConsistencyTargetNotSatisfiedException
import com.cleevio.fortraders.domain.model.payout.exception.PayoutMinProfitableTradingDaysNotSatisfiedException
import com.cleevio.fortraders.domain.model.payout.exception.PayoutMinimumDurationNotSatisfiedException
import com.cleevio.fortraders.domain.model.payout.exception.PayoutRequestAlreadyExistsException
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.domain.model.tradingaccount.exception.TradingAccountHasOpenTradesException
import com.cleevio.fortraders.domain.model.tradingaccount.exception.TradingAccountIsPausedException
import com.cleevio.fortraders.domain.model.tradingaccount.exception.TradingAccountNotFoundException
import com.cleevio.fortraders.domain.model.tradingaccount.exception.TradingAccountProfitNotEnoughException
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.extensions.time.withConstantNow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify
import java.time.Instant
import java.time.LocalDate

class CreatePayoutCommandHandlerIT(
    private val payoutRepository: PayoutRepository,
) : IntegrationTest({

    "should throw because order does not belong to user" {
        val user1 = testDataHelper.getUser(index = 1)
        val user2 = testDataHelper.getUser(index = 2)
        val order = testDataHelper.getOrder(userId = user1.id)
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = order.challengePlanId)
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order.id,
            challengeStepId = challengeStep.id,
            accountId = "1"
        )

        shouldThrowExactly<OrderNotFoundException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order.id,
                    userId = user2.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 101.1.toBigDecimal()
                )
            )
        }
    }

    "should throw because order is not completed" {
        val user1 = testDataHelper.getUser(index = 1)
        val order = testDataHelper.getOrder(userId = user1.id, state = OrderState.PENDING)
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = order.challengePlanId)
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order.id,
            challengeStepId = challengeStep.id,
            accountId = "1"
        )

        shouldThrowExactly<InvalidOrderStateException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 101.1.toBigDecimal()
                )
            )
        }
    }

    "should throw because trading account does not belong to given order" {
        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val order2 = testDataHelper.getOrder(
            userId = user1.id,
            state = OrderState.COMPLETED,
            challengeId = order1.challengeId,
            challengePlanId = order1.challengePlanId
        )
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = order1.challengePlanId)
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1"
        )

        shouldThrowExactly<TradingAccountNotFoundException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order2.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 101.1.toBigDecimal()
                )
            )
        }
    }

    "should throw because trading account does not have enough profit" {
        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = order1.challengePlanId)
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 100.0.toBigDecimal(),
            currentBalance = 201.05.toBigDecimal()
        )

        shouldThrowExactly<TradingAccountProfitNotEnoughException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order1.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 101.1.toBigDecimal()
                )
            )
        }
    }

    "should throw because trading account is paused" {
        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = order1.challengePlanId)
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 200.0.toBigDecimal(),
            currentBalance = 401.05.toBigDecimal(),
            state = TradingAccountState.PAUSED_DAILY_PAUSE
        )

        shouldThrowExactly<TradingAccountIsPausedException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order1.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 101.1.toBigDecimal()
                )
            )
        }
    }

    "should throw because trading account is not in funded step" {
        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = order1.challengePlanId,
            type = ChallengeStepType.EVALUATION
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 100.0.toBigDecimal(),
            currentBalance = 201.1.toBigDecimal()
        )

        shouldThrowExactly<ChallengeStepNotFundedException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order1.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 101.1.toBigDecimal()
                )
            )
        }
    }

    "should throw because requested payout already exists for given account" {
        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = order1.challengePlanId,
            type = ChallengeStepType.FUNDED
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 100.0.toBigDecimal(),
            currentBalance = 201.1.toBigDecimal()
        )
        testDataHelper.getPayout(
            tradingAccountId = tradingAccount.id,
            amount = 100.0.toBigDecimal(),
            state = PayoutState.REQUESTED
        )

        shouldThrowExactly<PayoutRequestAlreadyExistsException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order1.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 101.1.toBigDecimal()
                )
            )
        }
    }

    "should throw because account has open trades" {
        every { getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions(any()) } returns true

        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = order1.challengePlanId,
            type = ChallengeStepType.FUNDED
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 100.0.toBigDecimal(),
            currentBalance = 201.1.toBigDecimal()
        )

        shouldThrowExactly<TradingAccountHasOpenTradesException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order1.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 99.9.toBigDecimal()
                )
            )
        }

        verify {
            getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions("1")
        }
    }

    "should throw because payout amount is less than allowed minimum" {
        every { getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions(any()) } returns false

        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = order1.challengePlanId,
            type = ChallengeStepType.FUNDED
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 100.0.toBigDecimal(),
            currentBalance = 201.1.toBigDecimal(),
            isFundedChallengeStep = true,
            payoutsType = PayoutsType.WEEKLY
        )

        shouldThrowExactly<PayoutAmountLessThanAllowedMinimumException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order1.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 99.9.toBigDecimal()
                )
            )
        }

        verify {
            getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions("1")
        }
    }

    "should throw because payout amount is more than allowed maximum" {
        every { getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions(any()) } returns false

        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = order1.challengePlanId,
            type = ChallengeStepType.FUNDED
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 22.0.toBigDecimal(),
            currentBalance = 200.0.toBigDecimal(),
            equity = 200.0.toBigDecimal(),
            isFundedChallengeStep = true,
            payoutsType = PayoutsType.WEEKLY,
            maximumPayoutLimit = 100.2.toBigDecimal()
        )

        shouldThrowExactly<PayoutAmountExceedsMaximumException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order1.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 100.3.toBigDecimal()
                )
            )
        }

        verify {
            getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions("1")
        }
    }

    "should throw because payout is requested in duration less than allowed with no previous payout" {
        every { getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions(any()) } returns false

        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = order1.challengePlanId,
            type = ChallengeStepType.FUNDED
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 100.0.toBigDecimal(),
            currentBalance = 201.1.toBigDecimal(),
            payoutsType = PayoutsType.BI_WEEKLY,
            createdAt = Instant.parse("2021-10-01T10:00:01Z"),
            isFundedChallengeStep = true
        )

        shouldThrowExactly<PayoutMinimumDurationNotSatisfiedException> {
            withConstantNow(Instant.parse("2021-10-15T10:00:00Z")) {
                commandBus(
                    CreatePayoutCommand(
                        orderId = order1.id,
                        userId = user1.id,
                        tradingAccountId = tradingAccount.id,
                        amount = 100.1.toBigDecimal()
                    )
                )
            }
        }

        verify {
            getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions("1")
        }
    }

    "should throw because payout is requested in duration less than allowed with approved previous payout" {
        every { getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions(any()) } returns false

        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = order1.challengePlanId,
            type = ChallengeStepType.FUNDED
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 100.0.toBigDecimal(),
            currentBalance = 201.1.toBigDecimal(),
            payoutsType = PayoutsType.BI_WEEKLY,
            createdAt = Instant.parse("2021-10-14T10:00:01Z"),
            isFundedChallengeStep = true,
            nextPayoutAvailableAt = Instant.parse("2021-10-15T10:00:00.001Z")
        )

        shouldThrowExactly<PayoutMinimumDurationNotSatisfiedException> {
            withConstantNow(Instant.parse("2021-10-15T10:00:00Z")) {
                commandBus(
                    CreatePayoutCommand(
                        orderId = order1.id,
                        userId = user1.id,
                        tradingAccountId = tradingAccount.id,
                        amount = 100.1.toBigDecimal()
                    )
                )
            }
        }

        verify {
            getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions("1")
        }
    }

    "should throw because user does not have enough profitable trading days since last payout" {
        every { getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions(any()) } returns false

        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = order1.challengePlanId,
            type = ChallengeStepType.FUNDED
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 100.0.toBigDecimal(),
            currentBalance = 201.1.toBigDecimal(),
            payoutsType = PayoutsType.BI_WEEKLY,
            createdAt = Instant.parse("2021-10-14T10:00:01Z"),
            isFundedChallengeStep = true,
            nextPayoutAvailableAt = Instant.parse("2020-01-01T10:00:00.001Z"),
            minProfitableTradingDays = 2
        )
        testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount.id,
            profitableTradingDays = 1,
            date = LocalDate.parse("2021-10-16")
        )
        testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount.id,
            profitableTradingDays = 0,
            date = LocalDate.parse("2021-10-15")
        )
        testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount.id,
            profitableTradingDays = 2,
            date = LocalDate.parse("2021-10-14")
        )

        shouldThrowExactly<PayoutMinProfitableTradingDaysNotSatisfiedException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order1.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 100.1.toBigDecimal()
                )
            )
        }

        verify {
            getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions("1")
        }
    }

    "should throw because user has not completed consistency target" {
        every { getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions(any()) } returns false

        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = order1.challengePlanId,
            type = ChallengeStepType.FUNDED
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 22.0.toBigDecimal(),
            currentBalance = 200.0.toBigDecimal(),
            equity = 200.0.toBigDecimal(),
            consistencyTarget = 50,
            payoutsType = PayoutsType.BI_WEEKLY,
            createdAt = Instant.parse("2021-10-14T10:00:01Z"),
            isFundedChallengeStep = true,
            nextPayoutAvailableAt = Instant.parse("2020-01-01T10:00:00.001Z"),
            minProfitableTradingDays = 3,
            startingBalanceThisPayoutCycle = 100.0.toBigDecimal()
        )
        testDataHelper.getPayout(
            tradingAccountId = tradingAccount.id,
            state = PayoutState.APPROVED,
            createdAt = Instant.parse("2021-10-15T10:00:00Z")
        )
        testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount.id,
            dailyProfit = 50.0.toBigDecimal(),
            profitableTradingDays = 3,
            date = LocalDate.parse("2021-10-16"),
            createdAt = Instant.parse("2021-10-16T22:00:00Z")
        )
        testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount.id,
            dailyProfit = 49.9.toBigDecimal(),
            profitableTradingDays = 1,
            date = LocalDate.parse("2021-10-15"),
            createdAt = Instant.parse("2021-10-15T22:00:00Z")
        )
        testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount.id,
            dailyProfit = 11155.5.toBigDecimal(),
            profitableTradingDays = 0,
            date = LocalDate.parse("2021-10-14"),
            createdAt = Instant.parse("2021-10-14T22:00:00Z")
        )

        shouldThrowExactly<PayoutConsistencyTargetNotSatisfiedException> {
            commandBus(
                CreatePayoutCommand(
                    orderId = order1.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 100.0.toBigDecimal()
                )
            )
        }

        verify {
            getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions("1")
        }
    }

    "should create payout" {
        every { getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions(any()) } returns false
        every { closeAllMT5TradingAccountOpenTradesMock(any()) } returns Result.success(Unit)

        val user1 = testDataHelper.getUser(index = 1)
        val order1 = testDataHelper.getOrder(userId = user1.id, state = OrderState.COMPLETED)
        val challengeStep = testDataHelper.getChallengeStep(
            challengePlanId = order1.challengePlanId,
            type = ChallengeStepType.FUNDED
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order1.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            startingBalance = 22.0.toBigDecimal(),
            currentBalance = 200.0.toBigDecimal(),
            equity = 200.0.toBigDecimal(),
            consistencyTarget = 50,
            payoutsType = PayoutsType.BI_WEEKLY,
            createdAt = Instant.parse("2021-10-01T10:00:00Z"),
            isFundedChallengeStep = true,
            nextPayoutAvailableAt = Instant.parse("2021-10-15T10:00:00Z"),
            minProfitableTradingDays = 3,
            startingBalanceThisPayoutCycle = 100.0.toBigDecimal()
        )
        val existingPayout = testDataHelper.getPayout(
            tradingAccountId = tradingAccount.id,
            amount = 100.0.toBigDecimal(),
            state = PayoutState.APPROVED,
            createdAt = Instant.parse("2021-10-15T10:00:00Z")
        )
        testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount.id,
            dailyProfit = 49.9.toBigDecimal(),
            balance = 150.1.toBigDecimal(),
            profitableTradingDays = 3,
            date = LocalDate.parse("2021-10-16"),
            createdAt = Instant.parse("2021-10-16T22:00:00Z")
        )
        testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount.id,
            dailyProfit = 49.9.toBigDecimal(),
            profitableTradingDays = 2,
            date = LocalDate.parse("2021-10-15"),
            createdAt = Instant.parse("2021-10-15T22:00:00Z")
        )
        testDataHelper.getTradingAccountHistory(
            tradingAccountId = tradingAccount.id,
            dailyProfit = 11155.5.toBigDecimal(),
            profitableTradingDays = 1,
            date = LocalDate.parse("2021-10-14"),
            createdAt = Instant.parse("2021-10-15T09:59:59.999Z")
        )

        withConstantNow(Instant.parse("2021-10-15T10:00:00.001Z")) {
            commandBus(
                CreatePayoutCommand(
                    orderId = order1.id,
                    userId = user1.id,
                    tradingAccountId = tradingAccount.id,
                    amount = 100.0.toBigDecimal()
                )
            )
        }

        val payouts = payoutRepository.findAll().sortedBy { it.createdAt }
        payouts shouldHaveSize 2
        payouts[0].id shouldBe existingPayout.id
        payouts[1].let {
            it.state shouldBe PayoutState.REQUESTED
            it.tradingAccountId shouldBe tradingAccount.id
            it.amount shouldBeEqualComparingTo 100.0.toBigDecimal()
        }

        verify {
            getMT5TradingAccountOpenPositionsCountMock.hasOpenPositions(accountId = "1")
            closeAllMT5TradingAccountOpenTradesMock(accountId = "1")
            restrictMT5TradingAccountMock(accountId = "1")
        }
    }
})
