package com.cleevio.fortraders.application.module.mailtemplate

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.mailtemplate.command.AdminCreateOrUpdateMailTemplateCommand
import com.cleevio.fortraders.domain.model.mailtemplate.MailTemplateRepository
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailType
import com.cleevio.fortraders.domain.model.user.constant.PreferredLanguage
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe

class AdminCreateOrUpdateMailTemplateCommandHandlerIT(
    private val mailTemplateRepository: MailTemplateRepository,
) : IntegrationTest({

    "should correctly create new mail template" {
        val existingMailTemplate = testDataHelper.getMailTemplate(
            type = MailType.PAYMENT_FAILED,
            language = PreferredLanguage.ENGLISH
        )

        commandBus(
            AdminCreateOrUpdateMailTemplateCommand(
                type = MailType.PAYMENT_FAILED,
                language = PreferredLanguage.SPANISH,
                subject = "Spanish subject",
                htmlBody = "Spanish html body",
                jsonBody = "Spanish json body"
            )
        )

        val mailTemplates = mailTemplateRepository.findAll()
        mailTemplates shouldHaveSize 2
        mailTemplates[0].id shouldBe existingMailTemplate.id
        mailTemplates[1].let {
            it.type shouldBe MailType.PAYMENT_FAILED
            it.language shouldBe PreferredLanguage.SPANISH
            it.subject shouldBe "Spanish subject"
            it.htmlBody shouldBe "Spanish html body"
            it.jsonBody shouldBe "Spanish json body"
        }
    }

    "should correctly update existing mail template" {
        val existingMailTemplate = testDataHelper.getMailTemplate(
            type = MailType.PAYMENT_FAILED,
            language = PreferredLanguage.ENGLISH
        )

        commandBus(
            AdminCreateOrUpdateMailTemplateCommand(
                type = MailType.PAYMENT_FAILED,
                language = PreferredLanguage.ENGLISH,
                subject = "New subject",
                htmlBody = "New html body",
                jsonBody = "New json body"
            )
        )

        val mailTemplates = mailTemplateRepository.findAll()
        mailTemplates shouldHaveSize 1
        mailTemplates[0].let {
            it.id shouldBe existingMailTemplate.id
            it.type shouldBe MailType.PAYMENT_FAILED
            it.language shouldBe PreferredLanguage.ENGLISH
            it.subject shouldBe "New subject"
            it.htmlBody shouldBe "New html body"
            it.jsonBody shouldBe "New json body"
        }
    }
})
