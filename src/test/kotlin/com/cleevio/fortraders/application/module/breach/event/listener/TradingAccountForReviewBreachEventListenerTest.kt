package com.cleevio.fortraders.application.module.breach.event.listener

import com.cleevio.fortraders.UnitTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.breach.BreachService
import com.cleevio.fortraders.domain.model.tradingaccountforreview.event.TradingAccountForReviewDeclinedEvent
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify

class TradingAccountForReviewBreachEventListenerTest : UnitTest({

    val breachService = mockk<BreachService>()
    val underTest = TradingAccountForReviewBreachEventListener(
        breachService = breachService
    )

    afterAny {
        confirmVerified(breachService)
    }

    "should correctly call updateBreachToManualBreachType when handling TradingAccountForReviewDeclinedEvent" {
        every { breachService.updateBreachToManualBreachType(any(), any(), any()) } just runs

        underTest.handleTradingAccountForReviewDeclinedEvent(
            TradingAccountForReviewDeclinedEvent(
                id = "cad9a7d7-64aa-465d-9c40-55a993abcf8c".toUUID(),
                tradingAccountId = "1d62f71a-2767-4e4e-9bf9-304e701d03c8".toUUID(),
                internalReason = "Internal reason for declining",
                externalReason = "External reason for declining"
            )
        )

        verify {
            breachService.updateBreachToManualBreachType(
                tradingAccountId = "1d62f71a-2767-4e4e-9bf9-304e701d03c8".toUUID(),
                internalReason = "Internal reason for declining",
                externalReason = "External reason for declining"
            )
        }
    }
})
