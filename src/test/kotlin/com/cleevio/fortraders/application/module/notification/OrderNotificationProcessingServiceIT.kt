package com.cleevio.fortraders.application.module.notification

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.discountcode.DiscountCodeFinderService
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanCategory
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountUsageType
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailType
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailVariables
import com.cleevio.fortraders.domain.model.order.constant.OrderState
import com.cleevio.fortraders.domain.model.systemSetting.constant.SystemSettingType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import com.cleevio.fortraders.setAndReturnPrivateProperty
import io.kotest.extensions.time.withConstantNow
import io.kotest.matchers.shouldBe
import io.mockk.verify
import java.time.Instant

class OrderNotificationProcessingServiceIT(
    private val underTest: OrderNotificationProcessingService,
    private val discountCodeFinderService: DiscountCodeFinderService,
) : IntegrationTest({

    "should do nothing because order is not completed" {
        val order = testDataHelper.getOrder(state = OrderState.CANCELLED)

        underTest.processOrderPaymentCompleted(order.id)

        verify(exactly = 0) {
            adminSendOrderPaidNotificationMock(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }
    }

    "should correctly send admin notification about completed order" {
        val discountCode = testDataHelper.getDiscountCode(code = "DISCOUNT_CODE", usageType = DiscountUsageType.GLOBAL)
        val user1 = testDataHelper.getUser(index = 1, firstName = "John", lastName = "Doe", email = "<EMAIL>")
        val user2 = testDataHelper.getUser(index = 2, firstName = "Foo", lastName = "Bar", email = "<EMAIL>")
        val wallet1 = testDataHelper.getWallet(userId = user1.id)
        val wallet2 = testDataHelper.getWallet(userId = user2.id)
        testDataHelper.getTransaction(walletId = wallet1.id, amount = 1.1.toBigDecimal(), type = TransactionType.DEPOSIT) {
            it.changeStatus(TransactionStatus.COMPLETED)
        }
        testDataHelper.getTransaction(
            walletId = wallet1.id,
            amount = 2.1.toBigDecimal(),
            type = TransactionType.RECURRING_DEPOSIT
        ) {
            it.changeStatus(TransactionStatus.COMPLETED)
        }
        testDataHelper.getTransaction(
            walletId = wallet1.id,
            amount = 999.9.toBigDecimal(),
            type = TransactionType.CHALLENGE_ORDER
        )
        testDataHelper.getTransaction(
            walletId = wallet2.id,
            amount = 999.9.toBigDecimal(),
            type = TransactionType.CHALLENGE_ORDER
        )
        val country1 = testDataHelper.getCountry(isoCode = "CZ", name = "Czechia")
        val country2 = testDataHelper.getCountry(isoCode = "SK", name = "Slovakia")
        testDataHelper.getContact(userId = user1.id, city = "City 1", postCode = "12345", countryId = country1.id)
        testDataHelper.getContact(userId = user2.id, city = "City 2", postCode = "54321", countryId = country2.id)
        val challenge1 = testDataHelper.getChallenge(index = 1, name = "Challenge 1")
        val challenge2 = testDataHelper.getChallenge(index = 2, name = "Challenge 2")
        val challengePlan1 = testDataHelper.getChallengePlan(index = 1, title = "Challenge Plan 1", challengeId = challenge1.id)
        val challengePlan2 = testDataHelper.getChallengePlan(index = 2, title = "Challenge Plan 2", challengeId = challenge2.id)
        val order1 = testDataHelper.getOrder(
            state = OrderState.COMPLETED,
            userId = user1.id,
            challengeId = challenge1.id,
            challengePlanId = challengePlan1.id,
            price = 100.1.toBigDecimal(),
            paidAt = Instant.parse("2022-12-24T18:15:30Z"),
            platform = PlatformType.META_TRADER_5,
            discountCodeId = discountCode.id
        )
        val order2 = testDataHelper.getOrder(
            state = OrderState.COMPLETED,
            userId = user2.id,
            challengeId = challenge2.id,
            challengePlanId = challengePlan2.id,
            price = 200.2.toBigDecimal(),
            paidAt = Instant.parse("2022-12-24T01:09:30Z")
        )
        val order3 = testDataHelper.getOrder(
            state = OrderState.COMPLETED,
            userId = user1.id,
            challengeId = challenge1.id,
            challengePlanId = challengePlan1.id,
            price = 300.5.toBigDecimal(),
            paidAt = Instant.parse("2022-12-31T11:59:59Z")
        )
        val order4 = testDataHelper.getOrder(
            state = OrderState.COMPLETED,
            userId = user2.id,
            challengeId = challenge2.id,
            challengePlanId = challengePlan2.id,
            price = 9999999.5.toBigDecimal(),
            paidAt = Instant.parse("2023-01-01T00:00:01Z")
        )
        testDataHelper.getTransaction(
            walletId = wallet1.id,
            orderId = order1.id,
            amount = 99.1.toBigDecimal(),
            type = TransactionType.DEPOSIT
        ) {
            it.registerProviderPayment(provider = PaymentGatewayProvider.MY_FATOORAH, providerPaymentId = "123456")
            it.changeStatus(TransactionStatus.COMPLETED)
        }

        withConstantNow(Instant.parse("2022-12-24T21:05:01Z")) {
            underTest.processOrderPaymentCompleted(order1.id)
        }

        verify {
            adminSendOrderPaidNotificationMock(
                orderId = order1.id,
                orderName = "Challenge 1 - Challenge Plan 1",
                orderPrice = 100.1.toBigDecimal(),
                creditsUsed = 0.toBigDecimal(),
                affiliateCodeUsed = null,
                discountCodeUsed = "DISCOUNT_CODE",
                paymentProvider = PaymentGatewayProvider.MY_FATOORAH.toHumanReadableFormat(),
                platformType = PlatformType.META_TRADER_5,
                userFullName = "John Doe",
                email = "<EMAIL>",
                city = "City 1",
                postCode = "12345",
                countryName = "Czechia",
                countryIsoCode = "CZ",
                userOrdersTotal = 2,
                userOrdersRevenueTotal = 999.9.toBigDecimal(),
                ordersThisDay = 2,
                ordersRevenueThisDay = 300.3.toBigDecimal(),
                ordersThisMonth = 3,
                ordersRevenueThisMonth = 600.8.toBigDecimal(),
                isRecurringPayment = false
            )
        }
    }

    "should correctly send admin notification for recurring payment completed order" {
        val discountCode = testDataHelper.getDiscountCode(code = "DISCOUNT_CODE", usageType = DiscountUsageType.GLOBAL)
        val user1 = testDataHelper.getUser(index = 1, firstName = "John", lastName = "Doe", email = "<EMAIL>")
        val user2 = testDataHelper.getUser(index = 2, firstName = "Foo", lastName = "Bar", email = "<EMAIL>")
        val wallet1 = testDataHelper.getWallet(userId = user1.id)
        val wallet2 = testDataHelper.getWallet(userId = user2.id)
        testDataHelper.getTransaction(walletId = wallet1.id, amount = 1.1.toBigDecimal(), type = TransactionType.DEPOSIT) {
            it.changeStatus(TransactionStatus.COMPLETED)
        }
        testDataHelper.getTransaction(
            walletId = wallet1.id,
            amount = 2.1.toBigDecimal(),
            type = TransactionType.RECURRING_DEPOSIT
        ) {
            it.changeStatus(TransactionStatus.COMPLETED)
        }
        testDataHelper.getTransaction(
            walletId = wallet1.id,
            amount = 999.9.toBigDecimal(),
            type = TransactionType.CHALLENGE_ORDER
        )
        testDataHelper.getTransaction(
            walletId = wallet2.id,
            amount = 999.9.toBigDecimal(),
            type = TransactionType.CHALLENGE_ORDER
        )
        val country1 = testDataHelper.getCountry(isoCode = "CZ", name = "Czechia")
        val country2 = testDataHelper.getCountry(isoCode = "SK", name = "Slovakia")
        testDataHelper.getContact(userId = user1.id, city = "City 1", postCode = "12345", countryId = country1.id)
        testDataHelper.getContact(userId = user2.id, city = "City 2", postCode = "54321", countryId = country2.id)
        val challenge1 = testDataHelper.getChallenge(index = 1, name = "Challenge 1")
        val challenge2 = testDataHelper.getChallenge(index = 2, name = "Challenge 2")
        val challengePlan1 = testDataHelper.getChallengePlan(
            index = 1,
            title = "Challenge Plan 1",
            challengeId = challenge1.id,
            category = ChallengePlanCategory.PRO
        )
        val challengePlan2 = testDataHelper.getChallengePlan(index = 2, title = "Challenge Plan 2", challengeId = challenge2.id)
        val challengeStep1 = testDataHelper.getChallengeStep(challengePlanId = challengePlan1.id, number = 1)

        val order1 = testDataHelper.getOrder(
            state = OrderState.COMPLETED,
            userId = user1.id,
            challengeId = challenge1.id,
            challengePlanId = challengePlan1.id,
            price = 100.1.toBigDecimal(),
            paidAt = Instant.parse("2022-12-24T18:15:30Z"),
            platform = PlatformType.META_TRADER_5,
            discountCodeId = discountCode.id
        )
        val order2 = testDataHelper.getOrder(
            state = OrderState.COMPLETED,
            userId = user2.id,
            challengeId = challenge2.id,
            challengePlanId = challengePlan2.id,
            price = 200.2.toBigDecimal(),
            paidAt = Instant.parse("2022-12-24T01:09:30Z")
        )
        val order3 = testDataHelper.getOrder(
            state = OrderState.COMPLETED,
            userId = user1.id,
            challengeId = challenge1.id,
            challengePlanId = challengePlan1.id,
            price = 300.5.toBigDecimal(),
            paidAt = Instant.parse("2022-12-31T11:59:59Z")
        )
        val order4 = testDataHelper.getOrder(
            state = OrderState.COMPLETED,
            userId = user2.id,
            challengeId = challenge2.id,
            challengePlanId = challengePlan2.id,
            price = 9999999.5.toBigDecimal(),
            paidAt = Instant.parse("2023-01-01T00:00:01Z")
        )
        testDataHelper.getTradingAccount(
            accountId = "2",
            challengeStepId = challengeStep1.id,
            orderId = order1.id,
            platform = PlatformType.DX_TRADE,
            state = TradingAccountState.ACTIVE
        )

        testDataHelper.getTransaction(
            walletId = wallet1.id,
            orderId = order1.id,
            amount = 99.1.toBigDecimal(),
            type = TransactionType.DEPOSIT
        ) {
            it.registerProviderPayment(provider = PaymentGatewayProvider.MY_FATOORAH, providerPaymentId = "123456")
            it.changeStatus(TransactionStatus.COMPLETED)
        }

        withConstantNow(Instant.parse("2022-12-24T21:05:01Z")) {
            underTest.processOrderRecurringPaymentCompleted(order1.id)
        }

        verify {
            adminSendOrderPaidNotificationMock(
                orderId = order1.id,
                orderName = "Challenge 1 - Challenge Plan 1 PRO",
                orderPrice = 100.1.toBigDecimal(),
                creditsUsed = 0.toBigDecimal(),
                affiliateCodeUsed = null,
                discountCodeUsed = "DISCOUNT_CODE",
                paymentProvider = PaymentGatewayProvider.MY_FATOORAH.toHumanReadableFormat(),
                platformType = PlatformType.META_TRADER_5,
                userFullName = "John Doe",
                email = "<EMAIL>",
                city = "City 1",
                postCode = "12345",
                countryName = "Czechia",
                countryIsoCode = "CZ",
                userOrdersTotal = 2,
                userOrdersRevenueTotal = 999.9.toBigDecimal(),
                ordersThisDay = 2,
                ordersRevenueThisDay = 300.3.toBigDecimal(),
                ordersThisMonth = 3,
                ordersRevenueThisMonth = 600.8.toBigDecimal(),
                isRecurringPayment = true
            )
        }
    }

    "should correctly process abandoned orders" - {

        "when no notification was previously sent" {
            testDataHelper.getSystemSetting(
                type = SystemSettingType.UNCOMPLETED_ORDER_PROCESS_NOTIFICATION_TIME_PERIOD_MINUTES,
                value = "30"
            )
            testDataHelper.getSystemSetting(
                type = SystemSettingType.UNCOMPLETED_ORDER_DISCOUNT_CODE_PERCENTAGE,
                value = "15"
            )

            prepareEmailTemplates()

            val user = testDataHelper.getUser(firstName = "Alice", lastName = "Smith", email = "<EMAIL>")
            val challenge = testDataHelper.getChallenge(name = "Challenge Abandoned")
            val challengePlan = testDataHelper.getChallengePlan(
                title = "Challenge Plan Abandoned",
                challengeId = challenge.id
            )

            val order = testDataHelper.getOrder(
                state = OrderState.PENDING,
                userId = user.id,
                challengeId = challenge.id,
                challengePlanId = challengePlan.id,
                platform = PlatformType.META_TRADER_5
            ) {
                it.setAndReturnPrivateProperty("updatedAt", Instant.now().minusSeconds(1800)) // 30 minutes ago
            }

            underTest.processAbandonedOrders()

            val newDiscountCode = discountCodeFinderService.findAll()[0]

            verify {
                sendEmailNotificationMock(
                    email = "<EMAIL>",
                    type = MailType.ORDER_PROCESS_ABANDONED,
                    subject = "Subject ORDER_PROCESS_ABANDONED",
                    htmlBody = "Html body ORDER_PROCESS_ABANDONED",
                    mailVariables = withArg {
                        it[MailVariables.USER_FIRST_NAME] shouldBe "Alice"
                        it[MailVariables.USER_LAST_NAME] shouldBe "Smith"
                        it[MailVariables.ACCOUNT_ORDER_ID] shouldBe order.id
                        it[MailVariables.ACCOUNT_PLAN_NAME] shouldBe "Challenge Abandoned - Challenge Plan Abandoned"
                        it[MailVariables.DISCOUNT_CODE_PERCENTAGE] shouldBe 15
                        it[MailVariables.DISCOUNT_CODE] shouldBe newDiscountCode.code
                    }
                )
            }
        }

        "when notification was already sent" {
            testDataHelper.getSystemSetting(
                type = SystemSettingType.UNCOMPLETED_ORDER_PROCESS_NOTIFICATION_TIME_PERIOD_MINUTES,
                value = "30"
            )
            testDataHelper.getSystemSetting(
                type = SystemSettingType.UNCOMPLETED_ORDER_DISCOUNT_CODE_PERCENTAGE,
                value = "15"
            )

            prepareEmailTemplates()

            val user = testDataHelper.getUser(
                index = 3,
                firstName = "Robert",
                lastName = "Johnson",
                email = "<EMAIL>"
            )
            val challenge = testDataHelper.getChallenge(index = 3, name = "Challenge Already Notified")
            val challengePlan = testDataHelper.getChallengePlan(
                index = 3,
                title = "Challenge Plan Already Notified",
                challengeId = challenge.id
            )
            val challengeStep = testDataHelper.getChallengeStep(
                challengePlanId = challengePlan.id,
                number = 1
            )

            val order = testDataHelper.getOrder(
                state = OrderState.PENDING,
                userId = user.id,
                challengeId = challenge.id,
                challengePlanId = challengePlan.id,
                platform = PlatformType.META_TRADER_5
            ) {
                it.setAndReturnPrivateProperty("updatedAt", Instant.now().minusSeconds(1800))
            }

            val tradingAccount = testDataHelper.getTradingAccount(
                accountId = "alreadynotified123",
                orderId = order.id,
                challengeStepId = challengeStep.id,
                platform = PlatformType.META_TRADER_5,
                state = TradingAccountState.ACTIVE
            )

            // Create email log for the order first to simulate previous notification
            val mailVariables = mapOf(
                MailVariables.USER_FIRST_NAME to "Robert",
                MailVariables.USER_LAST_NAME to "Johnson"
            )

            testDataHelper.getEmailLog(
                userId = user.id,
                recipientEmail = "<EMAIL>",
                mailType = MailType.ORDER_PROCESS_ABANDONED,
                subject = "Previous notification",
                subjectId = order.id, // Link to the order
                fromAddress = "<EMAIL>",
                htmlContent = "Previous notification content",
                sentSuccessful = true,
                errorMessage = null,
                mailVariables = mailVariables
            )

            // Process abandoned orders, this order should be skipped
            underTest.processAbandonedOrders()

            // Verify that no notification was sent for this order
            verify(exactly = 0) {
                sendEmailNotificationMock(
                    email = "<EMAIL>",
                    type = MailType.ORDER_PROCESS_ABANDONED,
                    subject = any(),
                    htmlBody = any(),
                    mailVariables = any()
                )
            }
        }

        "when order is not pending" {
            testDataHelper.getSystemSetting(
                type = SystemSettingType.UNCOMPLETED_ORDER_PROCESS_NOTIFICATION_TIME_PERIOD_MINUTES,
                value = "30"
            )
            testDataHelper.getSystemSetting(
                type = SystemSettingType.UNCOMPLETED_ORDER_DISCOUNT_CODE_PERCENTAGE,
                value = "15"
            )

            prepareEmailTemplates()

            val user = testDataHelper.getUser(index = 4, firstName = "Eva", lastName = "Miller", email = "<EMAIL>")
            val challenge = testDataHelper.getChallenge(index = 4, name = "Challenge Not Pending")
            val challengePlan = testDataHelper.getChallengePlan(
                index = 4,
                title = "Challenge Plan Not Pending",
                challengeId = challenge.id
            )
            val challengeStep = testDataHelper.getChallengeStep(
                challengePlanId = challengePlan.id,
                number = 1
            )

            val order = testDataHelper.getOrder(
                state = OrderState.COMPLETED, // Not PENDING
                userId = user.id,
                challengeId = challenge.id,
                challengePlanId = challengePlan.id,
                platform = PlatformType.META_TRADER_5
            ) {
                it.setAndReturnPrivateProperty("updatedAt", Instant.now().minusSeconds(1800))
            }

            val tradingAccount = testDataHelper.getTradingAccount(
                accountId = "notpending123",
                orderId = order.id,
                challengeStepId = challengeStep.id,
                platform = PlatformType.META_TRADER_5,
                state = TradingAccountState.ACTIVE
            )

            // Process abandoned orders, this order should be skipped
            underTest.processAbandonedOrders()

            // Verify that no notification was sent for this order
            verify(exactly = 0) {
                sendEmailNotificationMock(
                    email = "<EMAIL>",
                    type = MailType.ORDER_PROCESS_ABANDONED,
                    subject = any(),
                    htmlBody = any(),
                    mailVariables = any()
                )
            }
        }

        "when order is recent" {
            testDataHelper.getSystemSetting(
                type = SystemSettingType.UNCOMPLETED_ORDER_PROCESS_NOTIFICATION_TIME_PERIOD_MINUTES,
                value = "30"
            )
            testDataHelper.getSystemSetting(
                type = SystemSettingType.UNCOMPLETED_ORDER_DISCOUNT_CODE_PERCENTAGE,
                value = "15"
            )

            prepareEmailTemplates()

            val user = testDataHelper.getUser(
                index = 5,
                firstName = "Luke",
                lastName = "Wilson",
                email = "<EMAIL>"
            )
            val challenge = testDataHelper.getChallenge(index = 5, name = "Challenge Recent")
            val challengePlan = testDataHelper.getChallengePlan(
                index = 5,
                title = "Challenge Plan Recent",
                challengeId = challenge.id
            )
            val challengeStep = testDataHelper.getChallengeStep(
                challengePlanId = challengePlan.id,
                number = 1
            )

            val order = testDataHelper.getOrder(
                state = OrderState.PENDING,
                userId = user.id,
                challengeId = challenge.id,
                challengePlanId = challengePlan.id,
                platform = PlatformType.META_TRADER_5
            ) {
                it.setAndReturnPrivateProperty("updatedAt", Instant.now().minusSeconds(600))
            }

            val tradingAccount = testDataHelper.getTradingAccount(
                accountId = "recent123",
                orderId = order.id,
                challengeStepId = challengeStep.id,
                platform = PlatformType.META_TRADER_5,
                state = TradingAccountState.ACTIVE
            )

            // Process abandoned orders, this order should be skipped
            underTest.processAbandonedOrders()

            // Verify that no notification was sent for this order
            verify(exactly = 0) {
                sendEmailNotificationMock(
                    email = "<EMAIL>",
                    type = MailType.ORDER_PROCESS_ABANDONED,
                    subject = any(),
                    htmlBody = any(),
                    mailVariables = any()
                )
            }
        }

        "when order is older than 60 days" {
            testDataHelper.getSystemSetting(
                type = SystemSettingType.UNCOMPLETED_ORDER_PROCESS_NOTIFICATION_TIME_PERIOD_MINUTES,
                value = "30"
            )
            testDataHelper.getSystemSetting(
                type = SystemSettingType.UNCOMPLETED_ORDER_DISCOUNT_CODE_PERCENTAGE,
                value = "15"
            )

            prepareEmailTemplates()

            val user = testDataHelper.getUser(
                index = 6, // Ensure unique index
                firstName = "Old",
                lastName = "OrderUser",
                email = "<EMAIL>"
            )
            val challenge = testDataHelper.getChallenge(index = 6, name = "Challenge Old Order")
            val challengePlan = testDataHelper.getChallengePlan(
                index = 6,
                title = "Challenge Plan Old Order",
                challengeId = challenge.id
            )
            val challengeStep = testDataHelper.getChallengeStep(
                challengePlanId = challengePlan.id,
                number = 1
            )

            val order = testDataHelper.getOrder(
                state = OrderState.PENDING,
                userId = user.id,
                challengeId = challenge.id,
                challengePlanId = challengePlan.id,
                platform = PlatformType.META_TRADER_5
            ) {
                it.setAndReturnPrivateProperty("updatedAt", Instant.now().minusSeconds(61 * 24 * 60 * 60))
            }

            testDataHelper.getTradingAccount(
                accountId = "oldorder123",
                orderId = order.id,
                challengeStepId = challengeStep.id,
                platform = PlatformType.META_TRADER_5,
                state = TradingAccountState.ACTIVE
            )

            underTest.processAbandonedOrders()

            verify(exactly = 0) {
                sendEmailNotificationMock(
                    email = "<EMAIL>",
                    type = MailType.ORDER_PROCESS_ABANDONED,
                    subject = any(),
                    htmlBody = any(),
                    mailVariables = any()
                )
            }
        }
    }
})
