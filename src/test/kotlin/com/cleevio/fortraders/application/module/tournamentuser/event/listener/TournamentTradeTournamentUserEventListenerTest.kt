package com.cleevio.fortraders.application.module.tournamentuser.event.listener

import com.cleevio.fortraders.UnitTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.tournamenttrade.TournamentTradePublishingService
import com.cleevio.fortraders.application.module.tournamentuser.TournamentUserProfitUpdatingService
import com.cleevio.fortraders.domain.model.tournamenttrade.event.TournamentTradesClosedEvent
import com.cleevio.fortraders.domain.model.tournamenttrade.event.TournamentUserTradesClosedEvent
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import java.util.UUID

class TournamentTradeTournamentUserEventListenerTest : UnitTest({

    val tournamentUserProfitUpdatingService = mockk<TournamentUserProfitUpdatingService>()
    val tournamentTradePublishingService = mockk<TournamentTradePublishingService>()
    val underTest = TournamentTradeTournamentUserEventListener(
        tournamentUserProfitUpdatingService = tournamentUserProfitUpdatingService,
        tournamentTradePublishingService = tournamentTradePublishingService
    )

    "should correctly call service method for tournament trades closed event" {
        every { tournamentUserProfitUpdatingService.updateUsersProfitsForEndedTournament(any(), any()) } just runs
        every { tournamentTradePublishingService.publishUserClosedTrades(any<Set<UUID>>(), any()) } just runs

        underTest.handleTournamentTradesClosedEvent(
            event = TournamentTradesClosedEvent(
                tournamentId = "dfe6ceab-8ae2-4c86-a24f-4b426781f638".toUUID(),
                userIds = setOf(
                    "3e007dad-d64b-48dc-aa63-1e50bb02b3af".toUUID(),
                    "edd79e5b-220b-49a4-bb7c-44a6b543dfb5".toUUID()
                )
            )
        )

        verify {
            tournamentUserProfitUpdatingService.updateUsersProfitsForEndedTournament(
                tournamentId = "dfe6ceab-8ae2-4c86-a24f-4b426781f638".toUUID(),
                userIds = setOf(
                    "3e007dad-d64b-48dc-aa63-1e50bb02b3af".toUUID(),
                    "edd79e5b-220b-49a4-bb7c-44a6b543dfb5".toUUID()
                )
            )
            tournamentTradePublishingService.publishUserClosedTrades(
                userIds = setOf(
                    "3e007dad-d64b-48dc-aa63-1e50bb02b3af".toUUID(),
                    "edd79e5b-220b-49a4-bb7c-44a6b543dfb5".toUUID()
                ),
                tournamentId = "dfe6ceab-8ae2-4c86-a24f-4b426781f638".toUUID()
            )
        }
    }

    "should correctly call service method for tournament user trades closed event" {
        every { tournamentUserProfitUpdatingService.updateProfitsForUser(any(), any()) } just runs

        underTest.handleTournamentUserTradesClosedEvent(
            event = TournamentUserTradesClosedEvent(
                tournamentId = "dfe6ceab-8ae2-4c86-a24f-4b426781f638".toUUID(),
                userId = "3e007dad-d64b-48dc-aa63-1e50bb02b3af".toUUID()
            )
        )

        verify {
            tournamentUserProfitUpdatingService.updateProfitsForUser(
                tournamentId = "dfe6ceab-8ae2-4c86-a24f-4b426781f638".toUUID(),
                userId = "3e007dad-d64b-48dc-aa63-1e50bb02b3af".toUUID()
            )
        }
    }
})
