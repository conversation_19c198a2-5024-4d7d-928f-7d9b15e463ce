package com.cleevio.fortraders.application.module.breach

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.breach.command.AdminBreachTradingAccountCommand
import com.cleevio.fortraders.domain.model.breach.BreachRepository
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.tradingaccount.TradingAccountRepository
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.domain.model.tradingaccount.exception.TradingAccountAlreadyBreachedException
import com.cleevio.fortraders.shouldNotBeNullAndBeEqualComparingTo
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.extensions.time.withConstantNow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.verify
import java.time.Instant

class AdminBreachTradingAccountCommandHandlerIT(
    private val breachRepository: BreachRepository,
    private val tradingAccountRepository: TradingAccountRepository,
) : IntegrationTest({

    "should throw because account is already breached" {
        breachRepository.count() shouldBe 0
        val challengePlan = testDataHelper.getChallengePlan()
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id)
        val order = testDataHelper.getOrder(challengeId = challengePlan.challengeId, challengePlanId = challengePlan.id)
        val tradingAccount1 = testDataHelper.getTradingAccount(
            challengeStepId = challengeStep.id,
            accountId = "105",
            platform = PlatformType.META_TRADER_5,
            orderId = order.id
        ) { it.breach() }

        shouldThrowExactly<TradingAccountAlreadyBreachedException> {
            commandBus(
                AdminBreachTradingAccountCommand(
                    tradingAccountId = tradingAccount1.id,
                    internalReason = "internal reason",
                    externalReason = "external reason"
                )
            )
        }

        breachRepository.count() shouldBe 0
    }

    "should correctly create breach" {
        breachRepository.count() shouldBe 0
        val challengePlan = testDataHelper.getChallengePlan(steps = 2)
        val challengeStep1 = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id, number = 1)
        val challengeStep2 = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id, number = 2)
        val order = testDataHelper.getOrder(challengeId = challengePlan.challengeId, challengePlanId = challengePlan.id)
        val tradingAccount1 = testDataHelper.getTradingAccount(
            challengeStepId = challengeStep1.id,
            accountId = "105",
            platform = PlatformType.META_TRADER_5,
            orderId = order.id,
            startingBalance = 205.5.toBigDecimal(),
            equity = 305.5.toBigDecimal()
        )
        testDataHelper.getTradingAccount(
            challengeStepId = challengeStep2.id,
            accountId = "105",
            platform = PlatformType.TRADE_LOCKER,
            orderId = order.id,
            startingBalance = 1.1.toBigDecimal()
        )
        testDataHelper.getTradeSync(PlatformType.META_TRADER_5)

        withConstantNow(Instant.parse("2019-12-24T18:15:30Z")) {
            commandBus(
                AdminBreachTradingAccountCommand(
                    tradingAccountId = tradingAccount1.id,
                    internalReason = "internal reason",
                    externalReason = "external reason"
                )
            )
        }

        val breaches = breachRepository.findAll()
        breaches shouldHaveSize 1
        breaches[0].let {
            it.tradingAccountId shouldBe tradingAccount1.id
            it.type shouldBe BreachType.MANUAL_BREACH
            it.accountBalance shouldBeEqualComparingTo 205.5.toBigDecimal()
            it.accountEquity shouldNotBeNullAndBeEqualComparingTo 305.5.toBigDecimal()
            it.breachedAt shouldBe Instant.parse("2019-12-24T18:15:30Z")
            it.internalReason shouldBe "internal reason"
            it.externalReason shouldBe "external reason"
            it.createdAt.shouldNotBeNull()
        }

        val tradingAccounts = tradingAccountRepository.findAll().sortedBy { it.createdAt }
        tradingAccounts shouldHaveSize 2
        tradingAccounts[0].let {
            it.id shouldBe tradingAccount1.id
            it.state shouldBe TradingAccountState.BREACHED
            it.currentBalance shouldBeEqualComparingTo 205.5.toBigDecimal()
        }
        tradingAccounts[1].let {
            it.state shouldBe TradingAccountState.ACTIVE
            it.currentBalance shouldBeEqualComparingTo 1.1.toBigDecimal()
        }

        verify {
            breachMT5TradingAccountMock("105")
        }
    }
})
