package com.cleevio.fortraders.application.module.user

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.domain.model.order.constant.OrderState
import com.cleevio.fortraders.domain.model.user.UserRepository
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import java.time.Instant

class UserServiceIT(
    private val userRepository: UserRepository,
    private val underTest: UserService,
) : IntegrationTest({

    "should do nothing because affiliate is null" {
        val user = testDataHelper.getUser()
        val order = testDataHelper.getOrder(userId = user.id)
        user.referrerAffiliateId.shouldBeNull()

        underTest.assignAffiliateToUserIfFirstCompletedOrder(
            userId = user.id,
            affiliateId = null
        )

        val users = userRepository.findAll()
        users shouldHaveSize 1
        users[0].let {
            it.id shouldBe user.id
            it.referrerAffiliateId.shouldBeNull()
        }
    }

    "should do nothing because user has more than one completed order" {
        val referredUser = testDataHelper.getUser(index = 1)
        val referringUser = testDataHelper.getUser(index = 2)
        val affiliate = testDataHelper.getAffiliate(userId = referringUser.id)
        val order1 = testDataHelper.getOrder(userId = referredUser.id, state = OrderState.COMPLETED)
        testDataHelper.getOrder(
            userId = referredUser.id,
            challengeId = order1.challengeId,
            challengePlanId = order1.challengePlanId,
            state = OrderState.COMPLETED
        )
        referredUser.referrerAffiliateId.shouldBeNull()

        underTest.assignAffiliateToUserIfFirstCompletedOrder(
            userId = referredUser.id,
            affiliateId = affiliate.id
        )

        val users = userRepository.findAll().sortedBy { it.createdAt }
        users shouldHaveSize 2
        users[0].let {
            it.id shouldBe referredUser.id
            it.referrerAffiliateId.shouldBeNull()
        }
        users[1].let {
            it.id shouldBe referringUser.id
            it.referrerAffiliateId.shouldBeNull()
        }
    }

    "should assign affiliate to user" {
        val referredUser = testDataHelper.getUser(index = 1)
        val referringUser = testDataHelper.getUser(index = 2)
        val affiliate = testDataHelper.getAffiliate(userId = referringUser.id)
        val order1 = testDataHelper.getOrder(userId = referredUser.id, state = OrderState.COMPLETED, paidAt = Instant.now())
        testDataHelper.getOrder(
            userId = referredUser.id,
            challengeId = order1.challengeId,
            challengePlanId = order1.challengePlanId,
            state = OrderState.PENDING
        )
        referredUser.referrerAffiliateId.shouldBeNull()

        underTest.assignAffiliateToUserIfFirstCompletedOrder(
            userId = referredUser.id,
            affiliateId = affiliate.id
        )

        val users = userRepository.findAll().sortedBy { it.createdAt }
        users shouldHaveSize 2
        users[0].let {
            it.id shouldBe referredUser.id
            it.referrerAffiliateId shouldBe affiliate.id
        }
        users[1].let {
            it.id shouldBe referringUser.id
            it.referrerAffiliateId.shouldBeNull()
        }
    }
})
