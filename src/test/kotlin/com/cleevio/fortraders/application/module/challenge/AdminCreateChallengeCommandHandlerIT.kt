package com.cleevio.fortraders.application.module.challenge

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.challenge.command.AdminCreateChallengeCommand
import com.cleevio.fortraders.domain.model.challenge.ChallengeRepository
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeState
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.challenge.exception.ChallengeWithStartingBalanceAndTypeAlreadyExistsException
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe

class AdminCreateChallengeCommandHandlerIT(
    private val challengeRepository: ChallengeRepository,
) : IntegrationTest({

    "should throw because challenge with same starting balance and type already exists" {
        testDataHelper.getChallenge(startingBalance = 105.5.toBigDecimal(), type = ChallengeType.CRYPTO)
        challengeRepository.count() shouldBe 1

        shouldThrowExactly<ChallengeWithStartingBalanceAndTypeAlreadyExistsException> {
            commandBus(
                AdminCreateChallengeCommand(
                    name = "Mission impossible",
                    startingBalance = 105.5.toBigDecimal(),
                    type = ChallengeType.CRYPTO
                )
            )
        }

        challengeRepository.count() shouldBe 1
    }

    "should correctly create challenge" {
        challengeRepository.count() shouldBe 0

        commandBus(
            AdminCreateChallengeCommand(
                name = "Mission impossible",
                startingBalance = 105.5.toBigDecimal(),
                type = ChallengeType.CRYPTO
            )
        )

        val challenges = challengeRepository.findAll()
        challenges shouldHaveSize 1
        challenges[0].let {
            it.name shouldBe "Mission impossible"
            it.startingBalance shouldBeEqualComparingTo 105.5.toBigDecimal()
            it.state shouldBe ChallengeState.DISABLED
            it.type shouldBe ChallengeType.CRYPTO
        }
    }
})
