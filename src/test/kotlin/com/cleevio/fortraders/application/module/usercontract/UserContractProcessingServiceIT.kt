package com.cleevio.fortraders.application.module.usercontract

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.usercontract.port.output.CreateContractSubmissionModel
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.contracttemplate.constant.ContractTemplateVariable
import com.cleevio.fortraders.domain.model.usercontract.UserContractRepository
import com.cleevio.fortraders.domain.model.usercontract.exception.UserContractCreationFailedException
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify

class UserContractProcessingServiceIT(
    private val userContractRepository: UserContractRepository,
    private val underTest: UserContractProcessingService,
) : IntegrationTest({

    "should correctly create user contract after review approved" {
        every { createContractSubmissionMock(any(), any(), any()) } returns Result.success(
            CreateContractSubmissionModel(
                submissionId = 456L,
                contractEmbedUrl = "https://example.com/contract"
            )
        )

        val challengePlan = testDataHelper.getChallengePlan(steps = 2)
        val challengeStep1 = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            number = 1,
            type = ChallengeStepType.EVALUATION
        )
        val challengeStep2 = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            number = 2,
            type = ChallengeStepType.FUNDED
        )
        val user = testDataHelper.getUser(email = "<EMAIL>", firstName = "John", lastName = "Doe")
        val country = testDataHelper.getCountry(name = "Slovakia")
        val contact = testDataHelper.getContact(
            userId = user.id,
            countryId = country.id,
            streetAddress = "Test Street 123",
            city = "Test City",
            postCode = "12345"
        )
        val order = testDataHelper.getOrder(
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            userId = user.id
        )
        val challengeStepSettings = prepareChallengeStepSettings(challengeStep2)
        prepareDefaultChallengeStepSettingMovements(
            challengeStepSettings = challengeStepSettings,
            maxDrawdown = 10,
            dailyDrawdown = 5
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            accountId = "1",
            orderId = order.id,
            challengeStepId = challengeStep1.id
        )
        val contractTemplate = testDataHelper.getContractTemplate(
            externalId = 1234L,
            supportedVariables = setOf(
                ContractTemplateVariable.MAX_DRAWDOWN,
                ContractTemplateVariable.DAILY_DRAWDOWN,
                ContractTemplateVariable.USER_FULL_NAME,
                ContractTemplateVariable.USER_STREET_ADDRESS,
                ContractTemplateVariable.USER_CITY,
                ContractTemplateVariable.USER_COUNTRY_NAME
            )
        )

        underTest.createUserContractAfterReviewApproved(
            tradingAccountId = tradingAccount.id,
            contractTemplateId = contractTemplate.id
        )

        val userContracts = userContractRepository.findAll()
        userContracts shouldHaveSize 1
        userContracts[0].let {
            it.userId shouldBe user.id
            it.orderId shouldBe order.id
            it.submissionId shouldBe 456L
            it.embedUrl shouldBe "https://example.com/contract"
        }

        verify {
            createContractSubmissionMock(
                contractTemplateExternalId = 1234L,
                contractTemplateVariables = match { variables ->
                    variables[ContractTemplateVariable.MAX_DRAWDOWN] == 10 &&
                        variables[ContractTemplateVariable.DAILY_DRAWDOWN] == 5 &&
                        variables[ContractTemplateVariable.USER_FULL_NAME] == "John Doe" &&
                        variables[ContractTemplateVariable.USER_STREET_ADDRESS] == "Test Street 123" &&
                        variables[ContractTemplateVariable.USER_CITY] == "Test City" &&
                        variables[ContractTemplateVariable.USER_COUNTRY_NAME] == "Slovakia"
                },
                email = "<EMAIL>"
            )
        }
    }

    "should throw UserContractCreationFailedException when contract submission fails" {
        every { createContractSubmissionMock(any(), any(), any()) } returns Result.failure(
            RuntimeException("Contract submission failed")
        )

        val challengePlan = testDataHelper.getChallengePlan(steps = 2)
        val challengeStep1 = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            number = 1
        )
        val challengeStep2 = testDataHelper.getChallengeStep(
            challengePlanId = challengePlan.id,
            number = 2
        )
        val user = testDataHelper.getUser(email = "<EMAIL>")
        val country = testDataHelper.getCountry()
        val contact = testDataHelper.getContact(userId = user.id, countryId = country.id)
        val order = testDataHelper.getOrder(
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            userId = user.id
        )

        val challengeStepSettings = prepareChallengeStepSettings(challengeStep2)
        prepareDefaultChallengeStepSettingMovements(challengeStepSettings)

        val tradingAccount = testDataHelper.getTradingAccount(
            accountId = "1",
            orderId = order.id,
            challengeStepId = challengeStep1.id
        )
        val contractTemplate = testDataHelper.getContractTemplate(
            externalId = 1234L,
            supportedVariables = setOf(
                ContractTemplateVariable.MAX_DRAWDOWN,
                ContractTemplateVariable.USER_FULL_NAME
            )
        )

        shouldThrowExactly<UserContractCreationFailedException> {
            underTest.createUserContractAfterReviewApproved(
                tradingAccountId = tradingAccount.id,
                contractTemplateId = contractTemplate.id
            )
        }

        userContractRepository.findAll() shouldHaveSize 0

        verify {
            createContractSubmissionMock(
                contractTemplateExternalId = 1234L,
                contractTemplateVariables = any(),
                email = "<EMAIL>"
            )
        }
    }
})
