package com.cleevio.fortraders.application.module.payout

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.payout.command.AdminPatchPayoutCommand
import com.cleevio.fortraders.domain.model.payout.PayoutRepository
import com.cleevio.fortraders.domain.model.payout.constant.PayoutFinalState
import com.cleevio.fortraders.domain.model.payout.constant.PayoutKycState
import com.cleevio.fortraders.domain.model.payout.constant.PayoutRiskState
import com.cleevio.fortraders.domain.model.payout.constant.PayoutState
import com.cleevio.fortraders.domain.model.payout.exception.PayoutNotFoundException
import com.cleevio.fortraders.domain.model.payout.exception.PayoutStateChangeNotAllowedException
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.toOptional
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe

class AdminPatchPayoutCommandHandlerIT(
    private val payoutRepository: PayoutRepository,
) : IntegrationTest({

    "should throw because payout does not exist" {
        val challengePlan = testDataHelper.getChallengePlan()
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id)
        val user = testDataHelper.getUser(firstName = "Johny", lastName = "Cash")
        val order = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            state = TradingAccountState.ACTIVE,
            currentBalance = 987.1.toBigDecimal()
        )
        testDataHelper.getPayout(tradingAccountId = tradingAccount.id)

        shouldThrowExactly<PayoutNotFoundException> {
            commandBus(
                AdminPatchPayoutCommand(
                    payoutId = "96da130f-ef26-4ea7-a13f-deaa73e37c73".toUUID(),
                    riskState = null,
                    kycState = null,
                    finalState = null,
                    note = null
                )
            )
        }
    }

    "should throw because payout is already approved" {
        val challengePlan = testDataHelper.getChallengePlan()
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id)
        val user = testDataHelper.getUser(firstName = "Johny", lastName = "Cash")
        val order = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            state = TradingAccountState.ACTIVE,
            currentBalance = 987.1.toBigDecimal()
        )
        val payout = testDataHelper.getPayout(tradingAccountId = tradingAccount.id, state = PayoutState.APPROVED)

        shouldThrowExactly<PayoutStateChangeNotAllowedException> {
            commandBus(
                AdminPatchPayoutCommand(
                    payoutId = payout.id,
                    riskState = PayoutRiskState.READY_TO_PAY,
                    kycState = null,
                    finalState = null,
                    note = null
                )
            )
        }
    }

    "should correctly patch values" {
        val challengePlan = testDataHelper.getChallengePlan()
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id)
        val user = testDataHelper.getUser(firstName = "Johny", lastName = "Cash")
        val order = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order.id,
            challengeStepId = challengeStep.id,
            accountId = "1",
            state = TradingAccountState.ACTIVE,
            currentBalance = 987.1.toBigDecimal()
        )
        val payout = testDataHelper.getPayout(tradingAccountId = tradingAccount.id, state = PayoutState.REQUESTED)

        commandBus(
            AdminPatchPayoutCommand(
                payoutId = payout.id,
                riskState = PayoutRiskState.READY_TO_PAY,
                kycState = PayoutKycState.VERIFY_CALL,
                finalState = PayoutFinalState.PAY_AND_TERMINATE,
                note = "Don't bother".toOptional()
            )
        )

        val payouts = payoutRepository.findAll()
        payouts shouldHaveSize 1
        payouts[0].let {
            it.id shouldBe payout.id
            it.state shouldBe PayoutState.REQUESTED
            it.riskState shouldBe PayoutRiskState.READY_TO_PAY
            it.kycState shouldBe PayoutKycState.VERIFY_CALL
            it.finalState shouldBe PayoutFinalState.PAY_AND_TERMINATE
            it.note shouldBe "Don't bother"
        }
    }
})
