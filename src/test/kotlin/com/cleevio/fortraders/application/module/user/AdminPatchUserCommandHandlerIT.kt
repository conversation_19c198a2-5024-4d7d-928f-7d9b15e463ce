package com.cleevio.fortraders.application.module.user

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.user.command.AdminPatchUserCommand
import com.cleevio.fortraders.domain.model.label.exception.LabelNotFoundException
import com.cleevio.fortraders.domain.model.user.UserRepository
import com.cleevio.fortraders.domain.model.user.constant.UserKycState
import com.cleevio.fortraders.domain.model.user.constant.UserRole
import com.cleevio.fortraders.domain.model.user.constant.UserState
import com.cleevio.fortraders.domain.model.user.exception.UserNotFoundException
import com.cleevio.fortraders.setAndReturnPrivateProperty
import com.cleevio.fortraders.toOptional
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.verify
import java.util.Optional
import java.util.UUID

class AdminPatchUserCommandHandlerIT(
    private val userRepository: UserRepository,
) : IntegrationTest({

    "should throw because user with given ID does not exist" {
        testDataHelper.getUser()
        userRepository.count() shouldBe 1

        shouldThrowExactly<UserNotFoundException> {
            commandBus(
                AdminPatchUserCommand(
                    userId = "ff1cddc4-74c6-47db-be3f-92975d498ac7".toUUID(),
                    role = Optional.of(UserRole.ADMIN),
                    kycState = UserKycState.APPROVED,
                    state = UserState.BLOCKED,
                    password = "new-password",
                    comment = "This is a comment".toOptional(),
                    labels = null
                )
            )
        }

        userRepository.count() shouldBe 1
    }

    "should correctly patch user role" {
        val user = testDataHelper.getUser(role = UserRole.MANAGER, firebaseId = "12345", state = UserState.ENABLED) {
            it.setAndReturnPrivateProperty("comment", "This is a comment")
        }
        userRepository.count() shouldBe 1

        commandBus(
            AdminPatchUserCommand(
                userId = user.id,
                role = Optional.of(UserRole.ADMIN),
                kycState = null,
                state = UserState.BLOCKED,
                password = null,
                comment = null,
                labels = null
            )
        )

        val users = userRepository.findAll()
        users shouldHaveSize 1
        users[0].let {
            it.id shouldBe user.id
            it.role shouldBe UserRole.ADMIN
            it.kycState shouldBe UserKycState.NOT_REQUESTED
            it.comment shouldBe "This is a comment"
        }

        verify {
            updateFirebaseUserMock(
                firebaseId = "12345",
                role = UserRole.ADMIN,
                state = UserState.BLOCKED,
                password = null
            )
        }
    }

    "should correctly patch user kycState" {
        val user = testDataHelper.getUser(firebaseId = "12345", role = null, kycState = UserKycState.NOT_REQUESTED)
        userRepository.count() shouldBe 1

        commandBus(
            AdminPatchUserCommand(
                userId = user.id,
                role = null,
                kycState = UserKycState.APPROVED,
                state = UserState.BLOCKED,
                password = "new-password",
                comment = "This is a comment".toOptional(),
                labels = null
            )
        )

        val users = userRepository.findAll()
        users shouldHaveSize 1
        users[0].let {
            it.id shouldBe user.id
            it.role shouldBe null
            it.kycState shouldBe UserKycState.APPROVED
            it.comment shouldBe "This is a comment"
        }

        verify {
            updateFirebaseUserMock(
                firebaseId = "12345",
                role = null,
                state = UserState.BLOCKED,
                password = "new-password"
            )
        }
    }

    "should throw because given label does not exist" {
        testDataHelper.getLabel(name = "Test label")
        val user = testDataHelper.getUser(firebaseId = "12345", role = null, kycState = UserKycState.NOT_REQUESTED)
        userRepository.count() shouldBe 1

        shouldThrowExactly<LabelNotFoundException> {
            commandBus(
                AdminPatchUserCommand(
                    userId = user.id,
                    role = null,
                    kycState = null,
                    state = null,
                    password = null,
                    comment = null,
                    labels = setOf(UUID.randomUUID())
                )
            )
        }
    }

    "should correctly patch labels" {
        val label = testDataHelper.getLabel(name = "Test label")
        val user = testDataHelper.getUser(firebaseId = "12345", role = null, kycState = UserKycState.NOT_REQUESTED)
        userRepository.count() shouldBe 1

        commandBus(
            AdminPatchUserCommand(
                userId = user.id,
                role = null,
                kycState = null,
                state = null,
                password = null,
                comment = null,
                labels = setOf(label.id)
            )
        )

        val users = userRepository.findAll()
        users shouldHaveSize 1
        users[0].let {
            it.id shouldBe user.id
            it.labels shouldBe setOf(label.id)
        }

        verify {
            updateFirebaseUserMock(
                firebaseId = any(),
                role = any(),
                state = any(),
                password = any()
            )
        }
    }
})
