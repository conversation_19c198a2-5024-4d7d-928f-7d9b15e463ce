package com.cleevio.fortraders.application.module.file

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.domain.model.file.File
import com.cleevio.fortraders.domain.model.file.FileRepository
import com.cleevio.fortraders.domain.model.file.constant.FileType
import com.cleevio.fortraders.domain.model.file.exception.FileNotFoundException
import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe

class FileFinderServiceIT(
    private val underTest: FileFinderService,
    private val fileRepository: FileRepository,
) : IntegrationTest({

    "should find file entity" {
        val file = fileRepository.save(File(extension = "png", type = FileType.GAME_LOGO))

        val foundFile = underTest.getById(file.id)

        foundFile.fileName() shouldBe "${file.id}.png"
        foundFile.type shouldBe FileType.GAME_LOGO
    }

    "should validate file entity exists" {
        val file = fileRepository.save(File(extension = "png", type = FileType.GAME_LOGO))

        shouldNotThrow<FileNotFoundException> {
            underTest.validateIfExists(file.id)
        }
    }

    "should throw if file validation fails" {
        shouldThrow<FileNotFoundException> {
            underTest.validateIfExists("cfbd23fe-53c5-491c-96d2-bec082337a03".toUUID())
        }
    }

    "should throw if file not exists" {
        shouldThrow<FileNotFoundException> {
            underTest.getById("cfbd23fe-53c5-491c-96d2-bec082337a03".toUUID())
        }
    }
})
