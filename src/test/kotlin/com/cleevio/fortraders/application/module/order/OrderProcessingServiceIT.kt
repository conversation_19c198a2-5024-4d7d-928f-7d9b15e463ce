package com.cleevio.fortraders.application.module.order

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.tradingaccount.port.output.dto.CreateTradingAccountResult
import com.cleevio.fortraders.domain.model.breach.BreachRepository
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.order.OrderRepository
import com.cleevio.fortraders.domain.model.order.constant.OrderState
import com.cleevio.fortraders.domain.model.tradingaccount.TradingAccountRepository
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.domain.model.transaction.TransactionRepository
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import com.cleevio.fortraders.domain.model.wallet.WalletRepository
import io.kotest.datatest.withData
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify

class OrderProcessingServiceIT(
    private val transactionRepository: TransactionRepository,
    private val orderRepository: OrderRepository,
    private val walletRepository: WalletRepository,
    private val tradingAccountRepository: TradingAccountRepository,
    private val breachRepository: BreachRepository,
    private val underTest: OrderProcessingService,
) : IntegrationTest({

    "should do nothing because transaction status is not completed" - {
        withData(TransactionStatus.entries.filter { it != TransactionStatus.COMPLETED }) { transactionStatus ->
            val user = testDataHelper.getUser(index = 1)
            val wallet = testDataHelper.getWallet(userId = user.id)
            val transaction = testDataHelper.getTransaction(walletId = wallet.id)

            underTest.processOrderTransactionCompleted(
                transactionId = transaction.id,
                transactionStatus = transactionStatus,
                transactionType = TransactionType.DEPOSIT
            )

            transactionRepository.count() shouldBe 1
        }
    }

    "should do nothing because order id is not found" {
        val user = testDataHelper.getUser(index = 1, firstName = "John", lastName = "Doe", email = "<EMAIL>")
        val wallet = testDataHelper.getWallet(userId = user.id) {
            it.addToBalance(1000.5.toBigDecimal())
        }
        val challengePlan = testDataHelper.getChallengePlan()
        val transaction = testDataHelper.getTransaction(walletId = wallet.id)
        val order1 = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id
        )

        underTest.processOrderTransactionCompleted(
            transactionId = transaction.id,
            transactionStatus = TransactionStatus.COMPLETED,
            transactionType = TransactionType.DEPOSIT
        )

        transactionRepository.count() shouldBe 1
        val orders = orderRepository.findAll()
        orders shouldHaveSize 1
        orders[0].let {
            it.id shouldBe order1.id
            it.state shouldBe OrderState.PENDING
        }
    }

    "should create transaction and mark order as paid" {
        every {
            createMT5TradingAccountMock(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns Result.success(CreateTradingAccountResult("12345"))

        val user = testDataHelper.getUser(index = 1, firstName = "John", lastName = "Doe", email = "<EMAIL>")
        val wallet = testDataHelper.getWallet(userId = user.id) {
            it.addToBalance(1000.5.toBigDecimal())
        }
        val (challengePlan, _, _) = prepareChallengePlanWithStepsAndSettings()
        val order1 = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            price = 999.9.toBigDecimal()
        )
        val order2 = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            price = 111.1.toBigDecimal()
        )
        val transaction = testDataHelper.getTransaction(walletId = wallet.id, orderId = order1.id) {
            it.changeStatus(TransactionStatus.COMPLETED)
        }

        underTest.processOrderTransactionCompleted(
            transactionId = transaction.id,
            transactionStatus = TransactionStatus.COMPLETED,
            transactionType = TransactionType.DEPOSIT
        )

        val transactions = transactionRepository.findAll().sortedBy { it.createdAt }
        transactions[0].let {
            it.id shouldBe transaction.id
            it.orderId shouldBe order1.id
            it.status shouldBe TransactionStatus.COMPLETED
        }
        transactions[1].also {
            it.orderId shouldBe order1.id
            it.status shouldBe TransactionStatus.COMPLETED
            it.amount shouldBeEqualComparingTo 999.9.toBigDecimal()
            it.type shouldBe TransactionType.CHALLENGE_ORDER
        }

        val orders = orderRepository.findAll().sortedBy { it.createdAt }
        orders shouldHaveSize 2
        orders[0].let {
            it.id shouldBe order1.id
            it.state shouldBe OrderState.COMPLETED
        }
        orders[1].let {
            it.id shouldBe order2.id
            it.state shouldBe OrderState.PENDING
        }

        val wallets = walletRepository.findAll()
        wallets shouldHaveSize 1
        wallets[0].let {
            it.userId shouldBe user.id
            it.balance shouldBeEqualComparingTo 0.6.toBigDecimal()
        }

        verify {
            createMT5TradingAccountMock(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        }
    }

    "should create transaction and mark order as paid for second time and reactivate trading account" {
        val user = testDataHelper.getUser(index = 1, firstName = "John", lastName = "Doe", email = "<EMAIL>")
        val wallet = testDataHelper.getWallet(userId = user.id) {
            it.addToBalance(1000.5.toBigDecimal())
        }
        val (challengePlan, challengeStep, _) = prepareChallengePlanWithStepsAndSettings()
        val order1 = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            price = 999.9.toBigDecimal()
        ) {
            it.markAsCompleted()
            it.markAsCancelled()
        }
        val order2 = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            price = 111.1.toBigDecimal()
        )
        val transaction = testDataHelper.getTransaction(walletId = wallet.id, orderId = order1.id) {
            it.changeStatus(TransactionStatus.COMPLETED)
        }
        testDataHelper.getTradingAccount(
            accountId = "1",
            orderId = order1.id,
            challengeStepId = challengeStep.id
        ) {
            it.recurringPaymentAttemptFailedPauseAccount()
        }

        underTest.processOrderTransactionCompleted(
            transactionId = transaction.id,
            transactionStatus = TransactionStatus.COMPLETED,
            transactionType = TransactionType.DEPOSIT
        )

        val transactions = transactionRepository.findAll().sortedBy { it.createdAt }
        transactions[0].let {
            it.id shouldBe transaction.id
            it.orderId shouldBe order1.id
            it.status shouldBe TransactionStatus.COMPLETED
        }
        transactions[1].also {
            it.orderId shouldBe order1.id
            it.status shouldBe TransactionStatus.COMPLETED
            it.amount shouldBeEqualComparingTo 999.9.toBigDecimal()
            it.type shouldBe TransactionType.CHALLENGE_ORDER
        }

        val orders = orderRepository.findAll().sortedBy { it.createdAt }
        orders shouldHaveSize 2
        orders[0].let {
            it.id shouldBe order1.id
            it.state shouldBe OrderState.COMPLETED
        }
        orders[1].let {
            it.id shouldBe order2.id
            it.state shouldBe OrderState.PENDING
        }

        val wallets = walletRepository.findAll()
        wallets shouldHaveSize 1
        wallets[0].let {
            it.userId shouldBe user.id
            it.balance shouldBeEqualComparingTo 0.6.toBigDecimal()
        }

        val tradingAccounts = tradingAccountRepository.findAll()
        tradingAccounts shouldHaveSize 1
        tradingAccounts[0].let {
            it.accountId shouldBe "1"
            it.state shouldBe TradingAccountState.ACTIVE
        }

        verify {
            activateMT5TradingAccountMock(accountId = "1")
        }
    }

    "should create failed transaction and mark order as cancelled and breach trading account" {
        every { closeAllMT5TradingAccountOpenTradesMock(any()) } returns Result.success(Unit)

        val user = testDataHelper.getUser(index = 1, firstName = "John", lastName = "Doe", email = "<EMAIL>")
        val wallet = testDataHelper.getWallet(userId = user.id) {
            it.addToBalance(1000.5.toBigDecimal())
        }
        val (challengePlan, challengeStep, _) = prepareChallengePlanWithStepsAndSettings(isSubscription = true)
        val order1 = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            price = 999.9.toBigDecimal()
        ) {
            it.markAsCompleted()
        }
        val order2 = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id,
            price = 111.1.toBigDecimal()
        )
        val transaction = testDataHelper.getTransaction(walletId = wallet.id, orderId = order1.id) {
            it.changeStatus(TransactionStatus.COMPLETED)
        }
        testDataHelper.getTradingAccount(
            accountId = "1",
            orderId = order1.id,
            challengeStepId = challengeStep.id
        )
        testDataHelper.getTradeSync(PlatformType.META_TRADER_5)

        underTest.processOrderTransactionCompleted(
            transactionId = transaction.id,
            transactionStatus = TransactionStatus.ERROR,
            transactionType = TransactionType.RECURRING_DEPOSIT
        )

        transactionRepository.count() shouldBe 1

        val orders = orderRepository.findAll().sortedBy { it.createdAt }
        orders shouldHaveSize 2
        orders[0].let {
            it.id shouldBe order1.id
            it.state shouldBe OrderState.CANCELLED
        }
        orders[1].let {
            it.id shouldBe order2.id
            it.state shouldBe OrderState.PENDING
        }

        val wallets = walletRepository.findAll()
        wallets shouldHaveSize 1
        wallets[0].let {
            it.userId shouldBe user.id
            it.balance shouldBeEqualComparingTo 1000.5.toBigDecimal()
        }

        val tradingAccounts = tradingAccountRepository.findAll()
        tradingAccounts shouldHaveSize 1
        tradingAccounts[0].let {
            it.accountId shouldBe "1"
            it.state shouldBe TradingAccountState.BREACHED
        }

        val breaches = breachRepository.findAll()
        breaches shouldHaveSize 1
        breaches[0].let {
            it.tradingAccountId shouldBe tradingAccounts[0].id
            it.type shouldBe BreachType.PAYMENT_FAILED
            it.externalReason shouldBe "Payment failed"
        }

        verify {
            breachMT5TradingAccountMock(accountId = "1")
        }
    }
})
