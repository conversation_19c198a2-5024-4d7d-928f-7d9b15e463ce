package com.cleevio.fortraders.application.module.prizedistributionsnapshot

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.domain.model.prizedistribution.PrizeDistributionRepository
import com.cleevio.fortraders.domain.model.prizedistributionsnapshot.PrizeDistributionSnapshotRepository
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe

class PrizeDistributionSnapshotInitServiceIT(
    private val prizeDistributionSnapshotRepository: PrizeDistributionSnapshotRepository,
    private val prizeDistributionRepository: PrizeDistributionRepository,
    private val underTest: PrizeDistributionSnapshotInitService,
) : IntegrationTest({

    "should do nothing because snapshot already exists" {
        val tournament = testDataHelper.getTournament()
        testDataHelper.getPrizeDistribution()
        testDataHelper.getPrizeDistributionSnapshot(tournamentId = tournament.id)
        prizeDistributionRepository.count() shouldBe 1
        prizeDistributionSnapshotRepository.count() shouldBe 1

        underTest.createSnapshotForTournament(tournament.id)

        prizeDistributionRepository.count() shouldBe 1
        prizeDistributionSnapshotRepository.count() shouldBe 1
    }

    "should correctly create snapshot" {
        val tournament = testDataHelper.getTournament()
        testDataHelper.getPrizeDistribution(
            position = 1,
            players = 10,
            challengeStartingBalance = 1000.5.toBigDecimal()
        )
        testDataHelper.getPrizeDistribution(
            position = 2,
            players = 9,
            challengeStartingBalance = 999.5.toBigDecimal()
        )
        prizeDistributionRepository.count() shouldBe 2
        prizeDistributionSnapshotRepository.count() shouldBe 0

        underTest.createSnapshotForTournament(tournament.id)

        prizeDistributionRepository.count() shouldBe 2
        val prizeDistributionSnapshots = prizeDistributionSnapshotRepository.findAll().sortedBy { it.createdAt }
        prizeDistributionSnapshots shouldHaveSize 2
        prizeDistributionSnapshots[0].let {
            it.tournamentId shouldBe tournament.id
            it.position shouldBe 1
            it.players shouldBe 10
            it.challengeStartingBalance shouldBe 1000.5.toBigDecimal()
        }
        prizeDistributionSnapshots[1].let {
            it.tournamentId shouldBe tournament.id
            it.position shouldBe 2
            it.players shouldBe 9
            it.challengeStartingBalance shouldBe 999.5.toBigDecimal()
        }
    }
})
