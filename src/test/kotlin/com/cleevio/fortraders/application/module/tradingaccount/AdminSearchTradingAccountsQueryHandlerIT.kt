package com.cleevio.fortraders.application.module.tradingaccount

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.tradingaccount.query.AdminSearchTradingAccountsQuery
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.tradingaccount.TradingAccount
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.truncatedShouldBe
import io.kotest.matchers.collections.shouldContainOnly
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.springframework.data.domain.Pageable
import java.time.Instant

class AdminSearchTradingAccountsQueryHandlerIT : IntegrationTest({

    "should correctly return paged result" {
        val challenge = testDataHelper.getChallenge(type = ChallengeType.FOREX)
        val challengePlan = testDataHelper.getChallengePlan(challengeId = challenge.id)
        val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id, type = ChallengeStepType.FUNDED)
        val user = testDataHelper.getUser(firstName = "Johny", lastName = "Cash")
        val order = testDataHelper.getOrder(
            userId = user.id,
            challengeId = challengePlan.challengeId,
            challengePlanId = challengePlan.id
        )
        val tradingAccount = testDataHelper.getTradingAccount(
            orderId = order.id,
            challengeStepId = challengeStep.id,
            accountId = "123456",
            platform = PlatformType.META_TRADER_5,
            state = TradingAccountState.ACTIVE,
            startingBalance = 555.toBigDecimal()
        )
        testDataHelper.getBreach(
            tradingAccountId = tradingAccount.id,
            type = BreachType.MANUAL_BREACH,
            breachedAt = Instant.parse("2021-01-01T00:00:00Z"),
            internalReason = "internal reason",
            externalReason = "external reason"
        )

        val page = queryBus(
            AdminSearchTradingAccountsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchTradingAccountsQuery.Filter(
                    createdAtFrom = null,
                    createdAtTo = null,
                    states = null,
                    breachTypes = null,
                    challengeStepTypes = null,
                    lastTradeCloseTimeFrom = null,
                    fulltext = null,
                    challengeTypes = null
                )
            )
        )

        page.totalElements shouldBe 1
        page.content.size shouldBe 1
        page.content[0].let {
            it.id shouldBe tradingAccount.id
            it.accountId shouldBe "123456"
            it.platform shouldBe PlatformType.META_TRADER_5
            it.challengeType shouldBe ChallengeType.FOREX
            it.state shouldBe TradingAccountState.ACTIVE
            it.currentBalance shouldBeEqualComparingTo 555.toBigDecimal()
            it.challengeStepType shouldBe ChallengeStepType.FUNDED
            it.challengeStepNumber shouldBe 2
            it.lastTradeOrFutureOrderCloseTime.shouldBeNull()
            it.createdAt truncatedShouldBe tradingAccount.createdAt
            it.updatedAt truncatedShouldBe tradingAccount.updatedAt
            it.user.id shouldBe user.id
            it.user.firstName shouldBe "Johny"
            it.user.lastName shouldBe "Cash"
            it.user.blacklisted shouldBe false
            it.breach.shouldNotBeNull {
                breachedAt truncatedShouldBe Instant.parse("2021-01-01T00:00:00Z")
                breachType shouldBe BreachType.MANUAL_BREACH
                internalReason shouldBe "internal reason"
                externalReason shouldBe "external reason"
            }
        }
    }

    "should correctly return paged result with trading accounts according to given filter" - {
        val tradingAccountFactory: (Int, String, String, TradingAccountState, ChallengeType) -> TradingAccount =
            { index, firstName, lastName, state, challengeType ->
                val challenge = testDataHelper.getChallenge(index = index, type = challengeType)
                val challengePlan = testDataHelper.getChallengePlan(index = index, challengeId = challenge.id)
                val challengeStep = testDataHelper.getChallengeStep(challengePlanId = challengePlan.id)
                val user = testDataHelper.getUser(index = index, firstName = firstName, lastName = lastName)
                val order = testDataHelper.getOrder(
                    userId = user.id,
                    challengeId = challengePlan.challengeId,
                    challengePlanId = challengePlan.id
                )

                val tradingAccount = testDataHelper.getTradingAccount(
                    orderId = order.id,
                    challengeStepId = challengeStep.id,
                    accountId = "$index".repeat(5),
                    state = state
                )

                testDataHelper.getTrade(
                    tradingAccountId = tradingAccount.id,
                    dealId = index.toString(),
                    positionId = index.toString(),
                    closeTime = Instant.parse("202$index-12-24T18:15:30Z")
                )

                tradingAccount
            }

        val account1 = tradingAccountFactory(1, "Peter", "Poor", TradingAccountState.ACTIVE, ChallengeType.FOREX)
        val account2 = tradingAccountFactory(2, "Jack", "Trader", TradingAccountState.BREACHED, ChallengeType.CRYPTO)
        val account3 = tradingAccountFactory(3, "Johnson", "Looser", TradingAccountState.ACTIVE, ChallengeType.FOREX)
        val account4 = tradingAccountFactory(4, "Jennifer", "Lopez", TradingAccountState.BREACHED, ChallengeType.CRYPTO)
        testDataHelper.getBreach(
            tradingAccountId = account2.id,
            type = BreachType.MAX_DRAWDOWN
        )
        testDataHelper.getBreach(
            tradingAccountId = account4.id,
            type = BreachType.DAILY_DRAWDOWN
        )

        "no filter" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = null,
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = null,
                        challengeTypes = null
                    )
                )
            )

            page.totalElements shouldBe 4
            page.content.size shouldBe 4
            page.content.map { it.id } shouldContainOnly listOf(account1, account2, account3, account4).map { it.id }
        }

        "trading account states filter" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = setOf(TradingAccountState.BREACHED),
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = null,
                        challengeTypes = null
                    )
                )
            )

            page.totalElements shouldBe 2
            page.content.size shouldBe 2
            page.content.map { it.id } shouldContainOnly listOf(account2, account4).map { it.id }
        }

        "trading account breach type filter" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = null,
                        breachTypes = setOf(BreachType.DAILY_DRAWDOWN),
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = null,
                        challengeTypes = null
                    )
                )
            )

            page.totalElements shouldBe 1
            page.content.size shouldBe 1
            page.content[0].id shouldBe account4.id
        }

        "createdAtFrom - createdAtTo filter" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = account2.createdAt,
                        createdAtTo = account3.createdAt,
                        states = null,
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = null,
                        challengeTypes = null
                    )
                )
            )

            page.totalElements shouldBe 2
            page.content.size shouldBe 2
            page.content.map { it.id } shouldContainOnly listOf(account2, account3).map { it.id }
        }

        "fulltext with first name filter" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = null,
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = "PETER",
                        challengeTypes = null
                    )
                )
            )

            page.totalElements shouldBe 1
            page.content.size shouldBe 1
            page.content.map { it.id } shouldContainOnly listOf(account1).map { it.id }
        }

        "fulltext with last name filter" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = null,
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = "trader",
                        challengeTypes = null
                    )
                )
            )

            page.totalElements shouldBe 1
            page.content.size shouldBe 1
            page.content.map { it.id } shouldContainOnly listOf(account2).map { it.id }
        }

        "fulltext with full name filter" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = null,
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = "jennifer lopez",
                        challengeTypes = null
                    )
                )
            )

            page.totalElements shouldBe 1
            page.content.size shouldBe 1
            page.content.map { it.id } shouldContainOnly listOf(account4).map { it.id }
        }

        "fulltext with accountId filter" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = null,
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = "33333",
                        challengeTypes = null
                    )
                )
            )

            page.totalElements shouldBe 1
            page.content.size shouldBe 1
            page.content.map { it.id } shouldContainOnly listOf(account3).map { it.id }
        }

        "fulltext with blank string filter" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = null,
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = "   ",
                        challengeTypes = null
                    )
                )
            )

            page.totalElements shouldBe 4
            page.content.size shouldBe 4
            page.content.map { it.id } shouldContainOnly listOf(account1, account2, account3, account4).map { it.id }
        }

        "lastTradeCloseTimeFrom" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = null,
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = Instant.parse("2023-12-24T18:15:30Z"),
                        fulltext = null,
                        challengeTypes = null
                    )
                )
            )

            page.totalElements shouldBe 2
            page.content.size shouldBe 2
            page.content.map { it.id } shouldContainOnly listOf(account3, account4).map { it.id }
        }

        "all filters" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = account2.createdAt,
                        createdAtTo = account4.createdAt,
                        states = setOf(TradingAccountState.BREACHED),
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = "44444",
                        challengeTypes = null
                    )
                )
            )

            page.totalElements shouldBe 1
            page.content.size shouldBe 1
            page.content.map { it.id } shouldContainOnly listOf(account4).map { it.id }
        }

        "challenge types filter - FOREX" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = null,
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = null,
                        challengeTypes = setOf(ChallengeType.FOREX)
                    )
                )
            )
            page.totalElements shouldBe 2
            page.content.size shouldBe 2
            page.content.map { it.id } shouldContainOnly listOf(account1.id, account3.id)
        }

        "challenge types filter - CRYPTO" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = null,
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = null,
                        challengeTypes = setOf(ChallengeType.CRYPTO)
                    )
                )
            )
            page.totalElements shouldBe 2
            page.content.size shouldBe 2
            page.content.map { it.id } shouldContainOnly listOf(account2.id, account4.id)
        }

        "challenge types filter - FOREX and CRYPTO" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = null,
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = null,
                        challengeTypes = setOf(ChallengeType.FOREX, ChallengeType.CRYPTO)
                    )
                )
            )
            page.totalElements shouldBe 4
            page.content.size shouldBe 4
            page.content.map { it.id } shouldContainOnly listOf(account1.id, account2.id, account3.id, account4.id)
        }

        "combined filter with challenge type" {
            val page = queryBus(
                AdminSearchTradingAccountsQuery(
                    pageable = Pageable.unpaged(),
                    filter = AdminSearchTradingAccountsQuery.Filter(
                        createdAtFrom = null,
                        createdAtTo = null,
                        states = setOf(TradingAccountState.ACTIVE),
                        breachTypes = null,
                        challengeStepTypes = null,
                        lastTradeCloseTimeFrom = null,
                        fulltext = "Peter",
                        challengeTypes = setOf(ChallengeType.FOREX)
                    )
                )
            )
            page.totalElements shouldBe 1
            page.content.size shouldBe 1
            page.content[0].id shouldBe account1.id
        }
    }
}, cleanAfterEachWithDataTestCase = false)
