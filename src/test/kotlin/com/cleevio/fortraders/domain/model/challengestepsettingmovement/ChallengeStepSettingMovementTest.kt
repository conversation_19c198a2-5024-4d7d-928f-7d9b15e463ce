package com.cleevio.fortraders.domain.model.challengestepsettingmovement

import com.cleevio.fortraders.UnitTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.ChallengeStepSettingType
import com.cleevio.fortraders.domain.model.challengestepsettingmovement.constant.ChallengeStepSettingMovementType
import com.cleevio.fortraders.domain.model.challengestepsettingmovement.exception.ChallengeStepSettingMovementTypeNotAllowedForGivenSettingException
import com.cleevio.fortraders.domain.model.challengestepsettingmovement.exception.ChallengeStepSettingMovementTypeRequiredValuesInvalidException
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.data.row
import io.kotest.datatest.withData
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import java.math.BigDecimal

class ChallengeStepSettingMovementTest : UnitTest({

    "should correctly disable default" {
        val underTest = createChallengeStepSettingMovement()
        underTest.isDefault shouldBe true

        underTest.disableDefault()

        underTest.isDefault shouldBe false
    }

    "should throw because creating entity with invalid arguments" - {
        withData(
            nameFn = { it.toString() },
            row(ChallengeStepSettingMovementType.PERCENTAGE, null, "WEEKLY", null, null),
            row(ChallengeStepSettingMovementType.PERCENTAGE, null, "WEEKLY", null, 22L.toBigDecimal()),
            row(ChallengeStepSettingMovementType.PERCENTAGE, null, "WEEKLY", 20, null),
            row(ChallengeStepSettingMovementType.PERCENTAGE, 50, null, null, null),
            row(ChallengeStepSettingMovementType.PERCENTAGE, 50, null, 100, null),
            row(ChallengeStepSettingMovementType.PERCENTAGE, 50, null, -100, null),
            row(ChallengeStepSettingMovementType.LIST, 50, null, null, null),
            row(ChallengeStepSettingMovementType.LIST, 50, null, null, 22L.toBigDecimal()),
            row(ChallengeStepSettingMovementType.LIST, 50, null, 20, null),
            row(ChallengeStepSettingMovementType.LIST, null, "WEEKLY", null, null),
            row(ChallengeStepSettingMovementType.LIST, null, "WEEKLY", 100, null),
            row(ChallengeStepSettingMovementType.LIST, null, "WEEKLY", -100, null),
            row(ChallengeStepSettingMovementType.LIST, null, "weekly", 50, null),
            row(ChallengeStepSettingMovementType.NONE, 50, null, 50, null),
            row(ChallengeStepSettingMovementType.NONE, null, "weekly", 50, null),
            row(ChallengeStepSettingMovementType.NONE, null, null, 100, null),
            row(ChallengeStepSettingMovementType.NONE, null, null, -100, null)
        ) { (challengeStepSettingMovementType, percentageValue, listValue, movementPercentageValue, movementAbsoluteValue) ->
            shouldThrowExactly<ChallengeStepSettingMovementTypeRequiredValuesInvalidException> {
                ChallengeStepSettingMovement(
                    challengeStepSettingId = "db664419-8296-4422-934e-50c010d60a9c".toUUID(),
                    challengePlanBasePrice = 100L.toBigDecimal(),
                    type = challengeStepSettingMovementType,
                    settingType = challengeStepSettingMovementType.getAllowedSettingType(),
                    percentageValue = percentageValue,
                    movementPercentageValue = movementPercentageValue,
                    movementAbsoluteValue = movementAbsoluteValue,
                    listValue = listValue,
                    isDefault = true
                )
            }
        }
    }

    "should throw because creating entity with invalid setting type for PERCENTAGE movement type" {
        shouldThrowExactly<ChallengeStepSettingMovementTypeNotAllowedForGivenSettingException> {
            ChallengeStepSettingMovement(
                challengeStepSettingId = "db664419-8296-4422-934e-50c010d60a9c".toUUID(),
                challengePlanBasePrice = 100L.toBigDecimal(),
                type = ChallengeStepSettingMovementType.PERCENTAGE,
                settingType = ChallengeStepSettingType.PAYOUTS,
                percentageValue = 50,
                listValue = null,
                movementPercentageValue = 25,
                movementAbsoluteValue = null,
                isDefault = true
            )
        }
    }

    "should throw because creating entity with invalid setting type for LIST movement type" - {
        withData(
            nameFn = { it.toString() },
            ChallengeStepSettingType.entries.filter { it != ChallengeStepSettingType.PAYOUTS }
        ) { challengeStepSettingMovementType ->
            shouldThrowExactly<ChallengeStepSettingMovementTypeNotAllowedForGivenSettingException> {
                ChallengeStepSettingMovement(
                    challengeStepSettingId = "db664419-8296-4422-934e-50c010d60a9c".toUUID(),
                    challengePlanBasePrice = 100L.toBigDecimal(),
                    type = ChallengeStepSettingMovementType.LIST,
                    settingType = challengeStepSettingMovementType,
                    percentageValue = null,
                    listValue = "WEEKLY",
                    movementPercentageValue = 25,
                    movementAbsoluteValue = null,
                    isDefault = true
                )
            }
        }
    }

    "should throw because creating entity with invalid setting type for NONE movement type" - {
        withData(
            nameFn = { it.toString() },
            ChallengeStepSettingType.entries.filter {
                it !in setOf(
                    ChallengeStepSettingType.REFUND,
                    ChallengeStepSettingType.DAILY_PAUSE,
                    ChallengeStepSettingType.DAILY_DRAWDOWN,
                    ChallengeStepSettingType.DAILY_PROFIT_CAP
                )
            }
        ) { challengeStepSettingMovementType ->
            shouldThrowExactly<ChallengeStepSettingMovementTypeNotAllowedForGivenSettingException> {
                ChallengeStepSettingMovement(
                    challengeStepSettingId = "db664419-8296-4422-934e-50c010d60a9c".toUUID(),
                    challengePlanBasePrice = 100L.toBigDecimal(),
                    type = ChallengeStepSettingMovementType.NONE,
                    settingType = challengeStepSettingMovementType,
                    percentageValue = null,
                    listValue = null,
                    movementPercentageValue = 25,
                    movementAbsoluteValue = null,
                    isDefault = true
                )
            }
        }
    }

    "should throw because updating properties with invalid arguments" - {
        withData(
            nameFn = { it.toString() },
            row(ChallengeStepSettingMovementType.PERCENTAGE, null, "WEEKLY", null, null),
            row(ChallengeStepSettingMovementType.PERCENTAGE, null, "WEEKLY", null, 22L.toBigDecimal()),
            row(ChallengeStepSettingMovementType.PERCENTAGE, null, "WEEKLY", 20, null),
            row(ChallengeStepSettingMovementType.PERCENTAGE, 50, null, null, null),
            row(ChallengeStepSettingMovementType.PERCENTAGE, 50, null, 100, null),
            row(ChallengeStepSettingMovementType.PERCENTAGE, 50, null, -100, null),
            row(ChallengeStepSettingMovementType.LIST, 50, null, null, null),
            row(ChallengeStepSettingMovementType.LIST, 50, null, null, 22L.toBigDecimal()),
            row(ChallengeStepSettingMovementType.LIST, 50, null, 20, null),
            row(ChallengeStepSettingMovementType.LIST, null, "WEEKLY", null, null),
            row(ChallengeStepSettingMovementType.LIST, null, "WEEKLY", 100, null),
            row(ChallengeStepSettingMovementType.LIST, null, "WEEKLY", -100, null),
            row(ChallengeStepSettingMovementType.LIST, null, "monthly", 50, null),
            row(ChallengeStepSettingMovementType.NONE, null, null, 100, null),
            row(ChallengeStepSettingMovementType.NONE, null, null, -100, null)
        ) { (challengeStepSettingMovementType, percentageValue, listValue, movementPercentageValue, movementAbsoluteValue) ->
            val underTest = createChallengeStepSettingMovement()

            shouldThrowExactly<ChallengeStepSettingMovementTypeRequiredValuesInvalidException> {
                underTest.updateProperties(
                    challengePlanBasePrice = 100L.toBigDecimal(),
                    type = challengeStepSettingMovementType,
                    settingType = challengeStepSettingMovementType.getAllowedSettingType(),
                    percentageValue = percentageValue,
                    listValue = listValue,
                    movementPercentageValue = movementPercentageValue,
                    movementAbsoluteValue = movementAbsoluteValue,
                    isDefault = false
                )
            }
        }
    }

    "should correctly update and recalculate properties" - {
        withData(
            nameFn = { it.toString() },
            row(100L.toBigDecimal(), 50, 50L.toBigDecimal()),
            row(100L.toBigDecimal(), 20, 20L.toBigDecimal()),
            row(123.123.toBigDecimal(), 50, 62.toBigDecimal()),
            row(456.7899.toBigDecimal(), 9, 42.toBigDecimal()),
            row(100L.toBigDecimal(), -50, (-50L).toBigDecimal()),
            row(100L.toBigDecimal(), -20, (-20L).toBigDecimal()),
            row(123.123.toBigDecimal(), -50, (-62).toBigDecimal()),
            row(456.7899.toBigDecimal(), -9, (-42).toBigDecimal())
        ) { (challengePlanBasePrice, movementPercentageValue, expectedMovementAbsoluteValue) ->
            val underTest = createChallengeStepSettingMovement()

            underTest.updateProperties(
                challengePlanBasePrice = challengePlanBasePrice,
                type = ChallengeStepSettingMovementType.PERCENTAGE,
                settingType = ChallengeStepSettingType.MAX_DRAWDOWN,
                percentageValue = 23,
                listValue = null,
                movementPercentageValue = movementPercentageValue,
                movementAbsoluteValue = null,
                isDefault = false
            )

            underTest.type shouldBe ChallengeStepSettingMovementType.PERCENTAGE
            underTest.percentageValue shouldBe 23
            underTest.listValue.shouldBeNull()
            underTest.movementPercentageValue shouldBe movementPercentageValue
            underTest.movementAbsoluteValue shouldBeEqualComparingTo expectedMovementAbsoluteValue
            underTest.isDefault shouldBe false
        }
    }

    "should correctly prefer movement percentage value to absolute value" - {
        val underTest = createChallengeStepSettingMovement()

        underTest.updateProperties(
            challengePlanBasePrice = 100L.toBigDecimal(),
            type = ChallengeStepSettingMovementType.LIST,
            settingType = ChallengeStepSettingType.PAYOUTS,
            percentageValue = null,
            listValue = "BI_WEEKLY",
            movementPercentageValue = 15,
            movementAbsoluteValue = 50L.toBigDecimal(),
            isDefault = false
        )

        underTest.listValue shouldBe "BI_WEEKLY"
        underTest.percentageValue.shouldBeNull()
        underTest.movementPercentageValue shouldBe 15
        underTest.movementAbsoluteValue shouldBeEqualComparingTo 15L.toBigDecimal()
    }

    "should correctly remove properties not related when updating to list type" - {
        val underTest = ChallengeStepSettingMovement(
            challengeStepSettingId = "db664419-8296-4422-934e-50c010d60a9c".toUUID(),
            challengePlanBasePrice = 100L.toBigDecimal(),
            type = ChallengeStepSettingMovementType.PERCENTAGE,
            settingType = ChallengeStepSettingType.MAX_DRAWDOWN,
            percentageValue = 50,
            movementPercentageValue = 12,
            movementAbsoluteValue = null,
            listValue = null,
            isDefault = true
        )
        underTest.listValue.shouldBeNull()
        underTest.percentageValue shouldBe 50
        underTest.movementPercentageValue shouldBe 12
        underTest.movementAbsoluteValue shouldBeEqualComparingTo 12L.toBigDecimal()

        underTest.updateProperties(
            challengePlanBasePrice = 100L.toBigDecimal(),
            type = ChallengeStepSettingMovementType.LIST,
            settingType = ChallengeStepSettingType.PAYOUTS,
            percentageValue = 99,
            listValue = "BI_WEEKLY",
            movementPercentageValue = null,
            movementAbsoluteValue = 50L.toBigDecimal(),
            isDefault = false
        )

        underTest.listValue shouldBe "BI_WEEKLY"
        underTest.percentageValue.shouldBeNull()
        underTest.movementPercentageValue.shouldBeNull()
        underTest.movementAbsoluteValue shouldBeEqualComparingTo 50L.toBigDecimal()
    }

    "should correctly remove properties not related when updating to percentage type" - {
        val underTest = ChallengeStepSettingMovement(
            challengeStepSettingId = "db664419-8296-4422-934e-50c010d60a9c".toUUID(),
            challengePlanBasePrice = 100L.toBigDecimal(),
            type = ChallengeStepSettingMovementType.LIST,
            settingType = ChallengeStepSettingType.PAYOUTS,
            percentageValue = null,
            movementPercentageValue = 12,
            movementAbsoluteValue = null,
            listValue = "WEEKLY",
            isDefault = true
        )
        underTest.listValue shouldBe "WEEKLY"
        underTest.percentageValue.shouldBeNull()
        underTest.movementPercentageValue shouldBe 12
        underTest.movementAbsoluteValue shouldBeEqualComparingTo 12L.toBigDecimal()

        underTest.updateProperties(
            challengePlanBasePrice = 100L.toBigDecimal(),
            type = ChallengeStepSettingMovementType.PERCENTAGE,
            settingType = ChallengeStepSettingType.MAX_DRAWDOWN,
            percentageValue = 99,
            listValue = null,
            movementPercentageValue = null,
            movementAbsoluteValue = 50L.toBigDecimal(),
            isDefault = false
        )

        underTest.listValue.shouldBeNull()
        underTest.percentageValue shouldBe 99
        underTest.movementPercentageValue.shouldBeNull()
        underTest.movementAbsoluteValue shouldBeEqualComparingTo 50L.toBigDecimal()
    }
})

private fun ChallengeStepSettingMovementType.getAllowedSettingType() = when (this) {
    ChallengeStepSettingMovementType.PERCENTAGE -> ChallengeStepSettingType.MAX_DRAWDOWN
    ChallengeStepSettingMovementType.LIST -> ChallengeStepSettingType.PAYOUTS
    ChallengeStepSettingMovementType.NONE -> ChallengeStepSettingType.DAILY_DRAWDOWN
}

private fun createChallengeStepSettingMovement(challengePlanBasePrice: BigDecimal = 100L.toBigDecimal()) =
    ChallengeStepSettingMovement(
        challengeStepSettingId = "db664419-8296-4422-934e-50c010d60a9c".toUUID(),
        challengePlanBasePrice = challengePlanBasePrice,
        type = ChallengeStepSettingMovementType.LIST,
        settingType = ChallengeStepSettingType.PAYOUTS,
        percentageValue = null,
        movementPercentageValue = null,
        movementAbsoluteValue = 50L.toBigDecimal(),
        listValue = "WEEKLY",
        isDefault = true
    )
