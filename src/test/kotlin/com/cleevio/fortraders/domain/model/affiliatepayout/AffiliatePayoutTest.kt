package com.cleevio.fortraders.domain.model.affiliatepayout

import com.cleevio.fortraders.UnitTest
import com.cleevio.fortraders.domain.model.affiliatepayout.constant.AffiliatePayoutState
import com.cleevio.fortraders.domain.model.affiliatepayout.exception.AffiliatePayoutAmountLessThanAllowedMinimumException
import com.cleevio.fortraders.domain.model.affiliatepayout.exception.AffiliatePayoutMinimumDurationNotSatisfiedException
import com.cleevio.fortraders.domain.model.affiliatepayout.exception.AffiliatePayoutStateChangeNotAllowedException
import com.cleevio.fortraders.toUUID
import io.kotest.assertions.throwables.shouldNotThrowAny
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.extensions.time.withConstantNow
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import java.math.BigDecimal
import java.time.Instant

class AffiliatePayoutTest : UnitTest({

    "should correctly create affiliate payout" {
        shouldNotThrowAny {
            withConstantNow(Instant.parse("2021-10-15T10:00:00Z")) {
                createAffiliatePayout(
                    minimalPayoutAmount = 100.1.toBigDecimal(),
                    amount = 100.11.toBigDecimal(),
                    nextAffiliatePayoutAvailableAt = Instant.parse("2021-10-15T10:00:00Z")
                )
            }
        }
    }

    "should throw when trying to create affiliate payout with amount less than allowed" {
        shouldThrowExactly<AffiliatePayoutAmountLessThanAllowedMinimumException> {
            withConstantNow(Instant.parse("2021-10-15T10:00:00Z")) {
                createAffiliatePayout(
                    minimalPayoutAmount = 100.1.toBigDecimal(),
                    amount = 100.09.toBigDecimal(),
                    nextAffiliatePayoutAvailableAt = Instant.parse("2021-10-15T10:00:00Z")
                )
            }
        }
    }

    "should throw when trying to create affiliate payout with affiliate created less than minimal duration" {
        shouldThrowExactly<AffiliatePayoutMinimumDurationNotSatisfiedException> {
            withConstantNow(Instant.parse("2021-10-15T10:00:00Z")) {
                createAffiliatePayout(
                    minimalPayoutAmount = 100.1.toBigDecimal(),
                    amount = 100.11.toBigDecimal(),
                    nextAffiliatePayoutAvailableAt = Instant.parse("2021-10-15T10:00:01Z")
                )
            }
        }
    }

    "should throw when trying to change to invalid state or from invalid state" {
        val underTest = createAffiliatePayout()
        underTest.state shouldBe AffiliatePayoutState.REQUESTED
        underTest.internalNote.shouldBeNull()
        underTest.externalNote.shouldBeNull()

        shouldNotThrowAny {
            underTest.decline(
                internalNote = "Internal note",
                externalNote = "External note"
            )
        }
        underTest.state shouldBe AffiliatePayoutState.DECLINED
        underTest.internalNote shouldBe "Internal note"
        underTest.externalNote shouldBe "External note"

        shouldThrowExactly<AffiliatePayoutStateChangeNotAllowedException> {
            underTest.approve(
                transactionId = 1.toUUID(),
                internalNote = "Internal note",
                externalNote = "External note"
            )
        }
    }
})

private fun createAffiliatePayout(
    minimalPayoutAmount: BigDecimal = 100.1.toBigDecimal(),
    amount: BigDecimal = 100.11.toBigDecimal(),
    nextAffiliatePayoutAvailableAt: Instant = Instant.parse("2021-10-08T10:00:01Z"),
) = AffiliatePayout(
    affiliateId = 1.toUUID(),
    minimalPayoutAmount = minimalPayoutAmount,
    amount = amount,
    nextAffiliatePayoutAvailableAt = nextAffiliatePayoutAvailableAt
)
