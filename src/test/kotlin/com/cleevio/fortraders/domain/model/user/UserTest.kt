package com.cleevio.fortraders.domain.model.user

import com.cleevio.fortraders.UnitTest
import com.cleevio.fortraders.domain.model.user.constant.UserKycState
import com.cleevio.fortraders.domain.model.user.exception.UserKycVerificationAlreadyFinishedException
import com.cleevio.fortraders.setAndReturnPrivateProperty
import io.kotest.assertions.throwables.shouldNotThrowAny
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.datatest.withData
import io.kotest.matchers.shouldBe

class UserTest : UnitTest({

    "should correctly set KYC verification" {
        createUser().let {
            it.setAndReturnPrivateProperty("kycState", UserKycState.PENDING)
            it.setIsKycVerified(true)
            it.kycState shouldBe UserKycState.APPROVED
        }

        createUser().let {
            it.setAndReturnPrivateProperty("kycState", UserKycState.PENDING)
            it.setIsKycVerified(false)
            it.kycState shouldBe UserKycState.FAILED
        }
    }

    "should throw when trying to request KYC verification on already verified user" - {
        withData(UserKycState.APPROVED, UserKycState.FAILED) { kycState ->
            val underTest = createUser()
            underTest.setAndReturnPrivateProperty("kycState", kycState)
            underTest.kycState shouldBe kycState

            shouldThrowExactly<UserKycVerificationAlreadyFinishedException> {
                underTest.requestKycVerification()
            }

            underTest.kycState shouldBe kycState
        }
    }

    "should correctly request KYC verification" - {
        withData(UserKycState.entries.filter { it !in setOf(UserKycState.APPROVED, UserKycState.FAILED) }) { kycState ->
            val underTest = createUser()
            underTest.setAndReturnPrivateProperty("kycState", kycState)
            underTest.kycState shouldBe kycState

            shouldNotThrowAny {
                underTest.requestKycVerification()
            }
            underTest.kycState shouldBe UserKycState.PENDING
        }
    }

    "should correctly return full name and anonymized name" {
        createUser(firstName = "John", lastName = "Doe").let {
            it.getFullName() shouldBe "John Doe"
            it.getAnonymizedName() shouldBe "John D."
        }

        createUser(firstName = "John", lastName = null).let {
            it.getFullName() shouldBe "John"
            it.getAnonymizedName() shouldBe "John"
        }

        createUser(firstName = null, lastName = "Doe").let {
            it.getFullName() shouldBe "Doe"
            it.getAnonymizedName() shouldBe "D."
        }
    }
})

private fun createUser(firstName: String? = "John", lastName: String? = "Doe") = User(
    email = "<EMAIL>",
    firebaseId = "1234567aabbcc",
    role = null,
    firstName = firstName,
    lastName = lastName,
    profileImageFileId = null
)
