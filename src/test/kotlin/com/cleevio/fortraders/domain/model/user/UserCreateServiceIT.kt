package com.cleevio.fortraders.domain.model.user

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.domain.model.user.constant.UserRole
import com.cleevio.fortraders.domain.model.wallet.WalletRepository
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify

class UserCreateServiceIT(
    private val userRepository: UserRepository,
    private val walletRepository: WalletRepository,
    private val underTest: UserCreateService,
) : IntegrationTest({

    "should correctly create new user and enable in firebase" {
        every { firebaseServiceMock.updateAndEnableUser(any(), any()) } just runs
        userRepository.count() shouldBe 0
        walletRepository.count() shouldBe 0

        val firebaseId = "AAA123"
        underTest.create(
            email = "<EMAIL>",
            firebaseId = firebaseId,
            role = UserRole.MANAGER,
            profileImageFileId = null,
            firstName = "John",
            lastName = "Doe",
            isBlockedInFirebase = true
        )

        val users = userRepository.findAll()
        users shouldHaveSize 1
        users[0].let {
            it.email shouldBe "<EMAIL>"
            it.firebaseId shouldBe firebaseId
            it.role shouldBe UserRole.MANAGER
            it.firstName shouldBe "John"
            it.lastName shouldBe "Doe"
            it.profileImageFileId.shouldBeNull()
        }

        val wallets = walletRepository.findAll()
        wallets shouldHaveSize 1
        wallets[0].userId shouldBe users[0].id

        verify {
            firebaseServiceMock.updateAndEnableUser(firebaseId = firebaseId, appUserId = users[0].id)
        }
    }

    "should correctly create new user and NOT enable in firebase" {
        every { firebaseServiceMock.updateAndEnableUser(any(), any()) } just runs
        userRepository.count() shouldBe 0
        walletRepository.count() shouldBe 0

        val firebaseId = "AAA123"
        underTest.create(
            email = "<EMAIL>",
            firebaseId = firebaseId,
            role = UserRole.MANAGER,
            profileImageFileId = null,
            firstName = "John",
            lastName = "Doe",
            isBlockedInFirebase = false
        )

        val users = userRepository.findAll()
        users shouldHaveSize 1
        users[0].let {
            it.email shouldBe "<EMAIL>"
            it.firebaseId shouldBe firebaseId
            it.role shouldBe UserRole.MANAGER
            it.firstName shouldBe "John"
            it.lastName shouldBe "Doe"
            it.profileImageFileId.shouldBeNull()
        }

        val wallets = walletRepository.findAll()
        wallets shouldHaveSize 1
        wallets[0].userId shouldBe users[0].id

        verify(exactly = 0) {
            firebaseServiceMock.updateAndEnableUser(any(), any())
        }
    }
})
