package com.cleevio.fortraders.domain.model.tournament

import com.cleevio.fortraders.UnitTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentPhase
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentPrizePoolType
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentReward
import com.cleevio.fortraders.domain.model.tournament.exception.TournamentWithInvalidInitialPrizePoolException
import com.cleevio.fortraders.domain.model.tournament.exception.TournamentWithInvalidStartEndTimestampsException
import com.cleevio.fortraders.setAndReturnPrivateProperty
import io.kotest.assertions.throwables.shouldNotThrowAny
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.data.row
import io.kotest.datatest.withData
import io.kotest.extensions.time.withConstantNow
import io.kotest.matchers.shouldBe
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit

class TournamentTest : UnitTest({

    "should throw because initial prize pool and reward tournaments values are invalid" {
        shouldThrowExactly<TournamentWithInvalidInitialPrizePoolException> {
            createTournament(initialPrizePool = null, reward = TournamentReward.MONEY)
        }
    }

    "should correctly create tournament with valid initial prize pool and reward tournaments values" - {
        withData(
            nameFn = { it.toString() },
            row(null, TournamentReward.CHALLENGES),
            row(BigDecimal.TEN, TournamentReward.MONEY),
            row(BigDecimal.TEN, TournamentReward.CHALLENGES)
        ) { (initialPrizePool, reward) ->
            shouldNotThrowAny {
                createTournament(initialPrizePool = initialPrizePool, reward = reward)
            }
        }
    }

    "should correctly validate startsAt and endsAt dates" - {
        val now = Instant.now()
        val past = now.minus(1, ChronoUnit.DAYS)
        val future = now.plus(1, ChronoUnit.DAYS)
        val furtherFuture = now.plus(2, ChronoUnit.DAYS)

        "should throw because startsAt is after endsAt" - {
            withData(
                nameFn = { it.toString() },
                row(past, past),
                row(future, past),
                row(past, future),
                row(future, future)
            ) { (startsAt, endsAt) ->
                shouldThrowExactly<TournamentWithInvalidStartEndTimestampsException> {
                    createTournament(startsAt = startsAt, endsAt = endsAt)
                }
            }
        }

        "should correctly create tournament with startsAt before endsAt" {
            shouldNotThrowAny {
                createTournament(startsAt = future, endsAt = furtherFuture)
            }
        }
    }

    "should correctly update all tournament properties" {
        val startsAt = Instant.now().plus(1, ChronoUnit.DAYS)
        val endsAt = startsAt.plus(10, ChronoUnit.DAYS)
        val underTest = Tournament(
            gameId = "db22f458-033d-42f6-bcbb-74cc4be53e79".toUUID(),
            coverFileId = "32bcc464-e423-4ca0-ac0a-3e22864dd636".toUUID(),
            name = "New Tournament",
            reward = TournamentReward.CHALLENGES,
            initialPrizePool = null,
            prizePoolType = TournamentPrizePoolType.SINGLE_TIER,
            entryFee = BigDecimal.TEN,
            buyInsLimit = null,
            maxDrawdownPercentage = 50,
            rules = null,
            startsAt = startsAt,
            endsAt = endsAt,
            leverageEnabled = false
        )

        underTest.updateProperties(
            gameId = "503c39e1-d1ee-4923-84f4-e611e200ccd1".toUUID(),
            coverFileId = "ab59bbbb-0520-4f8e-bf46-28de66198421".toUUID(),
            name = "Updated Tournament",
            reward = TournamentReward.MONEY,
            initialPrizePool = BigDecimal(1500),
            prizePoolType = TournamentPrizePoolType.MULTI_TIER,
            entryFee = BigDecimal(100),
            buyInsLimit = 5,
            maxDrawdownPercentage = 25,
            rules = "Added tournament rules",
            startsAt = startsAt.plus(1, ChronoUnit.DAYS),
            endsAt = endsAt.plus(1, ChronoUnit.DAYS),
            leverageEnabled = true
        )

        underTest.let {
            it.gameId shouldBe "503c39e1-d1ee-4923-84f4-e611e200ccd1".toUUID()
            it.coverFileId shouldBe "ab59bbbb-0520-4f8e-bf46-28de66198421".toUUID()
            it.name shouldBe "Updated Tournament"
            it.reward shouldBe TournamentReward.MONEY
            it.initialPrizePool shouldBe BigDecimal(1500)
            it.prizePoolType shouldBe TournamentPrizePoolType.MULTI_TIER
            it.entryFee shouldBe BigDecimal(100)
            it.buyInsLimit shouldBe 5
            it.maxDrawdownPercentage shouldBe 25
            it.rules shouldBe "Added tournament rules"
            it.startsAt shouldBe startsAt.plus(1, ChronoUnit.DAYS)
            it.endsAt shouldBe endsAt.plus(1, ChronoUnit.DAYS)
            it.leverageEnabled shouldBe true
        }
    }

    "should correctly verify that tournament has ended" - {
        withData(
            nameFn = { it.toString() },
            row(Instant.parse("2023-01-06T21:44:13.064778Z"), Instant.parse("2023-01-06T21:44:13.064777Z"), false),
            row(Instant.parse("2023-01-06T21:44:13.064778Z"), Instant.parse("2023-01-06T21:44:12.064778Z"), false),
            row(Instant.parse("2023-01-06T21:44:13.064778Z"), Instant.parse("2023-01-06T21:44:13.064779Z"), true),
            row(Instant.parse("2023-01-06T21:44:13.064778Z"), Instant.parse("2023-01-06T21:44:14.064778Z"), true)
        ) { (endsAt, now, expected) ->
            val underTest = createTournament()
            underTest.setAndReturnPrivateProperty("startsAt", Instant.EPOCH)
            underTest.setAndReturnPrivateProperty("endsAt", endsAt)
            withConstantNow(now) {
                underTest.hasEnded() shouldBe expected
            }
        }
    }

    "should correctly verify that tournament is in progress" - {
        withData(
            nameFn = { it.toString() },
            row(
                Instant.parse("2023-01-06T21:44:00.000000Z"),
                Instant.parse("2023-01-06T21:44:13.000000Z"),
                Instant.parse("2023-01-06T21:43:13.000000Z"),
                false
            ),
            row(
                Instant.parse("2023-01-06T21:44:00.000000Z"),
                Instant.parse("2023-01-06T21:44:13.000000Z"),
                Instant.parse("2023-01-06T21:44:10.000000Z"),
                true
            ),
            row(
                Instant.parse("2023-01-06T21:44:00.000000Z"),
                Instant.parse("2023-01-06T21:44:13.000000Z"),
                Instant.parse("2023-01-06T21:44:12.999999Z"),
                true
            ),
            row(
                Instant.parse("2023-01-06T21:44:00.000000Z"),
                Instant.parse("2023-01-06T21:44:13.000000Z"),
                Instant.parse("2023-01-06T21:44:14.000000Z"),
                false
            )
        ) { (startsAt, endsAt, now, expected) ->
            val underTest = createTournament()
            underTest.setAndReturnPrivateProperty("startsAt", startsAt)
            underTest.setAndReturnPrivateProperty("endsAt", endsAt)
            withConstantNow(now) {
                underTest.isInProgress() shouldBe expected
            }
        }
    }

    "should correctly return tournament phase" - {
        withData(
            nameFn = { it.toString() },
            row(
                Instant.parse("2023-01-06T21:44:13.064777Z"),
                Instant.parse("2023-01-06T21:44:13.064778Z"),
                Instant.parse("2023-01-06T21:44:13.064776Z"),
                TournamentPhase.UPCOMING
            ),
            row(
                Instant.parse("2023-01-06T21:44:13.064776Z"),
                Instant.parse("2023-01-06T21:44:13.064778Z"),
                Instant.parse("2023-01-06T21:44:13.064777Z"),
                TournamentPhase.IN_PROGRESS
            ),
            row(
                Instant.parse("2023-01-06T21:44:13.064776Z"),
                Instant.parse("2023-01-06T21:44:13.064777Z"),
                Instant.parse("2023-01-06T21:44:13.064778Z"),
                TournamentPhase.FINISHED
            )
        ) { (startsAt, endsAt, now, expected) ->
            val underTest = createTournament()
            underTest.setAndReturnPrivateProperty("startsAt", startsAt)
            underTest.setAndReturnPrivateProperty("endsAt", endsAt)
            withConstantNow(now) {
                underTest.getPhase() shouldBe expected
            }
        }
    }

    "should correctly mark tournament as start processed" {
        val underTest = createTournament()
        underTest.startProcessed shouldBe false

        underTest.markStartProcessed()

        underTest.startProcessed shouldBe true
    }
})

private fun createTournament(
    initialPrizePool: BigDecimal? = BigDecimal.TEN,
    reward: TournamentReward = TournamentReward.CHALLENGES,
    startsAt: Instant = Instant.now().plus(1, ChronoUnit.DAYS),
    endsAt: Instant = Instant.now().plus(10, ChronoUnit.DAYS),
) = Tournament(
    gameId = "db22f458-033d-42f6-bcbb-74cc4be53e79".toUUID(),
    coverFileId = "32bcc464-e423-4ca0-ac0a-3e22864dd636".toUUID(),
    name = "New Tournament",
    reward = reward,
    initialPrizePool = initialPrizePool,
    prizePoolType = TournamentPrizePoolType.SINGLE_TIER,
    entryFee = BigDecimal.TEN,
    buyInsLimit = null,
    maxDrawdownPercentage = 50,
    rules = null,
    startsAt = startsAt,
    endsAt = endsAt,
    leverageEnabled = false
)
