package com.cleevio.fortraders.domain.model.contact

import com.cleevio.fortraders.IntegrationTest
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe

class ContactCreateServiceIT(
    private val contactRepository: ContactRepository,
    private val underTest: ContactCreateService,
) : IntegrationTest({

    "should correctly create new user contact" {
        val user = testDataHelper.getUser()
        val country = testDataHelper.getCountry()
        contactRepository.count() shouldBe 0

        val result = underTest.create(
            userId = user.id,
            phonePrefix = "+421",
            phoneNumber = "911234567",
            streetAddress = "Mojzisova 25",
            city = "Streda nad Bodrogom",
            postCode = "07631",
            countryId = country.id
        )

        val contacts = contactRepository.findAll()
        contacts shouldHaveSize 1
        contacts[0].let {
            it.id shouldBe result.id
            it.userId shouldBe user.id
            it.phonePrefix shouldBe "+421"
            it.phoneNumber shouldBe "911234567"
            it.streetAddress shouldBe "Mojzisova 25"
            it.city shouldBe "Streda nad Bodrogom"
            it.postCode shouldBe "07631"
            it.countryId shouldBe country.id
        }
    }
})
