package com.cleevio.fortraders.domain.model.discountcodeuser

import com.cleevio.fortraders.IntegrationTest
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe

class DiscountCodeUserCreateServiceIT(
    private val discountCodeUserRepository: DiscountCodeUserRepository,
    private val underTest: DiscountCodeUserCreateService,
) : IntegrationTest({

    "should correctly create discount code user" {
        val user = testDataHelper.getUser()
        val discountCode = testDataHelper.getDiscountCode()
        discountCodeUserRepository.count() shouldBe 0

        val discountCodeUser = underTest.create(discountCodeId = discountCode.id, userId = user.id)

        val discountCodeUsers = discountCodeUserRepository.findAll()
        discountCodeUsers shouldHaveSize 1
        discountCodeUsers[0].let {
            it.id shouldBe discountCodeUser.id
            it.discountCodeId shouldBe discountCode.id
            it.userId shouldBe user.id
        }
    }
})
