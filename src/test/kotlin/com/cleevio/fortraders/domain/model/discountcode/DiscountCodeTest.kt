package com.cleevio.fortraders.domain.model.discountcode

import com.cleevio.fortraders.UnitTest
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeState
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeType
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountUsageType
import com.cleevio.fortraders.domain.model.discountcode.exception.DiscountCodeRequiredValuesInvalidException
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.setAndReturnPrivateProperty
import com.cleevio.fortraders.toLocalDate
import com.cleevio.fortraders.toOptional
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.data.row
import io.kotest.datatest.withData
import io.kotest.extensions.time.withConstantNow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate

class DiscountCodeTest : UnitTest({

    "should throw because creating or patching discount code with invalid properties" - {
        withData(
            nameFn = { it.toString() },
            row(DiscountCodeType.FIXED, 50, null),
            row(DiscountCodeType.PERCENTAGE, null, 55.5.toBigDecimal())
        ) { (type, percentage, amount) ->
            shouldThrowExactly<DiscountCodeRequiredValuesInvalidException> {
                createDiscountCode(
                    type = type,
                    percentage = percentage,
                    amount = amount
                )
            }

            shouldThrowExactly<DiscountCodeRequiredValuesInvalidException> {
                createDiscountCode(
                    type = DiscountCodeType.FIXED,
                    percentage = null,
                    amount = 10.5.toBigDecimal()
                ).patchAdminSpecifiedProperties(
                    type = type,
                    percentage = percentage?.toOptional(),
                    amount = amount?.toOptional()
                )
            }
        }
    }

    "should correctly create or patch discount code" - {
        withData(
            nameFn = { it.toString() },
            row(DiscountCodeType.FIXED, null, 55.5.toBigDecimal()),
            row(DiscountCodeType.PERCENTAGE, 50, null)
        ) { (type, percentage, amount) ->
            createDiscountCode(
                type = type,
                percentage = percentage,
                amount = amount
            ).let {
                it.code shouldBe "TEST-CODE"
                it.type shouldBe type
                it.percentage shouldBe percentage
                it.amount shouldBe amount
            }

            createDiscountCode(
                type = DiscountCodeType.FIXED,
                percentage = null,
                amount = 10.5.toBigDecimal()
            ).let {
                it.patchAdminSpecifiedProperties(
                    type = type,
                    percentage = percentage?.toOptional(),
                    amount = amount?.toOptional()
                )
                it.type shouldBe type
                it.percentage shouldBe percentage
                it.amount shouldBe amount
            }
        }
    }

    "should correctly patch discount code" {
        val underTest = createDiscountCode()
        underTest.patchAdminSpecifiedProperties(
            type = DiscountCodeType.FIXED,
            description = "New description",
            validFrom = "2022-01-01".toLocalDate(),
            validUntil = "2022-12-31".toLocalDate(),
            state = DiscountCodeState.DISABLED,
            percentage = null,
            amount = 20.5.toBigDecimal().toOptional(),
            totalLimit = 100.toOptional(),
            userLimit = 1.toOptional(),
            platformTypes = setOf(PlatformType.DX_TRADE).toOptional()
        )

        underTest.type shouldBe DiscountCodeType.FIXED
        underTest.description shouldBe "New description"
        underTest.validFrom shouldBe "2022-01-01".toLocalDate()
        underTest.validUntil shouldBe "2022-12-31".toLocalDate()
        underTest.state shouldBe DiscountCodeState.DISABLED
        underTest.percentage.shouldBeNull()
        underTest.amount shouldBe 20.5.toBigDecimal()
        underTest.totalLimit shouldBe 100
        underTest.userLimit shouldBe 1
        underTest.platforms shouldBe setOf(PlatformType.DX_TRADE)
    }

    "should correctly calculate discounted price" - {
        "with percentage" {
            createDiscountCode(
                type = DiscountCodeType.PERCENTAGE,
                amount = null,
                percentage = 25
            ).calculateDiscountedPrice(100.toBigDecimal()) shouldBeEqualComparingTo 75.toBigDecimal()

            createDiscountCode(
                type = DiscountCodeType.PERCENTAGE,
                amount = null,
                percentage = 100
            ).calculateDiscountedPrice(200.toBigDecimal()) shouldBeEqualComparingTo BigDecimal.ZERO
        }

        "with fixed amount" {
            createDiscountCode(
                type = DiscountCodeType.FIXED,
                amount = 20.toBigDecimal(),
                percentage = null
            ).calculateDiscountedPrice(100.toBigDecimal()) shouldBeEqualComparingTo 80.toBigDecimal()

            createDiscountCode(
                type = DiscountCodeType.FIXED,
                amount = 20.toBigDecimal(),
                percentage = null
            ).calculateDiscountedPrice(10.toBigDecimal()) shouldBeEqualComparingTo 0.toBigDecimal()
        }
    }

    "should correctly return platform validation result" - {
        withData(
            nameFn = { it.toString() },
            row(PlatformType.DX_TRADE, setOf(PlatformType.META_TRADER_5, PlatformType.C_TRADER), false),
            row(PlatformType.DX_TRADE, null, true),
            row(PlatformType.DX_TRADE, setOf(PlatformType.DX_TRADE, PlatformType.C_TRADER), true)
        ) { (platform, discountCodePlatforms, expected) ->
            val underTest = createDiscountCode(platforms = discountCodePlatforms)

            underTest.hasValidPlatform(platform) shouldBe expected
        }
    }

    "should throw because discount code is invalid" - {
        withData(
            nameFn = { it.toString() },
            row(DiscountCodeState.DISABLED, "2021-02-16".toLocalDate(), "2021-02-19".toLocalDate()),
            row(DiscountCodeState.DISABLED, "2021-02-16".toLocalDate(), null),
            row(DiscountCodeState.ENABLED, "2021-02-16".toLocalDate(), "2021-02-17".toLocalDate()),
            row(DiscountCodeState.ENABLED, "2021-02-19".toLocalDate(), "2021-02-20".toLocalDate())
        ) { (state, validFrom, validUntil) ->
            val underTest = createDiscountCode(
                validFrom = validFrom,
                validUntil = validUntil
            )
            underTest.setAndReturnPrivateProperty("state", state)

            withConstantNow(Instant.parse("2021-02-18T01:00:00Z")) {
                underTest.isValid() shouldBe false
            }
        }
    }
})

private fun createDiscountCode(
    type: DiscountCodeType = DiscountCodeType.PERCENTAGE,
    usageType: DiscountUsageType = DiscountUsageType.GLOBAL,
    percentage: Int? = 50,
    amount: BigDecimal? = null,
    validFrom: LocalDate = LocalDate.EPOCH,
    validUntil: LocalDate? = LocalDate.MAX,
    platforms: Set<PlatformType>? = null,
): DiscountCode = DiscountCode(
    code = "test-code",
    type = type,
    usageType = usageType,
    description = "Best coupon",
    percentage = percentage,
    amount = amount,
    validFrom = validFrom,
    validUntil = validUntil,
    totalLimit = null,
    userLimit = null,
    applicableToFirstUserOrderOnly = false,
    platforms = platforms,
    perCountryLimit = null
)
