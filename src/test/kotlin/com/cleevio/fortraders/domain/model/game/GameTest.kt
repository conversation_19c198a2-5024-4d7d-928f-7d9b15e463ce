package com.cleevio.fortraders.domain.model.game

import com.cleevio.fortraders.UnitTest
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.domain.model.game.constant.GameState
import com.cleevio.fortraders.domain.model.game.constant.GameType
import io.kotest.matchers.shouldBe

class GameTest : UnitTest({

    "should correctly update game properties" {

        val underTest = Game(
            GameType.CASUAL,
            GameState.ENABLED,
            "New Forex game",
            "New Forex game description",
            "123e4567-e89b-12d3-a456-************".toUUID()
        )

        underTest.updateProperties(
            GameState.DISABLED,
            "Updated Forex game",
            "Updated Forex game description",
            "123e4567-e89b-12d3-a456-************".toUUID()
        )

        underTest.state shouldBe GameState.DISABLED
        underTest.name shouldBe "Updated Forex game"
        underTest.description shouldBe "Updated Forex game description"
        underTest.logoFileId shouldBe "123e4567-e89b-12d3-a456-************".toUUID()
    }
})
