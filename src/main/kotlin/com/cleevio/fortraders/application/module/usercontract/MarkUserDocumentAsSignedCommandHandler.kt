package com.cleevio.fortraders.application.module.usercontract

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.USER_CONTRACT
import com.cleevio.fortraders.application.module.file.FileService
import com.cleevio.fortraders.application.module.usercontract.command.MarkUserDocumentAsSignedCommand
import com.cleevio.fortraders.application.module.usercontract.constant.MARK_USER_DOCUMENT_AS_SIGNED
import com.cleevio.fortraders.domain.model.file.constant.FileType
import com.cleevio.fortraders.domain.model.usercontract.event.UserContractSignedEvent
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class MarkUserDocumentAsSignedCommandHandler(
    private val userContractFinderService: UserContractFinderService,
    private val fileService: FileService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, MarkUserDocumentAsSignedCommand> {
    override val command = MarkUserDocumentAsSignedCommand::class

    @Transactional
    @Lock(module = USER_CONTRACT, lockName = MARK_USER_DOCUMENT_AS_SIGNED)
    override fun invoke(@LockFieldParameter("submissionId") command: MarkUserDocumentAsSignedCommand) {
        val pdfFile = fileService.downloadFileAndUploadToStorage(
            fileUrl = command.pdfUrl,
            fileType = FileType.USER_CONTRACT
        )
        userContractFinderService.getBySubmissionId(command.submissionId).also {
            it.markAsSigned(pdfFileId = pdfFile.id)

            applicationEventPublisher.publishEvent(
                UserContractSignedEvent(
                    id = it.id,
                    orderId = it.orderId
                )
            )
        }
    }
}
