package com.cleevio.fortraders.application.module.tradingaccount.command

import com.cleevio.fortraders.application.common.command.Command
import com.cleevio.fortraders.application.common.validation.NotZero
import com.cleevio.fortraders.application.common.validation.NullOrNotBlank
import java.math.BigDecimal
import java.util.UUID

data class AdminUpdateTradingAccountBalanceCommand(
    val tradingAccountId: UUID,
    @field:NotZero val amount: BigDecimal,
    @field:NullOrNotBlank val comment: String?,
    val adjustStartingBalance: <PERSON>olean,
) : Command<Unit>
