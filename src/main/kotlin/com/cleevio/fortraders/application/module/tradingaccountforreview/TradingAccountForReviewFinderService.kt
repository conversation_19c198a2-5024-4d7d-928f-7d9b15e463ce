package com.cleevio.fortraders.application.module.tradingaccountforreview

import com.cleevio.fortraders.application.common.BaseFinderService
import com.cleevio.fortraders.domain.model.tradingaccountforreview.TradingAccountForReview
import com.cleevio.fortraders.domain.model.tradingaccountforreview.TradingAccountForReviewRepository
import com.cleevio.fortraders.domain.model.tradingaccountforreview.exception.TradingAccountForReviewNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class TradingAccountForReviewFinderService(
    tradingAccountForReviewRepository: TradingAccountForReviewRepository,
) : BaseFinderService<TradingAccountForReview, TradingAccountForReviewRepository>(
    repository = tradingAccountForReviewRepository,
    notFoundException = ::TradingAccountForReviewNotFoundException
) {
    @Transactional(readOnly = true)
    fun existsByOrderId(orderId: UUID): Boolean = repository.existsByOrderId(orderId)

    @Transactional(readOnly = true)
    fun findByOrderId(orderId: UUID): TradingAccountForReview? = repository.findByOrderId(orderId)
}
