package com.cleevio.fortraders.application.module.payout

import com.cleevio.fortraders.application.common.query.QueryHandler
import com.cleevio.fortraders.application.module.payout.port.output.AdminSearchPayouts
import com.cleevio.fortraders.application.module.payout.query.AdminSearchPayoutsQuery
import org.springframework.data.domain.Page
import org.springframework.stereotype.Service

@Service
class AdminSearchPayoutsQueryHandler(
    private val adminSearchPayouts: AdminSearchPayouts,
) : QueryHandler<Page<AdminSearchPayoutsQuery.ResultWithTotalPayouts>, AdminSearchPayoutsQuery> {
    override val query = AdminSearchPayoutsQuery::class

    override fun handle(query: AdminSearchPayoutsQuery): Page<AdminSearchPayoutsQuery.ResultWithTotalPayouts> =
        adminSearchPayouts(
            pageable = query.pageable,
            states = query.filter.states,
            createdAtFrom = query.filter.createdAtFrom,
            createdAtTo = query.filter.createdAtTo,
            labels = query.filter.labels,
            fulltext = query.filter.fulltext,
            challengeTypes = query.filter.challengeTypes
        )
}
