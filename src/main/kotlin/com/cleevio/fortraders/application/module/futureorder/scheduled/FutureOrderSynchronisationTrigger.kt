package com.cleevio.fortraders.application.module.futureorder.scheduled

import com.cleevio.fortraders.application.common.constant.FUTURE_ORDER
import com.cleevio.fortraders.application.module.futureorder.FutureOrderSynchronisationService
import com.cleevio.fortraders.application.module.futureorder.constant.SYNCHRONISE_FUTURE_ORDERS
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class FutureOrderSynchronisationTrigger(
    private val futureOrderSynchronisationService: FutureOrderSynchronisationService,
) {
    @TryLock(module = FUTURE_ORDER, lockName = SYNCHRONISE_FUTURE_ORDERS)
    @Scheduled(cron = "\${fortraders.future-order.synchronisation.cron}")
    fun trigger() {
        futureOrderSynchronisationService.synchroniseFutureOrdersForActiveAccounts()
    }
}
