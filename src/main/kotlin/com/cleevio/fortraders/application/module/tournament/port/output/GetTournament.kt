package com.cleevio.fortraders.application.module.tournament.port.output

import com.cleevio.fortraders.application.module.file.port.output.FileToUrlMapper
import com.cleevio.fortraders.application.module.tournament.query.GetTournamentQuery
import com.cleevio.fortraders.domain.model.tournament.PrizePoolDistribution
import java.util.UUID

interface GetTournament {
    operator fun invoke(
        tournamentId: UUID,
        prizePoolDistribution: PrizePoolDistribution,
        fileToUrlMapper: FileToUrlMapper,
        userId: UUID?,
    ): GetTournamentQuery.Result
}
