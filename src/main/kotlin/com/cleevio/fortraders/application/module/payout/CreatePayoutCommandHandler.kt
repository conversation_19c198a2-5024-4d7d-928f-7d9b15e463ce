package com.cleevio.fortraders.application.module.payout

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.PAYOUT
import com.cleevio.fortraders.application.common.util.ifTrue
import com.cleevio.fortraders.application.common.util.roundPriceUp
import com.cleevio.fortraders.application.module.challengestep.ChallengeStepFinderService
import com.cleevio.fortraders.application.module.order.OrderFinderService
import com.cleevio.fortraders.application.module.payout.command.CreatePayoutCommand
import com.cleevio.fortraders.application.module.payout.constant.CREATE_PAYOUT
import com.cleevio.fortraders.application.module.tradingaccount.TradingAccountFinderService
import com.cleevio.fortraders.application.module.tradingaccount.TradingAccountService
import com.cleevio.fortraders.application.module.tradingaccounthistory.TradingAccountHistoryFinderService
import com.cleevio.fortraders.domain.model.payout.PayoutCreateService
import com.cleevio.fortraders.domain.model.payout.exception.PayoutRequestAlreadyExistsException
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.Instant

@Service
class CreatePayoutCommandHandler(
    private val orderFinderService: OrderFinderService,
    private val tradingAccountFinderService: TradingAccountFinderService,
    private val challengeStepFinderService: ChallengeStepFinderService,
    private val payoutFinderService: PayoutFinderService,
    private val tradingAccountHistoryFinderService: TradingAccountHistoryFinderService,
    private val tradingAccountService: TradingAccountService,
    private val payoutCreateService: PayoutCreateService,
) : CommandHandler<Unit, CreatePayoutCommand> {
    override val command = CreatePayoutCommand::class

    @Lock(module = PAYOUT, lockName = CREATE_PAYOUT)
    override fun invoke(@LockFieldParameter("tradingAccountId") command: CreatePayoutCommand) {
        val amount = command.amount.roundPriceUp()
        val order = orderFinderService.getByIdAndUserId(id = command.orderId, userId = command.userId).also {
            it.verifyIsCompleted()
        }
        val tradingAccount = tradingAccountFinderService.getByIdAndOrderId(
            id = command.tradingAccountId,
            orderId = order.id
        ).also {
            it.verifyIsNotBreached()
            it.checkHasEnoughProfit(amount)
            it.checkIsNotPaused()
        }
        challengeStepFinderService.getById(tradingAccount.challengeStepId)
            .verifyIsFundedStep()

        payoutFinderService.existsRequestedByTradingAccountId(tradingAccount.id).ifTrue {
            throw PayoutRequestAlreadyExistsException()
        }

        tradingAccountService.verifyHasNoOpenTrades(tradingAccount.id)

        val lastApprovedPayout = payoutFinderService.findLastApprovedByTradingAccountId(tradingAccount.id)
        val latestTradingAccountHistory = tradingAccountHistoryFinderService.findLatestByTradingAccountId(
            tradingAccountId = tradingAccount.id
        )
        val lastApprovedPayoutCreatedAt = lastApprovedPayout?.createdAt ?: Instant.EPOCH
        val bestTradingDayProfitValueWithToday = maxOf(
            tradingAccountHistoryFinderService.findBestDailyProfitByTradingAccountIdFrom(
                tradingAccountId = tradingAccount.id,
                from = lastApprovedPayoutCreatedAt
            ) ?: BigDecimal.ZERO,
            tradingAccount.calculateDailyProfit(latestTradingAccountHistory?.balance)
        )

        payoutCreateService.create(
            tradingAccountId = tradingAccount.id,
            amount = amount,
            profitSplit = requireNotNull(tradingAccount.profitSplit),
            minimalPayoutLimit = requireNotNull(tradingAccount.minimumPayoutLimit),
            maximumPayoutLimit = tradingAccount.maximumPayoutLimit,
            nextPayoutAvailableAt = requireNotNull(tradingAccount.nextPayoutAvailableAt),
            profitableTradingDays = tradingAccountHistoryFinderService.findLatestByTradingAccountId(
                tradingAccount.id
            )?.profitableTradingDays ?: 0,
            minProfitableTradingDays = tradingAccount.minProfitableTradingDays,
            profitSinceLastPayout = tradingAccount.currentBalance - tradingAccount.startingBalanceThisPayoutCycle,
            consistencyTarget = tradingAccount.consistencyTarget,
            bestTradingDayProfitValueWithToday = bestTradingDayProfitValueWithToday
        )
    }
}
