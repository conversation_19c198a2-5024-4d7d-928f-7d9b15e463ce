package com.cleevio.fortraders.application.module.instrument.port.output

import com.cleevio.fortraders.application.module.instrument.query.AdminSearchInstrumentsQuery
import com.cleevio.fortraders.domain.model.instrument.constant.InstrumentType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface AdminSearchInstruments {
    operator fun invoke(pageable: Pageable, types: Set<InstrumentType>?): Page<AdminSearchInstrumentsQuery.Result>
}
