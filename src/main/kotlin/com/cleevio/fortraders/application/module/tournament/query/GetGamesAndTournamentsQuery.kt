package com.cleevio.fortraders.application.module.tournament.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.game.constant.GameType
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentPhase
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentReward
import com.cleevio.fortraders.domain.model.tournamentuser.constant.TournamentUserState
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

class GetGamesAndTournamentsQuery(val userId: UUID?) : Query<GetGamesAndTournamentsQuery.Result> {

    @Schema(name = "GetGamesAndTournamentsResult")
    data class Result(
        val tournaments: List<TournamentDetails>,
        val games: GamesDetails,
    )

    @Schema(name = "GetGamesAndTournamentsResultTournamentDetails")
    data class TournamentDetails(
        val id: UUID,
        val name: String,
        val reward: TournamentReward,
        val initialPrizePool: BigDecimal?,
        val entryFee: BigDecimal,
        val startsAt: Instant,
        val endsAt: Instant,
        val leverageEnabled: Boolean,
        val coverUrl: String,
        val phase: TournamentPhase,
        val game: TournamentGameDetails,
        val buyInsLimitExceeded: Boolean,
        val user: UserDetail?,
    )

    @Schema(name = "GetGamesAndTournamentsResultTournamentGameDetails")
    data class TournamentGameDetails(
        val id: UUID,
        val name: String,
        val logoUrl: String,
    )

    @Schema(name = "GetGamesAndTournamentsResultGamesDetails")
    data class GamesDetails(
        val casual: List<GameDetails>,
        val professional: List<GameDetails>,
    )

    @Schema(name = "GetGamesAndTournamentsResultGameDetails")
    data class GameDetails(
        val id: UUID,
        val type: GameType,
        val name: String,
        val description: String,
        val logoUrl: String,
        val players: Int,
    )

    @Schema(name = "GetGamesAndTournamentsResultUserDetail")
    data class UserDetail(
        val profit: BigDecimal?,
        val state: TournamentUserState,
    )
}
