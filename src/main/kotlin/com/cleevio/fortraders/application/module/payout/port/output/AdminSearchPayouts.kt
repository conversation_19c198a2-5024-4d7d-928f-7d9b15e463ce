package com.cleevio.fortraders.application.module.payout.port.output

import com.cleevio.fortraders.application.module.payout.query.AdminSearchPayoutsQuery
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.payout.constant.PayoutState
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.time.Instant
import java.util.UUID

interface AdminSearchPayouts {
    operator fun invoke(
        pageable: Pageable,
        states: Set<PayoutState>?,
        createdAtFrom: Instant?,
        createdAtTo: Instant?,
        labels: Set<UUID>?,
        fulltext: String?,
        challengeTypes: Set<ChallengeType>?,
    ): Page<AdminSearchPayoutsQuery.ResultWithTotalPayouts>
}
