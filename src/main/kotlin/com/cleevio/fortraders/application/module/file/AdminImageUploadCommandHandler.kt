package com.cleevio.fortraders.application.module.file

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.command.IdResult
import com.cleevio.fortraders.application.module.file.command.AdminImageUploadCommand
import org.springframework.stereotype.Service

@Service
class AdminImageUploadCommandHandler(
    private val fileService: FileService,
) : CommandHandler<IdResult, AdminImageUploadCommand> {
    override val command = AdminImageUploadCommand::class

    override fun invoke(command: AdminImageUploadCommand): IdResult =
        IdResult(id = fileService.upload(command.file, command.fileType).id)
}
