package com.cleevio.fortraders.application.module.transaction.command

import com.cleevio.fortraders.application.common.command.Command
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import jakarta.validation.constraints.NotBlank

data class ProcessPaymentGatewaySubscriptionResultCommand(
    @field:NotBlank val reference: String,
    @field:NotBlank val result: String,
    val paymentGatewayProvider: PaymentGatewayProvider,
) : Command<Unit>
