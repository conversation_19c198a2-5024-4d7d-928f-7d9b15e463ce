package com.cleevio.fortraders.application.module.tradingaccount.event.listener

import com.cleevio.fortraders.application.module.tradingaccount.TradingAccountService
import com.cleevio.fortraders.domain.model.tradingaccountpause.constant.TradingAccountPauseType
import com.cleevio.fortraders.domain.model.tradingaccountpause.event.TradingAccountPauseCreatedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class TradingAccountPauseTradingAccountEventListener(
    private val tradingAccountService: TradingAccountService,
) {
    @EventListener
    fun handleTradingAccountPauseCreatedEvent(event: TradingAccountPauseCreatedEvent) {
        when (event.pauseType) {
            TradingAccountPauseType.DAILY_PROFIT_CAP -> tradingAccountService.dailyProfitCapPauseAccount(event.tradingAccountId)
            TradingAccountPauseType.DAILY_PAUSE -> tradingAccountService.dailyPauseAccount(
                id = event.tradingAccountId,
                accountEquity = event.accountEquity,
                accountBalance = event.accountBalance
            )
        }
    }
}
