package com.cleevio.fortraders.application.module.marketing.port.output

import java.math.BigDecimal
import java.time.Instant

interface CreateOrderInMarketingPlatform {
    operator fun invoke(
        orderId: String,
        email: String,
        shop: String,
        amount: BigDecimal,
        tax: BigDecimal,
        shipping: BigDecimal,
        city: String?,
        county: String?,
        country: String?,
        timestamp: Instant,
        status: String?,
        items: List<OrderItem>,
    )

    data class OrderItem(
        val code: String,
        val title: String,
        val category: String,
        val price: BigDecimal,
        val amount: Int,
    )
}
