package com.cleevio.fortraders.application.module.order

import com.cleevio.fortraders.application.common.constant.ORDER
import com.cleevio.fortraders.application.common.util.ifFalse
import com.cleevio.fortraders.application.module.challengeplan.ChallengePlanFinderService
import com.cleevio.fortraders.application.module.challengestep.ChallengeStepFinderService
import com.cleevio.fortraders.application.module.order.constant.UPDATE_ORDER
import com.cleevio.fortraders.application.module.tradingaccount.TradingAccountFinderService
import com.cleevio.fortraders.application.module.transaction.TransactionService
import com.cleevio.fortraders.domain.model.discountcode.DiscountCodeDeleteService
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountUsageType
import com.cleevio.fortraders.domain.model.discountcodeuser.DiscountCodeUserDeleteService
import com.cleevio.fortraders.domain.model.order.event.OrderCancellationReason
import com.cleevio.fortraders.domain.model.order.event.OrderCancelledEvent
import com.cleevio.fortraders.domain.model.order.event.OrderFirstPaymentCompletedEvent
import com.cleevio.fortraders.domain.model.order.event.OrderPendingCancellationEvent
import com.cleevio.fortraders.domain.model.order.event.OrderRecurringPaymentCompletedEvent
import com.cleevio.fortraders.domain.model.order.event.OrderRecurringPaymentFirstAttemptFailedEvent
import com.cleevio.fortraders.domain.model.order.event.OrderRecurringPaymentSubsequentAttemptFailedEvent
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class OrderService(
    private val orderFinderService: OrderFinderService,
    private val tradingAccountFinderService: TradingAccountFinderService,
    private val challengeStepFinderService: ChallengeStepFinderService,
    private val challengePlanFinderService: ChallengePlanFinderService,
    private val transactionService: TransactionService,
    private val discountCodeDeleteService: DiscountCodeDeleteService,
    private val discountCodeUserDeleteService: DiscountCodeUserDeleteService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    @Transactional
    @Lock(module = ORDER, lockName = UPDATE_ORDER)
    fun createTransactionAndMarkOrderAsPaid(@LockArgumentParameter orderId: UUID) {
        val order = orderFinderService.getById(orderId)

        transactionService.createChallengeOrderTransaction(
            userId = order.userId,
            orderId = order.id,
            amount = order.calculateFinalPrice(),
            amountUsedFromWallet = order.amountUsedFromWallet
        )
        val isOrderCompletedForTheFirstTime = order.markAsCompleted()

        if (isOrderCompletedForTheFirstTime) {
            applicationEventPublisher.publishEvent(
                OrderFirstPaymentCompletedEvent(
                    orderId = order.id,
                    userId = order.userId,
                    affiliateId = order.affiliateId
                )
            )
        } else {
            applicationEventPublisher.publishEvent(
                OrderRecurringPaymentCompletedEvent(
                    orderId = order.id,
                    userId = order.userId
                )
            )
        }
    }

    @Transactional
    @Lock(module = ORDER, lockName = UPDATE_ORDER)
    fun cancelOrderIfSubscription(@LockArgumentParameter orderId: UUID, reason: OrderCancellationReason) {
        val order = orderFinderService.getById(orderId).also {
            if (it.isCancelled()) return
        }
        challengePlanFinderService.getById(order.challengePlanId).let {
            if (!it.isSubscription) return
        }

        order.markAsCancelled()

        applicationEventPublisher.publishEvent(
            OrderCancelledEvent(
                orderId = order.id,
                reason = reason
            )
        )
    }

    @Transactional
    @Lock(module = ORDER, lockName = UPDATE_ORDER)
    fun markPendingCancellation(@LockArgumentParameter orderId: UUID) {
        val order = orderFinderService.getById(orderId).also {
            if (it.isPendingCancellation()) return
        }

        order.markAsPendingCancellation()

        applicationEventPublisher.publishEvent(
            OrderPendingCancellationEvent(
                orderId = order.id
            )
        )
    }

    @Transactional
    @Lock(module = ORDER, lockName = UPDATE_ORDER)
    fun markPaymentAttemptFailedIfSubscription(@LockArgumentParameter orderId: UUID) {
        val order = orderFinderService.getById(orderId)
        challengePlanFinderService.getById(order.challengePlanId).let {
            if (!it.isSubscription) return
        }

        if (order.isRecurringPaymentAttemptFailed()) {
            applicationEventPublisher.publishEvent(
                OrderRecurringPaymentSubsequentAttemptFailedEvent(
                    orderId = order.id
                )
            )
            return
        }

        order.markAsPaymentAttemptFailed()

        applicationEventPublisher.publishEvent(
            OrderRecurringPaymentFirstAttemptFailedEvent(
                orderId = order.id
            )
        )
    }

    @Transactional
    @Lock(module = ORDER, lockName = UPDATE_ORDER)
    fun setDiscountCodeAfterBreach(
        @LockArgumentParameter orderId: UUID,
        discountCodeId: UUID,
        discountCodeUsageType: DiscountUsageType,
    ) {
        if (discountCodeUsageType != DiscountUsageType.BREACH) return

        orderFinderService.getById(orderId).setBreachDiscountCode(discountCodeId)
    }

    @Transactional
    @Lock(module = ORDER, lockName = UPDATE_ORDER)
    fun changePlatform(@LockArgumentParameter orderId: UUID, newPlatform: PlatformType) {
        orderFinderService.getById(orderId).changePlatform(newPlatform)
    }

    @Transactional
    @Lock(module = ORDER, lockName = UPDATE_ORDER)
    fun deleteOrderBreachDiscountCode(@LockArgumentParameter orderId: UUID) {
        val order = orderFinderService.getById(orderId)
        val discountCodeId = order.breachDiscountCodeId ?: return

        order.deleteBreachDiscountCode()
        orderFinderService.existsByDiscountCodeId(discountCodeId).ifFalse {
            // only delete discount code if it is not used already
            discountCodeUserDeleteService.delete(discountCodeId = discountCodeId)
            discountCodeDeleteService.delete(discountCodeId)
        }
    }

    @Transactional
    @Lock(module = ORDER, lockName = UPDATE_ORDER)
    fun markAsCompletedAfterFundedTradingAccountCreated(@LockArgumentParameter orderId: UUID, tradingAccountId: UUID) {
        val order = orderFinderService.getById(orderId).also {
            if (!it.state.isCancellationState()) return
        }
        val tradingAccount = tradingAccountFinderService.getById(tradingAccountId)
        challengeStepFinderService.getById(tradingAccount.challengeStepId).let {
            if (it.isEvaluationStep()) return
        }

        order.markAsCompletedWhenUpgradedToFunded()
    }
}
