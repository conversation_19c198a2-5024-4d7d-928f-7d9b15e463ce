package com.cleevio.fortraders.application.module.tournamentinstrument

import com.cleevio.fortraders.application.common.BaseFinderService
import com.cleevio.fortraders.domain.model.tournamentinstrument.TournamentInstrument
import com.cleevio.fortraders.domain.model.tournamentinstrument.TournamentInstrumentRepository
import com.cleevio.fortraders.domain.model.tournamentinstrument.exception.TournamentInstrumentNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class TournamentInstrumentFinderService(
    tournamentInstrumentRepository: TournamentInstrumentRepository,
) : BaseFinderService<TournamentInstrument, TournamentInstrumentRepository>(
    repository = tournamentInstrumentRepository,
    notFoundException = ::TournamentInstrumentNotFoundException
) {
    @Transactional(readOnly = true)
    fun findByTournamentIdsAndInstrumentNonDeleted(tournamentIds: Set<UUID>): List<TournamentInstrument> =
        repository.findByTournamentIdInAndInstrumentDeletedAtIsNull(tournamentIds)

    @Transactional(readOnly = true)
    fun findInstrumentIdsByTournamentIdAndInstrumentIdIn(tournamentId: UUID, instrumentIds: Set<UUID>): Set<UUID> =
        repository.findInstrumentIdsByTournamentIdAndInstrumentIdIn(tournamentId = tournamentId, instrumentIds = instrumentIds)

    @Transactional(readOnly = true)
    fun existsByTournamentIdAndInstrumentId(tournamentId: UUID, instrumentId: UUID): Boolean =
        repository.existsByTournamentIdAndInstrumentId(tournamentId = tournamentId, instrumentId = instrumentId)
}
