package com.cleevio.fortraders.application.module.challengestepsetting.command

import com.cleevio.fortraders.application.common.command.Command
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.ChallengeStepSettingType
import jakarta.validation.constraints.Min
import java.util.UUID

data class AdminPatchChallengeStepSettingCommand(
    val challengePlanId: UUID,
    @field:Min(1) val challengeStepNumber: Int,
    val challengeStepSettingType: ChallengeStepSettingType,
    val useMovementsFromOrder: Boolean?,
    val isVisibleInConfigurator: Boolean?,
) : Command<Unit>
