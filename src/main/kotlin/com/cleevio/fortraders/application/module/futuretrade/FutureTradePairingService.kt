package com.cleevio.fortraders.application.module.futuretrade

import com.cleevio.fortraders.application.common.constant.FUTURE_TRADE
import com.cleevio.fortraders.application.common.util.monetaryDivide
import com.cleevio.fortraders.application.module.futureorder.FutureOrderFinderService
import com.cleevio.fortraders.application.module.futuretrade.constant.PAIR_FUTURE_TRADES
import com.cleevio.fortraders.domain.model.futureorder.FutureOrder
import com.cleevio.fortraders.domain.model.futuretrade.FutureTradeCreateService
import com.cleevio.fortraders.domain.model.futuretradeorderpair.FutureTradeOrderPairCreateService
import com.cleevio.library.lockinghandler.service.Lock
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
class FutureTradePairingService(
    private val futureOrderFinderService: FutureOrderFinderService,
    private val futureTradeFinderService: FutureTradeFinderService,
    private val futureTradeOrderPairCreateService: FutureTradeOrderPairCreateService,
    private val futureTradeCreateService: FutureTradeCreateService,
) {
    @Lock(module = FUTURE_TRADE, lockName = PAIR_FUTURE_TRADES)
    fun pairFutureTradesForTradingAccounts(tradingAccountIds: Set<UUID>) {
        tradingAccountIds.forEach { pairFutureOrders(it) }
    }

    @Lock(module = FUTURE_TRADE, lockName = PAIR_FUTURE_TRADES)
    fun pairFutureTradesForTradingAccount(tradingAccountId: UUID) {
        pairFutureOrders(tradingAccountId)
    }

    private fun pairFutureOrders(tradingAccountId: UUID) {
        val futureOrders = futureOrderFinderService.findAllByTradingAccountIdOrderedByTransactionTime(tradingAccountId)
        val existingFutureTradeIds = futureTradeFinderService.findTradeIdsByTradingAccountId(tradingAccountId)

        futureOrders.groupBy { it.symbol }.forEach { (_, sortedOrders) ->
            val openingOrdersDeque = ArrayDeque<OpeningOrderInfo>()

            sortedOrders.forEach { order ->
                if (order.profit != null) {
                    // orders with profit are closing ones
                    val pairedOrders = mutableListOf<PartialFutureOrderInfo>()
                    var remainingClosingOrderQuantity = order.quantity
                    var commissionSum = BigDecimal.ZERO

                    while (remainingClosingOrderQuantity > 0 && openingOrdersDeque.isNotEmpty()) {
                        val openingOrderInfo = openingOrdersDeque.first()

                        val pairedQuantity = minOf(remainingClosingOrderQuantity, openingOrderInfo.remainingQuantity)
                        commissionSum += openingOrderInfo.commissionPerQuantity * pairedQuantity.toBigDecimal()

                        pairedOrders.add(
                            PartialFutureOrderInfo(
                                order = openingOrderInfo.order,
                                usedQuantity = pairedQuantity
                            )
                        )

                        openingOrderInfo.remainingQuantity -= pairedQuantity
                        remainingClosingOrderQuantity -= pairedQuantity

                        if (openingOrderInfo.remainingQuantity == 0) {
                            openingOrdersDeque.removeFirst()
                        }
                    }
                    val tradeQuantity = order.quantity - remainingClosingOrderQuantity
                    pairedOrders.add(
                        PartialFutureOrderInfo(
                            order = order,
                            usedQuantity = tradeQuantity
                        )
                    )

                    if (remainingClosingOrderQuantity > 0) {
                        // handle excess quantity that cannot be paired
                        val commissionPerQuantity = order.commission.monetaryDivide(order.quantity)
                        commissionSum += commissionPerQuantity * tradeQuantity.toBigDecimal()
                        openingOrdersDeque.add(
                            OpeningOrderInfo(
                                order = order,
                                commissionPerQuantity = commissionPerQuantity,
                                remainingQuantity = remainingClosingOrderQuantity
                            )
                        )
                    } else {
                        commissionSum += order.commission
                    }

                    val tradeId = pairedOrders.joinToString("-") { it.order.orderId }
                    if (tradeId !in existingFutureTradeIds) {
                        val firstOrder = pairedOrders.first().order
                        val futureTrade = futureTradeCreateService.create(
                            tradingAccountId = tradingAccountId,
                            tradeId = tradeId,
                            profit = order.profit,
                            symbol = order.symbol,
                            type = order.type,
                            quantity = tradeQuantity,
                            side = order.side.toOpposite(),
                            commission = commissionSum,
                            openTime = firstOrder.transactionTime,
                            openPrice = firstOrder.price,
                            closeTime = order.transactionTime,
                            closePrice = order.price
                        )
                        pairedOrders.forEachIndexed { index, it ->
                            futureTradeOrderPairCreateService.create(
                                futureTradeId = futureTrade.id,
                                futureOrderId = it.order.id,
                                usedQuantity = it.usedQuantity,
                                usedCommission = it.order.calculateCommissionPerQuantity() * it.usedQuantity.toBigDecimal(),
                                isClosing = index == (pairedOrders.size - 1)
                            )
                        }
                    }
                } else {
                    // Opening order - add to the deque
                    val commissionPerQuantity = order.calculateCommissionPerQuantity()
                    openingOrdersDeque.add(
                        OpeningOrderInfo(
                            order = order,
                            commissionPerQuantity = commissionPerQuantity,
                            remainingQuantity = order.quantity
                        )
                    )
                }
            }
        }
    }
}

private data class OpeningOrderInfo(
    val order: FutureOrder,
    val commissionPerQuantity: BigDecimal,
    var remainingQuantity: Int,
)

private data class PartialFutureOrderInfo(
    val order: FutureOrder,
    val usedQuantity: Int,
)
