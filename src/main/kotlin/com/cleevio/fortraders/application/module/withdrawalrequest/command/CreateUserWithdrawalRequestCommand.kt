package com.cleevio.fortraders.application.module.withdrawalrequest.command

import com.cleevio.fortraders.application.common.command.Command
import com.cleevio.fortraders.application.common.validation.BIC
import com.cleevio.fortraders.application.common.validation.Erc20Wallet
import com.cleevio.fortraders.application.common.validation.IBAN
import com.cleevio.fortraders.application.common.validation.NullOrNotBlank
import com.cleevio.fortraders.domain.model.withdrawalrequest.constant.WithdrawalRequestType
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Min
import java.math.BigDecimal
import java.util.UUID

data class CreateUserWithdrawalRequestCommand(
    val userId: UUID,
    @field:Min(1) val amount: BigDecimal,
    val type: WithdrawalRequestType,
    @field:NullOrNotBlank val accountHolderFullName: String?,
    @field:NullOrNotBlank val bankName: String?,
    @field:NullOrNotBlank val bankAddress: String?,
    @field:IBAN val iban: String?,
    @field:BIC val swift: String?,
    @field:Erc20Wallet val erc20Address: String?,
    @field:Email val email: String?,
) : Command<Unit>
