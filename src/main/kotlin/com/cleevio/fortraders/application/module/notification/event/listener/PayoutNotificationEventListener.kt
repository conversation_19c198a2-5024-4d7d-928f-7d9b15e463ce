package com.cleevio.fortraders.application.module.notification.event.listener

import com.cleevio.fortraders.application.module.notification.PayoutNotificationProcessingService
import com.cleevio.fortraders.domain.model.payout.event.PayoutApprovedEvent
import com.cleevio.fortraders.domain.model.payout.event.PayoutCreatedEvent
import com.cleevio.fortraders.domain.model.payout.event.PayoutDeclinedEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    name = ["fortraders.listeners.payout-notification.enabled"],
    havingValue = "true",
    matchIfMissing = true
)
class PayoutNotificationEventListener(
    private val payoutNotificationProcessingService: PayoutNotificationProcessingService,
) {
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    fun handleAsyncPayoutCreatedEvent(event: PayoutCreatedEvent) {
        payoutNotificationProcessingService.processPayoutCreated(event.payoutId)
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    fun handleAsyncPayoutApprovedEvent(event: PayoutApprovedEvent) {
        payoutNotificationProcessingService.processPayoutApproved(event.payoutId)
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    fun handleAsyncPayoutDeclinedEvent(event: PayoutDeclinedEvent) {
        payoutNotificationProcessingService.processPayoutDeclined(event.payoutId)
    }
}
