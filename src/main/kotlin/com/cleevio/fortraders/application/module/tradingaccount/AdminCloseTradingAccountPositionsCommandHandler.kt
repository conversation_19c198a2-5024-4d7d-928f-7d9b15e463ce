package com.cleevio.fortraders.application.module.tradingaccount

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.TRADING_ACCOUNT
import com.cleevio.fortraders.application.module.tradingaccount.command.AdminCloseTradingAccountPositionsCommand
import com.cleevio.fortraders.application.module.tradingaccount.constant.UPDATE_TRADING_ACCOUNT
import com.cleevio.fortraders.application.module.tradingaccount.port.output.CloseAllTradingAccountOpenTrades
import com.cleevio.fortraders.application.module.tradingaccount.port.output.forPlatform
import com.cleevio.fortraders.domain.model.tradingaccount.exception.TradingAccountPositionClosingFailedException
import com.cleevio.fortraders.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service

@Service
class AdminCloseTradingAccountPositionsCommandHandler(
    private val tradingAccountFinderService: TradingAccountFinderService,
    private val closeAllTradingAccountOpenTradesFunctions: List<CloseAllTradingAccountOpenTrades>,
) : CommandHandler<Unit, AdminCloseTradingAccountPositionsCommand> {
    override val command = AdminCloseTradingAccountPositionsCommand::class
    private val logger = logger()

    @Lock(module = TRADING_ACCOUNT, lockName = UPDATE_TRADING_ACCOUNT)
    override fun invoke(@LockFieldParameter("tradingAccountId") command: AdminCloseTradingAccountPositionsCommand) {
        val tradingAccountId = tradingAccountFinderService.getById(command.tradingAccountId)
        closeAllTradingAccountOpenTradesFunctions.forPlatform(tradingAccountId.platform)(tradingAccountId.accountId).onFailure {
            logger.error("Closing open trades for account ${tradingAccountId.accountId} failed", it)
            throw TradingAccountPositionClosingFailedException(it.message)
        }
    }
}
