package com.cleevio.fortraders.application.module.orderblacklist.query

import com.cleevio.fortraders.application.common.query.Query
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.util.UUID

data class AdminSearchOrderBlacklistQuery(
    val pageable: Pageable,
    val filter: Filter,
) : Query<Page<AdminSearchOrderBlacklistQuery.Result>> {

    data class Filter(
        val userEmails: Set<String>? = null,
        val fulltext: String? = null,
    )

    @Schema(name = "AdminSearchOrderBlacklistResult")
    data class Result(
        val id: UUID,
        val email: String,
        val note: String?,
    )
}
