package com.cleevio.fortraders.application.module.tradingaccount

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.TRADING_ACCOUNT
import com.cleevio.fortraders.application.module.tradingaccount.command.AdminMigrateTradingAccountPlatformCommand
import com.cleevio.fortraders.application.module.tradingaccount.constant.UPDATE_TRADING_ACCOUNT
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminMigrateTradingAccountPlatformCommandHandler(
    private val tradingAccountService: TradingAccountService,
) : CommandHandler<Unit, AdminMigrateTradingAccountPlatformCommand> {
    override val command = AdminMigrateTradingAccountPlatformCommand::class

    @Transactional
    @Lock(module = TRADING_ACCOUNT, lockName = UPDATE_TRADING_ACCOUNT)
    override fun invoke(@LockFieldParameter("tradingAccountId") command: AdminMigrateTradingAccountPlatformCommand) {
        tradingAccountService.migrateAccountToNewPlatform(
            id = command.tradingAccountId,
            newPlatform = command.newPlatform
        )
    }
}
