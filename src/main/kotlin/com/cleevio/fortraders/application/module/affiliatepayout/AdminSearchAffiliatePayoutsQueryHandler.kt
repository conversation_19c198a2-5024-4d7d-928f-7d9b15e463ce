package com.cleevio.fortraders.application.module.affiliatepayout

import com.cleevio.fortraders.application.common.query.QueryHandler
import com.cleevio.fortraders.application.module.affiliatepayout.port.output.AdminSearchAffiliatePayouts
import com.cleevio.fortraders.application.module.affiliatepayout.query.AdminSearchAffiliatePayoutsQuery
import org.springframework.data.domain.Page
import org.springframework.stereotype.Service

@Service
class AdminSearchAffiliatePayoutsQueryHandler(
    private val adminSearchAffiliatePayouts: AdminSearchAffiliatePayouts,
) : QueryHandler<Page<AdminSearchAffiliatePayoutsQuery.Result>, AdminSearchAffiliatePayoutsQuery> {
    override val query = AdminSearchAffiliatePayoutsQuery::class

    override fun handle(query: AdminSearchAffiliatePayoutsQuery): Page<AdminSearchAffiliatePayoutsQuery.Result> =
        adminSearchAffiliatePayouts(
            pageable = query.pageable,
            createdAtFrom = query.filter.createdAtFrom,
            createdAtTo = query.filter.createdAtTo,
            states = query.filter.states,
            fulltext = query.filter.fulltext
        )
}
