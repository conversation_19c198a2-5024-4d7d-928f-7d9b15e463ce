package com.cleevio.fortraders.application.module.affiliate

import com.cleevio.fortraders.application.common.constant.AFFILIATE
import com.cleevio.fortraders.application.common.util.generateRandomCode
import com.cleevio.fortraders.application.common.util.ifTrue
import com.cleevio.fortraders.application.module.affiliate.constant.CREATE_OR_UPDATE_AFFILIATE
import com.cleevio.fortraders.domain.model.affiliate.Affiliate
import com.cleevio.fortraders.domain.model.affiliate.AffiliateCreateService
import com.cleevio.fortraders.domain.model.affiliate.exception.AffiliateCannotBeAppliedToYourselfException
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import com.cleevio.library.lockinghandler.service.LockService
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant
import java.util.UUID

@Service
class AffiliateService(
    @Value("\${fortraders.affiliate-payouts.duration-between-payouts}")
    private val durationBetweenAffiliatePayouts: Duration,

    @Value("\${fortraders.affiliate.after-registration-creation.discount-percentage}")
    private val afterRegistrationDiscountPercentage: Int,

    @Value("\${fortraders.affiliate.after-registration-creation.commission-percentage}")
    private val afterRegistrationCommissionPercentage: Int,

    private val lockService: LockService,
    private val affiliateFinderService: AffiliateFinderService,
    private val affiliateCreateService: AffiliateCreateService,
) {
    @Transactional
    @Lock(module = AFFILIATE, lockName = CREATE_OR_UPDATE_AFFILIATE)
    fun addToCommissionBalance(@LockArgumentParameter userId: UUID, amount: BigDecimal) {
        affiliateFinderService.getByUserId(userId).addToCommissionBalance(amount)
    }

    @Transactional
    @Lock(module = AFFILIATE, lockName = CREATE_OR_UPDATE_AFFILIATE)
    fun deductFromCommissionBalanceAfterApproval(
        @LockArgumentParameter userId: UUID,
        amount: BigDecimal,
        lastApprovedAffiliatePayoutCreatedAt: Instant,
    ) {
        affiliateFinderService.getByUserId(userId).deductFromCommissionAfterApprovalBalance(
            amount = amount,
            lastApprovedAffiliatePayoutCreatedAt = lastApprovedAffiliatePayoutCreatedAt,
            durationBetweenAffiliatePayouts = durationBetweenAffiliatePayouts
        )
    }

    @Transactional
    @Lock(module = AFFILIATE, lockName = CREATE_OR_UPDATE_AFFILIATE)
    fun makeUserAffiliate(@LockArgumentParameter userId: UUID) {
        affiliateFinderService.existsByUserId(userId).ifTrue { return }

        generateSequence { generateRandomCode(length = 10) }.forEach { couponCode ->
            lockService.obtainBlockingLock(
                module = AFFILIATE,
                lockName = CREATE_OR_UPDATE_AFFILIATE,
                couponCode
            ).use {
                if (!affiliateFinderService.existsByCouponCode(couponCode)) {
                    affiliateCreateService.create(
                        userId = userId,
                        couponCode = couponCode,
                        discountAmountPercentage = afterRegistrationDiscountPercentage,
                        commissionPercentage = afterRegistrationCommissionPercentage,
                        note = null
                    )
                    return@makeUserAffiliate
                }
            }
        }
    }

    fun getAndValidateAffiliate(code: String, usedByUserId: UUID): Affiliate = affiliateFinderService.getActiveByCode(code).also {
        if (it.userId == usedByUserId) throw AffiliateCannotBeAppliedToYourselfException()
    }
}
