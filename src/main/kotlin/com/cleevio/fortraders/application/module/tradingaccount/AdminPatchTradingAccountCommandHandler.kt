package com.cleevio.fortraders.application.module.tradingaccount

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.TRADING_ACCOUNT
import com.cleevio.fortraders.application.module.challengestep.ChallengeStepFinderService
import com.cleevio.fortraders.application.module.tradingaccount.command.AdminPatchTradingAccountCommand
import com.cleevio.fortraders.application.module.tradingaccount.constant.UPDATE_TRADING_ACCOUNT
import com.cleevio.fortraders.application.module.tradingaccount.port.output.UpdateTradingAccountGroup
import com.cleevio.fortraders.application.module.tradingaccount.port.output.forPlatformOrNull
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminPatchTradingAccountCommandHandler(
    private val challengeStepFinderService: ChallengeStepFinderService,
    private val tradingAccountFinderService: TradingAccountFinderService,
    private val updateTradingAccountGroupFunctions: List<UpdateTradingAccountGroup>,
) : CommandHandler<Unit, AdminPatchTradingAccountCommand> {
    override val command = AdminPatchTradingAccountCommand::class

    @Transactional
    @Lock(module = TRADING_ACCOUNT, lockName = UPDATE_TRADING_ACCOUNT)
    override fun invoke(@LockFieldParameter("tradingAccountId") command: AdminPatchTradingAccountCommand) {
        val tradingAccount = tradingAccountFinderService.getById(command.tradingAccountId)
        val challengeStep = challengeStepFinderService.getById(tradingAccount.challengeStepId)
        val previousTradingGroup = tradingAccount.tradingGroup

        tradingAccount.patchAdminSpecifiedProperties(
            isFundedChallengeStep = challengeStep.isFundedStep(),
            tradingGroup = command.tradingGroup,
            leverage = command.leverage,
            inactivityPeriodDays = command.inactivityPeriodDays,
            profitTarget = command.profitTarget,
            dailyDrawdown = command.dailyDrawdown,
            maxDrawdown = command.maxDrawdown,
            profitSplit = command.profitSplit,
            minProfitableTradingDays = command.minProfitableTradingDays,
            maxTradingDays = command.maxTradingDays,
            consistencyTarget = command.consistencyTarget,
            nextPayoutAvailableAt = command.nextPayoutAvailableAt,
            capTrailingDrawdownAfterPayout = command.capTrailingDrawdownAfterPayout,
            minimumPayoutLimit = command.minimumPayoutLimit,
            maximumPayoutLimit = command.maximumPayoutLimit
        )

        if (tradingAccount.tradingGroup != previousTradingGroup) {
            updateTradingAccountGroupFunctions.forPlatformOrNull(tradingAccount.platform)?.invoke(
                accountId = tradingAccount.accountId,
                tradingGroup = tradingAccount.tradingGroup
            )
        }
    }
}
