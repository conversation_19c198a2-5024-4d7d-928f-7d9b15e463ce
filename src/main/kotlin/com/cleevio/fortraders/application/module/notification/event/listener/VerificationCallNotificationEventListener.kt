package com.cleevio.fortraders.application.module.notification.event.listener

import com.cleevio.fortraders.application.module.notification.VerificationCallNotificationProcessingService
import com.cleevio.fortraders.domain.model.verificationcall.event.VerificationCallRequestedEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    name = ["fortraders.listeners.verification-call-notification.enabled"],
    havingValue = "true",
    matchIfMissing = true
)
class VerificationCallNotificationEventListener(
    private val verificationCallNotificationProcessingService: VerificationCallNotificationProcessingService,
) {
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    fun handleAsyncVerificationCallRequestedEvent(event: VerificationCallRequestedEvent) {
        verificationCallNotificationProcessingService.processVerificationCallNotification(
            verificationCallId = event.verificationCallId,
            userId = event.userId
        )
    }
}
