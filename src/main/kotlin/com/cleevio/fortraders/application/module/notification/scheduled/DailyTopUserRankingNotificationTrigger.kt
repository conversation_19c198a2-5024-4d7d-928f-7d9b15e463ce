package com.cleevio.fortraders.application.module.notification.scheduled

import com.cleevio.fortraders.application.common.constant.NOTIFICATION
import com.cleevio.fortraders.application.module.notification.TopUserRankingNotificationService
import com.cleevio.fortraders.application.module.notification.constant.TOP_USER_RANKING
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class DailyTopUserRankingNotificationTrigger(
    private val topUserRankingNotificationService: TopUserRankingNotificationService,
) {

    @TryLock(module = NOTIFICATION, lockName = TOP_USER_RANKING)
    @Scheduled(cron = "\${fortraders.notification.top-user-ranking.cron}")
    fun trigger() = topUserRankingNotificationService.notifyDailyTopUsers()
}
