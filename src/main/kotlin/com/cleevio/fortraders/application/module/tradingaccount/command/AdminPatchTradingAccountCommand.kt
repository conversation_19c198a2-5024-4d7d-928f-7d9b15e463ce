package com.cleevio.fortraders.application.module.tradingaccount.command

import com.cleevio.fortraders.application.common.command.Command
import com.cleevio.fortraders.application.common.validation.NullOrNotBlank
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.PositiveOrZero
import java.math.BigDecimal
import java.time.Instant
import java.util.Optional
import java.util.UUID

data class AdminPatchTradingAccountCommand(
    val tradingAccountId: UUID,
    @field:NullOrNotBlank val tradingGroup: String?,
    @field:Positive val leverage: Int?,
    val inactivityPeriodDays: Optional<@Positive Int>?,
    @field:Positive val profitTarget: Int?,
    val dailyDrawdown: Optional<@Positive Int>?,
    @field:Positive val maxDrawdown: Int?,
    @field:Positive val profitSplit: Int?,
    @field:PositiveOrZero val minProfitableTradingDays: Int?,
    val maxTradingDays: Optional<@Positive Int>?,
    val consistencyTarget: Optional<@Positive Int>?,
    val nextPayoutAvailableAt: Instant?,
    val capTrailingDrawdownAfterPayout: Boolean?,
    @field:Positive val minimumPayoutLimit: BigDecimal?,
    val maximumPayoutLimit: Optional<@Positive BigDecimal>?,
) : Command<Unit>
