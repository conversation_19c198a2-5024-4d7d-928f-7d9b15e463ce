package com.cleevio.fortraders.application.module.wallet

import com.cleevio.fortraders.application.common.BaseFinderService
import com.cleevio.fortraders.domain.model.wallet.Wallet
import com.cleevio.fortraders.domain.model.wallet.WalletRepository
import com.cleevio.fortraders.domain.model.wallet.exception.WalletNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class WalletFinderService(
    walletRepository: WalletRepository,
) : BaseFinderService<Wallet, WalletRepository>(
    repository = walletRepository,
    notFoundException = ::WalletNotFoundException
) {
    @Transactional(readOnly = true)
    fun getByUserId(userId: UUID): Wallet = repository.findByUserId(userId) ?: throw notFoundException()
}
