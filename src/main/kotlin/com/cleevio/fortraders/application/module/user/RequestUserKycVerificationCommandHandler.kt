package com.cleevio.fortraders.application.module.user

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.USER
import com.cleevio.fortraders.application.module.user.command.RequestUserKycVerificationCommand
import com.cleevio.fortraders.application.module.user.constant.UPDATE_USER
import com.cleevio.fortraders.application.module.user.port.output.RequestUserKycVerification
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class RequestUserKycVerificationCommandHandler(
    private val userFinderService: UserFinderService,
    private val requestUserKycVerification: RequestUserKycVerification,
) : CommandHandler<RequestUserKycVerificationCommand.Result, RequestUserKycVerificationCommand> {
    override val command = RequestUserKycVerificationCommand::class

    @Transactional
    @Lock(module = USER, lockName = UPDATE_USER)
    override fun invoke(
        @LockFieldParameter("userId") command: RequestUserKycVerificationCommand,
    ): RequestUserKycVerificationCommand.Result {
        userFinderService.getById(command.userId).requestKycVerification()
        return RequestUserKycVerificationCommand.Result(
            url = requestUserKycVerification(command.userId)
        )
    }
}
