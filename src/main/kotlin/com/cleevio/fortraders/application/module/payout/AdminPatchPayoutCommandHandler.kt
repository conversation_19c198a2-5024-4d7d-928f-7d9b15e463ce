package com.cleevio.fortraders.application.module.payout

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.PAYOUT
import com.cleevio.fortraders.application.module.payout.command.AdminPatchPayoutCommand
import com.cleevio.fortraders.application.module.payout.constant.UPDATE_PAYOUT
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminPatchPayoutCommandHandler(
    private val payoutFinderService: PayoutFinderService,
) : CommandHandler<Unit, AdminPatchPayoutCommand> {
    override val command = AdminPatchPayoutCommand::class

    @Transactional
    @Lock(module = PAYOUT, lockName = UPDATE_PAYOUT)
    override fun invoke(@LockFieldParameter("payoutId") command: AdminPatchPayoutCommand) {
        payoutFinderService.getById(command.payoutId).patchAdminSpecifiedProperties(
            riskState = command.riskState,
            kycState = command.kycState,
            finalState = command.finalState,
            note = command.note
        )
    }
}
