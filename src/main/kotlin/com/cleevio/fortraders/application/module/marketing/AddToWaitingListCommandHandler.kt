package com.cleevio.fortraders.application.module.marketing

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.MARKETING
import com.cleevio.fortraders.application.module.country.CountryFinderService
import com.cleevio.fortraders.application.module.marketing.command.AddToWaitingListCommand
import com.cleevio.fortraders.application.module.marketing.constant.SYNCHRONISE_TO_MARKETING_PLATFORM
import com.cleevio.fortraders.application.module.marketing.port.output.CreateUserInMarketingPlatformWaitingList
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service

@Service
class AddToWaitingListCommandHandler(
    private val countryFinderService: CountryFinderService,
    private val createUserInMarketingPlatformWaitingList: CreateUserInMarketingPlatformWaitingList,
) : CommandHandler<Unit, AddToWaitingListCommand> {
    override val command = AddToWaitingListCommand::class

    @Lock(module = MARKETING, lockName = SYNCHRONISE_TO_MARKETING_PLATFORM)
    override fun invoke(@LockFieldParameter("email") command: AddToWaitingListCommand) {
        val country = countryFinderService.getById(command.countryId)
        createUserInMarketingPlatformWaitingList(
            email = command.email,
            countryIsoCode = country.isoCode
        )
    }
}
