package com.cleevio.fortraders.application.module.tradingaccount

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.module.tradingaccount.command.AdminUpgradeTradingAccountCommand
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminUpgradeTradingAccountCommandHandler(
    private val tradingAccountUpgradeService: TradingAccountUpgradeService,
) : CommandHandler<Unit, AdminUpgradeTradingAccountCommand> {
    override val command = AdminUpgradeTradingAccountCommand::class

    @Transactional
    override fun invoke(command: AdminUpgradeTradingAccountCommand) {
        tradingAccountUpgradeService.upgradeToNextStep(
            tradingAccountId = command.tradingAccountId,
            internalReason = command.internalReason,
            externalReason = command.externalReason
        )
    }
}
