package com.cleevio.fortraders.application.module.certificate

import com.cleevio.fortraders.application.common.constant.CERTIFICATE
import com.cleevio.fortraders.application.common.constant.PropFirm
import com.cleevio.fortraders.application.common.util.formatUSD
import com.cleevio.fortraders.application.module.certificate.constant.CREATE_OR_UPDATE_CERTIFICATE
import com.cleevio.fortraders.application.module.challengestep.ChallengeStepFinderService
import com.cleevio.fortraders.application.module.order.OrderFinderService
import com.cleevio.fortraders.application.module.payout.PayoutFinderService
import com.cleevio.fortraders.application.module.tradingaccount.TradingAccountFinderService
import com.cleevio.fortraders.application.module.user.UserFinderService
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.certificate.CertificateCreateService
import com.cleevio.fortraders.domain.model.certificate.constant.CertificateType
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CertificateService(
    private val tradingAccountFinderService: TradingAccountFinderService,
    private val challengeStepFinderService: ChallengeStepFinderService,
    private val orderFinderService: OrderFinderService,
    private val userFinderService: UserFinderService,
    private val payoutFinderService: PayoutFinderService,
    private val certificateFinderService: CertificateFinderService,
    private val certificateCreateService: CertificateCreateService,

    @Value("\${fortraders.deployment.prop-firm}")
    private val propFirm: PropFirm,
) {
    @Transactional
    @Lock(module = CERTIFICATE, lockName = CREATE_OR_UPDATE_CERTIFICATE)
    fun createEvaluationStepCompletedCertificate(@LockArgumentParameter tradingAccountId: UUID, breachType: BreachType) {
        if (breachType.isFinalBreachType()) return

        val tradingAccount = tradingAccountFinderService.getById(tradingAccountId)
        challengeStepFinderService.getById(tradingAccount.challengeStepId).also {
            if (!it.isEvaluationStep()) return
        }
        val order = orderFinderService.getById(tradingAccount.orderId)
        val user = userFinderService.getById(order.userId)

        val certificateType = if (tradingAccount.isLastEvaluationStep) {
            CertificateType.LAST_EVALUATION_STEP
        } else {
            CertificateType.EVALUATION_STEP
        }
        certificateCreateService.create(
            tradingAccountId = tradingAccount.id,
            type = certificateType,
            header = user.getAnonymizedName(),
            title = "Passed",
            subtitle = "${propFirm.title} Challenge"
        )
    }

    @Transactional
    @Lock(module = CERTIFICATE, lockName = CREATE_OR_UPDATE_CERTIFICATE)
    fun createAndUpdateApprovedPayoutCertificates(@LockArgumentParameter tradingAccountId: UUID, payoutId: UUID) {
        val tradingAccount = tradingAccountFinderService.getById(tradingAccountId)
        val order = orderFinderService.getById(tradingAccount.orderId)
        val user = userFinderService.getById(order.userId)
        val payout = payoutFinderService.getById(payoutId)
        val payoutAmount = payout.amountAfterSplit.formatUSD()

        certificateCreateService.create(
            tradingAccountId = tradingAccount.id,
            type = CertificateType.PAYOUT,
            header = user.getAnonymizedName(),
            title = payoutAmount,
            subtitle = "Profit Share"
        )

        val totalPayoutsCertificate = certificateFinderService.findTotalPayoutsTypeByTradingAccountId(tradingAccount.id)
        if (totalPayoutsCertificate == null) {
            certificateCreateService.create(
                tradingAccountId = tradingAccount.id,
                type = CertificateType.TOTAL_PAYOUTS,
                header = user.getAnonymizedName(),
                title = payoutAmount,
                subtitle = "Total Payouts"
            )
            return
        }

        totalPayoutsCertificate.updateTitle(
            payoutFinderService.findAllApprovedByTradingAccountId(tradingAccountId)
                .map { it.amountAfterSplit }
                .sumOf { it }
                .formatUSD()
        )
    }
}
