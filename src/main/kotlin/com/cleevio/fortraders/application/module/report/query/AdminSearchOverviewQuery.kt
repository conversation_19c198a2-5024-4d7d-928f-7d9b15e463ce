package com.cleevio.fortraders.application.module.report.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanCategory
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.domain.Sort
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

data class AdminSearchOverviewQuery(
    val sort: Sort,
    val filter: Filter,
) : Query<AdminSearchOverviewQuery.Result> {
    data class Filter(
        val platforms: Set<PlatformType>?,
        val newUsersFrom: LocalDate,
        val newUsersTo: LocalDate,
        val newClientsFrom: LocalDate,
        val newClientsTo: LocalDate,
        val newTradingAccountsFrom: LocalDate,
        val newTradingAccountsTo: LocalDate,
        val trendsOverviewFrom: LocalDate,
        val trendsOverviewTo: LocalDate,
        val fundedAccountsOverviewFrom: LocalDate,
        val fundedAccountsOverviewTo: LocalDate,
        val instantAccountsOverviewFrom: LocalDate,
        val instantAccountsOverviewTo: LocalDate,
        val activeAccountsFrom: LocalDate,
        val activeAccountsTo: LocalDate,
        val activeAccountSelected: LocalDate,
        val challengeTypes: Set<ChallengeType>?,
    )

    @Schema(name = "AdminSearchOverviewResult")
    data class Result(
        val activeAccounts: AccountsDetail,
        val newUsers: NewUsersDetail,
        val newClients: NewClientsDetail,
        val newTradingAccounts: TradingAccountsDetail,
        val possiblePayouts: PayoutsDetail,
        val orders: OrdersDetail,
        val trends: TrendsDetail,
        val fundedAccounts: FundedAccountsDetail,
        val instantAccounts: InstantAccountsDetail,
    )

    @Schema(name = "AdminSearchOverviewResultAccountsDetail")
    data class AccountsDetail(
        val total: Int,
        val thisMonth: Int,
        val challenges: List<ChallengeDetail>,
    )

    @Schema(name = "AdminSearchOverviewResultChallengeDetail")
    data class ChallengeDetail(
        val id: UUID,
        val name: String,
        val startingBalance: BigDecimal,
        val type: ChallengeType,
        val plansDateRange: List<ChallengePlanDetail>,
        val plansToDate: List<ChallengePlanDetail>,
    )

    @Schema(name = "AdminSearchOverviewResultChallengePlanDetail")
    data class ChallengePlanDetail(
        val title: String,
        val steps: Int,
        val category: ChallengePlanCategory,
        val totalAccounts: Int,
        val stepNumber: Int,
        val stepType: ChallengeStepType,
        val date: LocalDate?,
    )

    @Schema(name = "AdminSearchOverviewResultNewUsersDetail")
    data class NewUsersDetail(
        val total: Int,
        val counts: List<NewUsersCountDetail>,
    )

    @Schema(name = "AdminSearchOverviewResultNewClientsDetail")
    data class NewClientsDetail(
        val total: Int,
        val counts: List<NewClientsCountDetail>,
    )

    @Schema(name = "AdminSearchOverviewResultNewUsersCountDetail")
    data class NewUsersCountDetail(
        val date: LocalDate,
        val count: Int,
    )

    @Schema(name = "AdminSearchOverviewResultNewClientsCountDetail")
    data class NewClientsCountDetail(
        val date: LocalDate,
        val count: Int,
    )

    @Schema(name = "AdminSearchOverviewResultTradingAccountsDetail")
    data class TradingAccountsDetail(
        val total: Int,
        val counts: List<TradingAccountCountDetail>,
    )

    @Schema(name = "AdminSearchOverviewResultTradingAccountCountDetail")
    data class TradingAccountCountDetail(
        val date: LocalDate,
        val count: Int,
    )

    @Schema(name = "AdminSearchOverviewResultPayoutsDetail")
    data class PayoutsDetail(
        val total: BigDecimal,
        val topTradingAccounts: List<TopTradingAccountDetail>,
    )

    @Schema(name = "AdminSearchOverviewResultTopTradingAccountDetail")
    data class TopTradingAccountDetail(
        val id: UUID,
        val accountId: String,
        val platform: PlatformType,
        val plan: String,
        val startingBalance: BigDecimal,
        val equity: BigDecimal,
        val profit: BigDecimal,
        val profitAfterSplit: BigDecimal,
        val profitSplit: Int,
        val nextPayoutAvailableAt: Instant,
        val user: UserDetail,
    )

    @Schema(name = "AdminSearchOverviewResultUserDetail")
    data class UserDetail(
        val id: UUID,
        val firstName: String?,
        val lastName: String?,
        val email: String,
    )

    @Schema(name = "AdminSearchOverviewResultOrdersDetail")
    data class OrdersDetail(
        val totalCount: Int,
        val totalTurnover: BigDecimal,
        val countries: List<CountryDetail>,
    )

    @Schema(name = "AdminSearchOverviewResultCountryDetail")
    data class CountryDetail(
        val id: UUID,
        val name: String,
        val countToday: Int,
        val countThisWeek: Int,
        val countLastWeek: Int,
        val countLastMonth: Int,
        val countTotal: Int,
        val turnoverToday: BigDecimal,
        val turnoverThisWeek: BigDecimal,
        val turnoverLastWeek: BigDecimal,
        val turnoverLastMonth: BigDecimal,
        val turnoverTotal: BigDecimal,
    )

    @Schema(name = "AdminSearchOverviewResultTrendsDetail")
    data class TrendsDetail(
        val ordersTotalCount: Int,
        val breachesTotalCount: Int,
        val counts: List<TrendCountDetail>,
    )

    @Schema(name = "AdminSearchOverviewResultTrendsDetailCountDetail")
    data class TrendCountDetail(
        val date: LocalDate,
        val ordersCount: Int,
        val breachesCount: Int,
    )

    @Schema(name = "AdminSearchOverviewResultFundedAccountsDetail")
    data class FundedAccountsDetail(
        val newAccountsTotalCount: Int,
        val breachedAccountsTotalCount: Int,
        val counts: List<FundedAccountCountDetail>,
    )

    @Schema(name = "AdminSearchOverviewResultFundedAccountCountDetail")
    data class FundedAccountCountDetail(
        val date: LocalDate,
        val newAccountsCount: Int,
        val breachedAccountsCount: Int,
    )

    @Schema(name = "AdminSearchOverviewResultInstantAccountsDetail")
    data class InstantAccountsDetail(
        val newAccountsTotalCount: Int,
        val breachedAccountsTotalCount: Int,
        val counts: List<InstantAccountCountDetail>,
    )

    @Schema(name = "AdminSearchOverviewResultInstantAccountCountDetail")
    data class InstantAccountCountDetail(
        val date: LocalDate,
        val newAccountsCount: Int,
        val breachedAccountsCount: Int,
    )
}
