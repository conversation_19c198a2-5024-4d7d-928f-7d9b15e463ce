package com.cleevio.fortraders.application.module.withdrawalrequest

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.WITHDRAWAL_REQUEST
import com.cleevio.fortraders.application.module.withdrawalrequest.command.AdminRejectWithdrawalRequestCommand
import com.cleevio.fortraders.application.module.withdrawalrequest.constant.UPDATE_WITHDRAWAL_REQUEST
import com.cleevio.fortraders.domain.model.withdrawalrequest.event.WithdrawalRequestRejectedEvent
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminRejectWithdrawalRequestCommandHandler(
    private val withdrawalRequestFinderService: WithdrawalRequestFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : <PERSON><PERSON><PERSON><PERSON><Unit, AdminRejectWithdrawalRequestCommand> {
    override val command = AdminRejectWithdrawalRequestCommand::class

    @Transactional
    @Lock(module = WITHDRAWAL_REQUEST, lockName = UPDATE_WITHDRAWAL_REQUEST)
    override fun invoke(@LockFieldParameter("withdrawalRequestId") command: AdminRejectWithdrawalRequestCommand) {
        withdrawalRequestFinderService.getById(command.withdrawalRequestId).also {
            it.reject(command.reason)
            applicationEventPublisher.publishEvent(
                WithdrawalRequestRejectedEvent(
                    withdrawalRequestId = it.id,
                    userId = it.userId
                )
            )
        }
    }
}
