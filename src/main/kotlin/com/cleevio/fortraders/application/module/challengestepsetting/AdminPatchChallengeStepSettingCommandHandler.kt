package com.cleevio.fortraders.application.module.challengestepsetting

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.CHALLENGE_STEP_SETTING
import com.cleevio.fortraders.application.module.challengeplan.ChallengePlanFinderService
import com.cleevio.fortraders.application.module.challengestep.ChallengeStepFinderService
import com.cleevio.fortraders.application.module.challengestepsetting.command.AdminPatchChallengeStepSettingCommand
import com.cleevio.fortraders.application.module.challengestepsetting.constant.CREATE_OR_UPDATE_CHALLENGE_STEP_SETTING
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminPatchChallengeStepSettingCommandHandler(
    private val challengePlanFinderService: ChallengePlanFinderService,
    private val challengeStepFinderService: ChallengeStepFinderService,
    private val challengeStepSettingFinderService: ChallengeStepSettingFinderService,
) : CommandHandler<Unit, AdminPatchChallengeStepSettingCommand> {
    override val command = AdminPatchChallengeStepSettingCommand::class

    @Transactional
    @Lock(module = CHALLENGE_STEP_SETTING, lockName = CREATE_OR_UPDATE_CHALLENGE_STEP_SETTING)
    override fun invoke(@LockFieldParameter("challengePlanId") command: AdminPatchChallengeStepSettingCommand) {
        challengePlanFinderService.getById(command.challengePlanId).verifyIsDraft()
        val challengeStep = challengeStepFinderService.getByChallengePlanIdAndNumber(
            challengePlanId = command.challengePlanId,
            number = command.challengeStepNumber
        )
        challengeStepSettingFinderService.getByChallengeStepIdAndType(
            challengeStepId = challengeStep.id,
            type = command.challengeStepSettingType
        ).patchAdminSpecifiedProperties(
            isFirstChallengeStep = challengeStep.isFirstStep(),
            isFundedChallengeStep = challengeStep.isFundedStep(),
            useMovementsFromOrder = command.useMovementsFromOrder,
            isVisibleInConfigurator = command.isVisibleInConfigurator
        )
    }
}
