package com.cleevio.fortraders.application.module.tournamenttrade.command

import com.cleevio.fortraders.application.common.command.Command
import com.cleevio.fortraders.domain.model.tournamenttrade.constant.TournamentTradeType
import org.hibernate.validator.constraints.Range
import java.math.BigDecimal
import java.util.UUID

data class CreateTournamentTradeCommand(
    val gameId: UUID,
    val tournamentId: UUID,
    val userId: UUID,
    val instrumentId: UUID,
    val type: TournamentTradeType,
    @field:Range(min = 1, max = 20) val leverage: Int,
    val stopLoss: BigDecimal?,
    val takeProfit: BigDecimal?,
) : Command<Unit>
