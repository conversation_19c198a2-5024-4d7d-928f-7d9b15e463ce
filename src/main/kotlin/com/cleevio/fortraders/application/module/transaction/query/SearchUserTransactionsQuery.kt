package com.cleevio.fortraders.application.module.transaction.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class SearchUserTransactionsQuery(
    val pageable: Pageable,
    val filter: Filter,
) : Query<Page<SearchUserTransactionsQuery.Result>> {
    data class Filter(
        val userId: UUID,
        val types: Set<TransactionType>?,
        val statuses: Set<TransactionStatus>?,
        val createdAtFrom: Instant?,
        val createdAtTo: Instant?,
    )

    @Schema(name = "SearchUserTransactionsResult")
    data class Result(
        val id: UUID,
        val type: TransactionType,
        val amount: BigDecimal,
        val status: TransactionStatus,
        val createdAt: Instant,
        val updatedAt: Instant,
        val invoicePdfUrl: String?,
    )
}
