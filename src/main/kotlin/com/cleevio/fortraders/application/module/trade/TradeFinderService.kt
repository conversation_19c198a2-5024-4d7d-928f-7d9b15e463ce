package com.cleevio.fortraders.application.module.trade

import com.cleevio.fortraders.application.common.BaseFinderService
import com.cleevio.fortraders.domain.model.trade.Trade
import com.cleevio.fortraders.domain.model.trade.TradeRepository
import com.cleevio.fortraders.domain.model.trade.exception.TradeNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.UUID

@Service
class TradeFinderService(
    tradeRepository: TradeRepository,
) : BaseFinderService<Trade, TradeRepository>(
    repository = tradeRepository,
    notFoundException = ::TradeNotFoundException
) {
    @Transactional(readOnly = true)
    fun findAllByTradingAccountIdOrderedByCloseTimeDescending(tradingAccountId: UUID): List<Trade> =
        repository.findAllByTradingAccountIdOrderedByCloseTimeDesc(
            tradingAccountId = tradingAccountId
        )

    @Transactional(readOnly = true)
    fun findAllDealIdsByTradingAccountId(tradingAccountId: UUID): Set<String> = repository.findAllDealIdsByTradingAccountId(
        tradingAccountId = tradingAccountId
    )

    @Transactional(readOnly = true)
    fun findAllDealIdsByTradingAccountIds(tradingAccountIds: Set<UUID>): Set<String> =
        repository.findAllDealIdsByTradingAccountIdIn(
            tradingAccountIds = tradingAccountIds
        )

    @Transactional(readOnly = true)
    fun findLastTradeCloseTimeForAccounts(tradingAccountIds: Set<UUID>): Map<UUID, Instant> =
        repository.findLastTradeCloseTimeForAccounts(tradingAccountIds)
            .associateBy { it.tradingAccountId }
            .mapValues { it.value.lastClosedTradeTime }
}
