package com.cleevio.fortraders.application.module.order

import com.cleevio.fortraders.application.common.query.QueryHandler
import com.cleevio.fortraders.application.module.order.port.output.GetUserOrders
import com.cleevio.fortraders.application.module.order.query.GetUserOrdersQuery
import org.springframework.stereotype.Service

@Service
class GetUserOrdersQueryHandler(
    private val getUserOrders: GetUserOrders,
) : QueryHandler<GetUserOrdersQuery.Result, GetUserOrdersQuery> {
    override val query = GetUserOrdersQuery::class

    override fun handle(query: GetUserOrdersQuery): GetUserOrdersQuery.Result = getUserOrders(query.userId)
}
