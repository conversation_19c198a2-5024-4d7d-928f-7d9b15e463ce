package com.cleevio.fortraders.application.module.tradingaccount.port.output

import java.util.UUID

interface ActivateFeedSubscription : PlatformSpecificPort {
    operator fun invoke(
        orderId: UUID,
        email: String,
        firstName: String,
        lastName: String,
        country: String?,
        streetAddress: String?,
        city: String?,
        postCode: String?,
        phone: String?,
    ): Result<ActivateFeedSubscriptionResult>
}

data class ActivateFeedSubscriptionResult(
    val feedAccountId: UUID,
    val onboardingUrl: String?,
)
