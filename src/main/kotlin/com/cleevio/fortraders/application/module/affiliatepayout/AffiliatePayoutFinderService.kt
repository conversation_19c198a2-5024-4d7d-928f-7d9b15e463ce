package com.cleevio.fortraders.application.module.affiliatepayout

import com.cleevio.fortraders.application.common.BaseFinderService
import com.cleevio.fortraders.domain.model.affiliatepayout.AffiliatePayout
import com.cleevio.fortraders.domain.model.affiliatepayout.AffiliatePayoutRepository
import com.cleevio.fortraders.domain.model.affiliatepayout.constant.AffiliatePayoutState
import com.cleevio.fortraders.domain.model.affiliatepayout.exception.AffiliatePayoutNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class AffiliatePayoutFinderService(
    affiliatePayoutRepository: AffiliatePayoutRepository,
) : BaseFinderService<AffiliatePayout, AffiliatePayoutRepository>(
    repository = affiliatePayoutRepository,
    notFoundException = ::AffiliatePayoutNotFoundException
) {
    @Transactional(readOnly = true)
    fun existsRequestedByAffiliateId(affiliateId: UUID): Boolean = repository.existsByAffiliateIdAndState(
        affiliateId = affiliateId,
        state = AffiliatePayoutState.REQUESTED
    )
}
