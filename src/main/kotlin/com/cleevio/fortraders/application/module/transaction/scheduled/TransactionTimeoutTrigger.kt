package com.cleevio.fortraders.application.module.transaction.scheduled

import com.cleevio.fortraders.application.common.constant.TRANSACTION
import com.cleevio.fortraders.application.module.transaction.TransactionService
import com.cleevio.fortraders.application.module.transaction.constant.CANCEL_UNPROCESSED_DEPOSIT_TRANSACTIONS
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class TransactionTimeoutTrigger(
    private val transactionService: TransactionService,
) {
    @TryLock(module = TRANSACTION, lockName = CANCEL_UNPROCESSED_DEPOSIT_TRANSACTIONS)
    @Scheduled(cron = "\${fortraders.transaction.deposit.expiration-processing.cron}")
    fun trigger() {
        transactionService.cancelUnprocessedDepositTransactions()
    }
}
