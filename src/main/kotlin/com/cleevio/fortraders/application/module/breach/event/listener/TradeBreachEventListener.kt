package com.cleevio.fortraders.application.module.breach.event.listener

import com.cleevio.fortraders.application.module.breach.InactivityBreachMonitoringService
import com.cleevio.fortraders.domain.model.trade.event.TradesSynchronisedEvent
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@Profile("!test")
class TradeBreachEventListener(
    private val inactivityBreachMonitoringService: InactivityBreachMonitoringService,
) {
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    fun handleAsyncTradesSynchronisedEvent(event: TradesSynchronisedEvent) {
        inactivityBreachMonitoringService.verifyInactivityOfAllAccounts()
    }
}
