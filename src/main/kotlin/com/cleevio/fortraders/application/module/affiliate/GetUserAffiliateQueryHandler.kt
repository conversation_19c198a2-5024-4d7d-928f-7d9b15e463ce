package com.cleevio.fortraders.application.module.affiliate

import com.cleevio.fortraders.application.common.query.QueryHandler
import com.cleevio.fortraders.application.module.affiliate.port.output.GetUserAffiliate
import com.cleevio.fortraders.application.module.affiliate.query.GetUserAffiliateQuery
import org.springframework.stereotype.Service

@Service
class GetUserAffiliateQueryHandler(
    private val getUserAffiliate: GetUserAffiliate,
) : QueryHandler<GetUserAffiliateQuery.Result, GetUserAffiliateQuery> {
    override val query = GetUserAffiliateQuery::class

    override fun handle(query: GetUserAffiliateQuery): GetUserAffiliateQuery.Result = getUserAffiliate(
        userId = query.userId
    )
}
