package com.cleevio.fortraders.application.module.user.command

import com.cleevio.fortraders.application.common.command.Command
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class RequestUserKycVerificationCommand(
    val userId: UUID,
) : Command<RequestUserKycVerificationCommand.Result> {

    @Schema(name = "RequestUserKycVerificationResult")
    data class Result(
        val url: String,
    )
}
