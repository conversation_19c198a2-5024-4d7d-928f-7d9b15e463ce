package com.cleevio.fortraders.application.module.notification

import com.cleevio.fortraders.application.module.user.UserFinderService
import com.cleevio.fortraders.application.module.verificationcall.VerificationCallFinderService
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailType
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailVariables
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.util.UriComponentsBuilder
import java.util.UUID

@Service
class VerificationCallNotificationProcessingService(
    private val userFinderService: UserFinderService,
    private val mailNotificationService: MailNotificationService,

    @Value("\${integration.calendly.base-booking-url}")
    private val baseScheduleUrl: String,
    private val verificationCallFinderService: VerificationCallFinderService,
) {
    fun processVerificationCallNotification(verificationCallId: UUID, userId: UUID) {
        val user = userFinderService.getById(userId)
        val verificationCall = verificationCallFinderService.getById(verificationCallId)
        val scheduleLink = createVerificationCallScheduleLink(
            verificationCallId = verificationCallId,
            firstName = user.firstName,
            lastName = user.lastName,
            email = user.email
        )

        val mailVariables = mapOf(
            MailVariables.BOOKING_URL to scheduleLink,
            MailVariables.EMAIL_CONTENT to verificationCall.emailContent
        )

        mailNotificationService.sendEmailNotificationToUser(
            userId = userId,
            subjectId = verificationCallId,
            type = MailType.VERIFICATION_CALL_REQUEST,
            mailVariables = mailVariables
        )
    }

    private fun createVerificationCallScheduleLink(
        verificationCallId: UUID,
        firstName: String?,
        lastName: String?,
        email: String,
    ): String = UriComponentsBuilder.fromUriString(baseScheduleUrl)
        .queryParam("first_name", firstName ?: "")
        .queryParam("last_name", lastName ?: "")
        .queryParam("email", email)
        .queryParam("utm_content", verificationCallId.toString())
        .toUriString()
}
