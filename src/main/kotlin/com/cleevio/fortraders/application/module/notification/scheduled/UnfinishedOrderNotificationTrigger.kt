package com.cleevio.fortraders.application.module.notification.scheduled

import com.cleevio.fortraders.application.common.constant.NOTIFICATION
import com.cleevio.fortraders.application.module.notification.OrderNotificationProcessingService
import com.cleevio.fortraders.application.module.notification.constant.ABANDONED_ORDER
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class UnfinishedOrderNotificationTrigger(
    private val orderNotificationProcessingService: OrderNotificationProcessingService,
) {
    @TryLock(module = NOTIFICATION, lockName = ABANDONED_ORDER)
    @Scheduled(cron = "\${fortraders.notification.unfinished-order.cron}")
    fun trigger() {
        orderNotificationProcessingService.processAbandonedOrders()
    }
}
