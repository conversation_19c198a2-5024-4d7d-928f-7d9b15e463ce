package com.cleevio.fortraders.application.module.tradingaccount

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.TRADING_ACCOUNT
import com.cleevio.fortraders.application.common.util.ifFalse
import com.cleevio.fortraders.application.module.tradingaccount.command.AdminReactivateDailyPausedTradingAccountCommand
import com.cleevio.fortraders.application.module.tradingaccount.constant.UPDATE_TRADING_ACCOUNT
import com.cleevio.fortraders.domain.model.tradingaccount.exception.TradingAccountNotPausedException
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminReactivateDailyPausedTradingAccountCommandHandler(
    private val tradingAccountFinderService: TradingAccountFinderService,
    private val tradingAccountService: TradingAccountService,
) : CommandHandler<Unit, AdminReactivateDailyPausedTradingAccountCommand> {
    override val command = AdminReactivateDailyPausedTradingAccountCommand::class

    @Transactional
    @Lock(module = TRADING_ACCOUNT, lockName = UPDATE_TRADING_ACCOUNT)
    override fun invoke(@LockFieldParameter("tradingAccountId") command: AdminReactivateDailyPausedTradingAccountCommand) {
        val tradingAccount = tradingAccountFinderService.getById(command.tradingAccountId)
        tradingAccount.isDailyPaused().ifFalse {
            throw TradingAccountNotPausedException()
        }
        tradingAccountService.reactivateDailyPausedAccount(tradingAccount.id)
    }
}
