package com.cleevio.fortraders.application.module.tournament.command

import com.cleevio.fortraders.application.common.command.Command
import com.cleevio.fortraders.application.common.validation.NullOrNotBlank
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentPrizePoolType
import com.cleevio.fortraders.domain.model.tournament.constant.TournamentReward
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.PositiveOrZero
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class AdminCreateTournamentCommand(
    val gameId: UUID,
    @field:NotBlank val name: String,
    val reward: TournamentReward,
    @field:Positive val initialPrizePool: BigDecimal?,
    val prizePoolType: TournamentPrizePoolType,
    @field:PositiveOrZero val entryFee: BigDecimal,
    @field:Positive val buyInsLimit: Int?,
    @field:Positive val maxDrawdown: Int,
    @field:NullOrNotBlank val rules: String?,
    val startsAt: Instant,
    val endsAt: Instant,
    val coverFileId: UUID,
    val leverageEnabled: <PERSON>olean,
) : Command<Unit>
