package com.cleevio.fortraders.application.module.withdrawalrequest.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.withdrawalrequest.constant.WithdrawalRequestStatus
import com.cleevio.fortraders.domain.model.withdrawalrequest.constant.WithdrawalRequestType
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class AdminGetWithdrawalRequestDetailsQuery(
    val withdrawalRequestId: UUID,
) : Query<AdminGetWithdrawalRequestDetailsQuery.Result> {

    @Schema(name = "AdminGetWithdrawalRequestResult")
    data class Result(
        val id: UUID,
        val user: UserDetails,
        val amount: BigDecimal,
        val type: WithdrawalRequestType,
        val accountHolderFullName: String?,
        val bankName: String?,
        val bankAddress: String?,
        val iban: String?,
        val swift: String?,
        val erc20Address: String?,
        val riseEmail: String?,
        val status: WithdrawalRequestStatus,
        val rejectReason: String?,
        val createdAt: Instant,
        val updatedAt: Instant,
        val sumOfWithdrawals: BigDecimal,
    )

    @Schema(name = "AdminGetWithdrawalRequestUserDetails")
    data class UserDetails(
        val id: UUID,
        val email: String,
        val phonePrefix: String?,
        val phoneNumber: String?,
        val firstName: String?,
        val lastName: String?,
        val profileImageUrl: String?,
        val blacklisted: Boolean,
    )
}
