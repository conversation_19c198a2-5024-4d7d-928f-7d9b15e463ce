package com.cleevio.fortraders.application.module.wallet

import com.cleevio.fortraders.application.common.constant.WALLET
import com.cleevio.fortraders.application.module.transaction.TransactionFinderService
import com.cleevio.fortraders.application.module.wallet.constant.WALLET_BALANCE_MANIPULATION
import com.cleevio.fortraders.application.module.withdrawalrequest.WithdrawalRequestFinderService
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import com.cleevio.fortraders.domain.model.wallet.event.WalletBalanceChangedEvent
import com.cleevio.fortraders.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class WalletService(
    private val walletFinderService: WalletFinderService,
    private val withdrawalRequestFinderService: WithdrawalRequestFinderService,
    private val transactionFinderService: TransactionFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val logger = logger()

    @Transactional
    @Lock(module = WALLET, lockName = WALLET_BALANCE_MANIPULATION)
    fun processTransaction(transactionId: UUID, @LockArgumentParameter walletId: UUID) {
        logger.info("Processing transaction $transactionId")
        val transaction = transactionFinderService.getById(transactionId)
        // withdrawal requests deduct from balance immediately when requested
        if (transaction.type.isWithdrawalRelated()) return

        if (transaction.status == TransactionStatus.COMPLETED) {
            val wallet = walletFinderService.getById(walletId).apply {
                if (transaction.type.isAddingToBalance()) {
                    addToBalance(transaction.amount)
                } else {
                    deductFromBalance(transaction.amount)
                }
                applicationEventPublisher.publishEvent(
                    WalletBalanceChangedEvent(
                        userId = userId,
                        balance = balance,
                        updatedAt = updatedAt
                    )
                )
            }

            logger.info(
                "Added ${transaction.amount} balance to user ${wallet.userId} because of successful transaction $transactionId"
            )
            return
        }
        logger.info("Transaction $transactionId is not completed, skipping processing")
    }

    @Transactional
    @Lock(module = WALLET, lockName = WALLET_BALANCE_MANIPULATION)
    fun processWithdrawalRequestCreated(withdrawalRequestId: UUID, @LockArgumentParameter walletId: UUID) {
        val withdrawalRequest = withdrawalRequestFinderService.getById(withdrawalRequestId)
        val wallet = walletFinderService.getById(walletId)
        wallet.deductFromBalance(withdrawalRequest.amount)
    }

    @Transactional
    @Lock(module = WALLET, lockName = WALLET_BALANCE_MANIPULATION)
    fun processWithdrawalRequestRejected(withdrawalRequestId: UUID, @LockArgumentParameter walletId: UUID) {
        val withdrawalRequest = withdrawalRequestFinderService.getById(withdrawalRequestId)
        val wallet = walletFinderService.getById(walletId)
        wallet.addToBalance(withdrawalRequest.amount)
    }
}
