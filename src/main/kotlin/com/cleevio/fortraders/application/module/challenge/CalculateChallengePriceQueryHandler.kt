package com.cleevio.fortraders.application.module.challenge

import com.cleevio.fortraders.application.common.query.QueryHandler
import com.cleevio.fortraders.application.module.challenge.port.output.CalculateChallengePrice
import com.cleevio.fortraders.application.module.challenge.query.CalculateChallengePriceQuery
import org.springframework.stereotype.Service

@Service
class CalculateChallengePriceQueryHandler(
    private val calculateChallengePrice: CalculateChallengePrice,
) : QueryHandler<CalculateChallengePriceQuery.Result, CalculateChallengePriceQuery> {
    override val query = CalculateChallengePriceQuery::class

    override fun handle(query: CalculateChallengePriceQuery): CalculateChallengePriceQuery.Result = calculateChallengePrice(
        userId = query.userId,
        challengeId = query.challengeId,
        challengePlanId = query.challengePlanId,
        platform = query.platform,
        profitTargetSettingId = query.profitTargetSettingId,
        maxDrawdownSettingId = query.maxDrawdownSettingId,
        dailyDrawdownSettingId = query.dailyDrawdownSettingId,
        payoutsTypeSettingId = query.payoutsTypeSettingId,
        profitSplitSettingId = query.profitSplitSettingId,
        refundSettingId = query.refundSettingId,
        dailyPauseSettingId = query.dailyPauseSettingId,
        dailyProfitCapSettingId = query.dailyProfitCapSettingId,
        discountCode = query.discountCode,
        affiliateCode = query.affiliateCode
    )
}
