package com.cleevio.fortraders.application.module.orderblacklist.command

import com.cleevio.fortraders.application.common.command.Command
import com.cleevio.fortraders.application.common.validation.NullOrNotBlank
import jakarta.validation.constraints.Email

data class AdminAddEmailToOrderBlacklistCommand(
    @field:Email val email: String,
    @field:NullOrNotBlank val note: String?,
) : Command<Unit>
