package com.cleevio.fortraders.application.module.contract

import com.cleevio.fortraders.application.common.query.QueryHandler
import com.cleevio.fortraders.application.module.contract.port.output.AdminGetContractTemplates
import com.cleevio.fortraders.application.module.contract.query.AdminGetContractTemplatesQuery
import org.springframework.stereotype.Service

@Service
class AdminGetContractTemplatesQueryHandler(
    private val adminGetContractTemplates: AdminGetContractTemplates,
) : QueryHandler<List<AdminGetContractTemplatesQuery.Result>, AdminGetContractTemplatesQuery> {
    override val query = AdminGetContractTemplatesQuery::class

    override fun handle(query: AdminGetContractTemplatesQuery): List<AdminGetContractTemplatesQuery.Result> =
        adminGetContractTemplates()
}
