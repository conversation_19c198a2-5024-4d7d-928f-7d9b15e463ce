package com.cleevio.fortraders.application.module.user.command

import com.cleevio.fortraders.application.common.command.Command
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern
import java.util.UUID

data class AdminUpdateUserDetailsCommand(
    val userId: UUID,
    @field:NotBlank val firstName: String,
    @field:NotBlank val lastName: String,
    @field:Pattern(regexp = "^\\+[0-9]{1,3}\$") val phonePrefix: String,
    @field:NotBlank val phoneNumber: String,
    val countryId: UUID,
    @field:NotBlank val streetAddress: String,
    @field:NotBlank val city: String,
    @field:NotBlank val postCode: String,
) : Command<Unit>
