package com.cleevio.fortraders.application.module.usercontract.event.listener

import com.cleevio.fortraders.application.module.usercontract.UserContractProcessingService
import com.cleevio.fortraders.domain.model.tradingaccountforreview.event.TradingAccountForReviewApprovedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class TradingAccountForReviewUserContractEventListener(
    private val userContractProcessingService: UserContractProcessingService,
) {
    @EventListener
    fun handleTradingAccountForReviewApprovedEvent(event: TradingAccountForReviewApprovedEvent) {
        userContractProcessingService.createUserContractAfterReviewApproved(
            tradingAccountId = event.tradingAccountId,
            contractTemplateId = event.contractTemplateId
        )
    }
}
