package com.cleevio.fortraders.application.module.affiliate.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.affiliate.constant.AffiliateState
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class AdminSearchAffiliatesQuery(
    val pageable: Pageable,
    val filter: Filter,
) : Query<Page<AdminSearchAffiliatesQuery.Result>> {
    data class Filter(
        val referralCountFrom: Int?,
        val referralCountTo: Int?,
        val states: Set<AffiliateState>?,
        val fulltext: String?,
    )

    @Schema(name = "AdminSearchAffiliatesResult")
    data class Result(
        val id: UUID,
        val state: AffiliateState,
        val couponCode: String,
        val discountAmountPercentage: Int,
        val commissionPercentage: Int,
        val commissionBalance: BigDecimal,
        val note: String?,
        val referralCount: Int,
        val createdAt: Instant,
        val updatedAt: Instant,
        val user: UserDetail,
    )

    @Schema(name = "AdminSearchAffiliatesResultUserDetail")
    data class UserDetail(
        val id: UUID,
        val email: String,
        val firstName: String?,
        val lastName: String?,
        val blacklisted: Boolean,
    )
}
