package com.cleevio.fortraders.application.module.tournament.event.listener

import com.cleevio.fortraders.domain.model.tournament.TournamentPrizePoolDistributionService
import com.cleevio.fortraders.domain.model.tournament.event.TournamentEndedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class TournamentTournamentEventListener(
    private val tournamentPrizePoolDistributionService: TournamentPrizePoolDistributionService,
) {
    @EventListener
    fun handleTournamentEndedEvent(event: TournamentEndedEvent) {
        tournamentPrizePoolDistributionService.distributePrizePool(event.tournamentId)
    }
}
