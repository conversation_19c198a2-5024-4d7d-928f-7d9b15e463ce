package com.cleevio.fortraders.application.module.tournament

import com.cleevio.fortraders.application.common.BaseFinderService
import com.cleevio.fortraders.domain.model.tournament.Tournament
import com.cleevio.fortraders.domain.model.tournament.TournamentRepository
import com.cleevio.fortraders.domain.model.tournament.exception.TournamentNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.UUID

@Service
class TournamentFinderService(
    tournamentRepository: TournamentRepository,
) : BaseFinderService<Tournament, TournamentRepository>(
    repository = tournamentRepository,
    notFoundException = ::TournamentNotFoundException
) {
    @Transactional(readOnly = true)
    fun findIdsOfNotEnded(): Set<UUID> = repository.findByEndsAtGreaterThan(Instant.now())

    @Transactional(readOnly = true)
    fun findAllNotEnded(): List<Tournament> = repository.findAllByEndsAtGreaterThan(Instant.now())

    @Transactional(readOnly = true)
    fun findAllNotStartedAndNotProcessed(): List<Tournament> =
        repository.findAllByStartsAtLessThanEqualAndStartProcessedIsFalse(Instant.now())

    @Transactional(readOnly = true)
    fun findAllEndedAndEndNotProcessed(): Set<Tournament> =
        repository.findAllByEndsAtLessThanEqualAndEndProcessedIsFalse(Instant.now())
}
