package com.cleevio.fortraders.application.module.game

import com.cleevio.fortraders.application.common.BaseFinderService
import com.cleevio.fortraders.domain.model.game.Game
import com.cleevio.fortraders.domain.model.game.GameRepository
import com.cleevio.fortraders.domain.model.game.exception.GameNotFoundException
import org.springframework.stereotype.Service

@Service
class GameFinderService(
    gameRepository: GameRepository,
) : BaseFinderService<Game, GameRepository>(
    repository = gameRepository,
    notFoundException = ::GameNotFoundException
)
