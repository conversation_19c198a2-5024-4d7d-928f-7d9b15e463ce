package com.cleevio.fortraders.application.module.instrument.port.output

import com.cleevio.fortraders.application.module.instrument.port.output.model.InstrumentQuotePriceModel
import com.cleevio.fortraders.application.module.instrument.port.output.model.LoadInstrumentPriceModel

interface LoadInstrumentPricesContinuously {
    operator fun invoke(
        forexInstruments: Set<LoadInstrumentPriceModel>,
        cryptoInstruments: Set<LoadInstrumentPriceModel>,
        callback: (InstrumentQuotePriceModel) -> Unit,
    )
}
