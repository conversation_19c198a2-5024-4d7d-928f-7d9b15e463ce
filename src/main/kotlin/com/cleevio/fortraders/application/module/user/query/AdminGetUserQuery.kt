package com.cleevio.fortraders.application.module.user.query

import com.cleevio.fortraders.application.common.dto.AuditRevisionResult
import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.affiliate.constant.AffiliateState
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanCategory
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.emaillog.constant.EmailLogDeliveryStatus
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailType
import com.cleevio.fortraders.domain.model.order.constant.OrderState
import com.cleevio.fortraders.domain.model.payout.constant.PayoutKycState
import com.cleevio.fortraders.domain.model.payout.constant.PayoutState
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.TradingAccountState
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import com.cleevio.fortraders.domain.model.user.constant.PreferredLanguage
import com.cleevio.fortraders.domain.model.user.constant.UserKycState
import com.cleevio.fortraders.domain.model.user.constant.UserRole
import com.cleevio.fortraders.domain.model.user.constant.UserState
import com.cleevio.fortraders.domain.model.usercontract.constant.UserContractState
import com.cleevio.fortraders.domain.model.withdrawalrequest.constant.WithdrawalRequestStatus
import com.cleevio.fortraders.domain.model.withdrawalrequest.constant.WithdrawalRequestType
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class AdminGetUserQuery(
    val userId: UUID,
    val withdrawalRequestStatuses: Set<WithdrawalRequestStatus>?,
    val transactionStatuses: Set<TransactionStatus>?,
    val transactionTypes: Set<TransactionType>?,
    val tradingAccountStates: Set<TradingAccountState>?,
    val tradingAccountChallengeStepTypes: Set<ChallengeStepType>?,
    val orderStates: Set<OrderState>?,
    val omitSystemRevisions: Boolean,
) : Query<AdminGetUserQuery.Result> {

    @Schema(name = "AdminGetUserQueryResult")
    data class Result(
        val id: UUID,
        val role: UserRole?,
        val email: String,
        val isEmailOrderBlacklisted: Boolean,
        val firstName: String?,
        val lastName: String?,
        val state: UserState,
        val kycState: UserKycState?,
        val lastLoginIp: String?,
        val comment: String?,
        val preferredLanguage: PreferredLanguage,
        val createdAt: Instant,
        val updatedAt: Instant,
        val walletBalance: BigDecimal,
        val contact: ContactDetail?,
        val withdrawalRequests: WithdrawalRequestsDetail,
        val transactions: List<TransactionDetail>,
        val affiliate: AffiliateDetail?,
        val tradingAccounts: List<TradingAccountDetail>,
        val approvedPayoutsStats: ApprovedPayoutsStatsDetail,
        val payouts: List<PayoutDetail>,
        val ordersStats: OrdersStatsDetail,
        val orders: List<OrderDetail>,
        val audits: List<AuditRevisionResult>,
        val emails: List<EmailDetail>,
        val contracts: List<ContractDetail>,
        val labels: List<LabelDetail>,
    )

    @Schema(name = "AdminGetUserQueryResultEmailDetail")
    data class EmailDetail(
        val id: UUID,
        val recipientEmail: String,
        val mailType: MailType,
        val subject: String,
        val fromAddress: String,
        val status: EmailLogDeliveryStatus,
        val createdAt: Instant,
    )

    @Schema(name = "AdminGetUserQueryResultContactDetail")
    data class ContactDetail(
        val phonePrefix: String?,
        val phoneNumber: String?,
        val countryName: String,
        val streetAddress: String,
        val city: String,
        val postCode: String,
    )

    @Schema(name = "AdminGetUserQueryResultWithdrawalRequestsDetail")
    data class WithdrawalRequestsDetail(
        val total: BigDecimal,
        val requests: List<WithdrawalRequestDetail>,
    )

    @Schema(name = "AdminGetUserQueryResultWithdrawalRequestDetail")
    data class WithdrawalRequestDetail(
        val id: UUID,
        val transactionId: UUID?,
        val amount: BigDecimal,
        val type: WithdrawalRequestType,
        val status: WithdrawalRequestStatus,
        val createdAt: Instant,
        val updatedAt: Instant,
    )

    @Schema(name = "AdminGetUserQueryResultTransactionDetail")
    data class TransactionDetail(
        val id: UUID,
        val amount: BigDecimal,
        val type: TransactionType,
        val status: TransactionStatus,
        val paymentGatewayProvider: PaymentGatewayProvider?,
        val note: String?,
        val createdAt: Instant,
        val updatedAt: Instant,
    )

    @Schema(name = "AdminGetUserQueryResultAffiliateDetail")
    data class AffiliateDetail(
        val id: UUID,
        val state: AffiliateState,
        val couponCode: String,
        val discountAmountPercentage: Int,
        val commissionPercentage: Int,
        val commissionBalance: BigDecimal,
        val commissionsSum: BigDecimal,
        val note: String?,
        val referralCount: Int,
        val affiliateCommissions: List<AffiliateCommissionDetail>,
        val createdAt: Instant,
        val updatedAt: Instant,
    )

    @Schema(name = "AdminGetUserQueryResultAffiliateCommissionDetail")
    data class AffiliateCommissionDetail(
        val id: UUID,
        val userId: UUID,
        val amount: BigDecimal,
        val createdAt: Instant,
        val order: AffiliateOrderDetail,
    )

    @Schema(name = "AdminGetUserQueryResultAffiliateOrderDetail")
    data class AffiliateOrderDetail(
        val id: UUID,
        val productTitle: String,
        val price: BigDecimal,
    )

    @Schema(name = "AdminGetUserQueryResultTradingAccountDetail")
    data class TradingAccountDetail(
        val id: UUID,
        val accountId: String,
        val orderId: UUID,
        val platform: PlatformType,
        val challengeType: ChallengeType,
        val state: TradingAccountState,
        val plan: String,
        val startingBalance: BigDecimal,
        val currentBalance: BigDecimal,
        val equity: BigDecimal,
        val challengeStepType: ChallengeStepType,
        val createdAt: Instant,
    )

    @Schema(name = "AdminGetUserQueryResultOrdersStatsDetail")
    data class OrdersStatsDetail(
        val count: Int,
        val priceTotal: BigDecimal,
    )

    @Schema(name = "AdminGetUserQueryResultOrderDetail")
    data class OrderDetail(
        val id: UUID,
        val price: BigDecimal,
        val state: OrderState,
        val platform: PlatformType,
        val paidAt: Instant?,
        val challenge: OrderChallengeDetail,
        val challengePlan: OrderChallengePlanDetail,
        val payments: List<OrderPaymentDetail>,
    )

    @Schema(name = "AdminGetUserQueryResultOrderChallengeDetail")
    data class OrderChallengeDetail(
        val id: UUID,
        val name: String,
        val type: ChallengeType,
    )

    @Schema(name = "AdminGetUserQueryResultOrderChallengePlanDetail")
    data class OrderChallengePlanDetail(
        val id: UUID,
        val title: String,
        val category: ChallengePlanCategory,
        val steps: Int,
    )

    @Schema(name = "AdminGetUserQueryResultOrderPaymentDetail")
    data class OrderPaymentDetail(
        val id: UUID,
        val amount: BigDecimal,
        val status: TransactionStatus,
        val type: TransactionType,
        val provider: PaymentGatewayProvider,
        val providerPaymentId: String?,
        val createdAt: Instant,
        val updatedAt: Instant,
    )

    @Schema(name = "AdminGetUserQueryResultApprovedPayoutsStatsDetail")
    data class ApprovedPayoutsStatsDetail(
        val count: Int,
        val amountTotal: BigDecimal,
    )

    @Schema(name = "AdminGetUserQueryResultPayoutDetail")
    data class PayoutDetail(
        val id: UUID,
        val accountId: String,
        val transactionId: UUID?,
        val kycState: PayoutKycState,
        val state: PayoutState,
        val amount: BigDecimal,
        val amountAfterSplit: BigDecimal,
        val bonus: BigDecimal?,
        val amountTotal: BigDecimal,
        val tradingAccountId: UUID,
        val shouldCountdown: Boolean,
        val createdAt: Instant,
        val plan: String,
        val challengeType: ChallengeType,
        val profitSplit: Int,
    )

    @Schema(name = "AdminGetUserQueryResultContractDetail")
    data class ContractDetail(
        val id: UUID,
        val orderId: UUID,
        val orderProductName: String,
        val orderPrice: BigDecimal,
        val submissionId: Long,
        val embedUrl: String,
        val state: UserContractState,
        val pdfFileUrl: String?,
        val createdAt: Instant,
        val updatedAt: Instant,
    )

    @Schema(name = "AdminGetUserQueryResultLabelDetail")
    data class LabelDetail(
        val id: UUID,
        val name: String,
    )
}
