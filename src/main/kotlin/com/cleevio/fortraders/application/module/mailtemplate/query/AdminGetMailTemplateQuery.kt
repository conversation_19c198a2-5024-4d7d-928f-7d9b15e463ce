package com.cleevio.fortraders.application.module.mailtemplate.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailLinks
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailType
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailVariables
import com.cleevio.fortraders.domain.model.user.constant.PreferredLanguage
import io.swagger.v3.oas.annotations.media.Schema

data class AdminGetMailTemplateQuery(
    val type: MailType,
    val language: PreferredLanguage,
) : Query<AdminGetMailTemplateQuery.Result> {

    @Schema(name = "AdminGetMailTemplateResult")
    data class Result(
        val type: MailType,
        val language: PreferredLanguage,
        val subject: String,
        val htmlBody: String,
        val jsonBody: String,
        val variables: List<MailVariables>,
        val links: List<MailLinks>,
    )
}
