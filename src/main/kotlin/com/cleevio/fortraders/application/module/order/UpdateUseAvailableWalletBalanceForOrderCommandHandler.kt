package com.cleevio.fortraders.application.module.order

import com.cleevio.fortraders.application.common.command.CommandHandler
import com.cleevio.fortraders.application.common.constant.ORDER
import com.cleevio.fortraders.application.module.challengeplan.ChallengePlanFinderService
import com.cleevio.fortraders.application.module.order.command.UpdateUseAvailableWalletBalanceForOrderCommand
import com.cleevio.fortraders.application.module.order.constant.UPDATE_ORDER
import com.cleevio.fortraders.application.module.wallet.WalletFinderService
import com.cleevio.fortraders.application.module.withdrawalrequest.WithdrawalRequestFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class UpdateUseAvailableWalletBalanceForOrderCommandHandler(
    private val orderFinderService: OrderFinderService,
    private val walletFinderService: WalletFinderService,
    private val withdrawalRequestFinderService: WithdrawalRequestFinderService,
    private val challengePlanFinderService: ChallengePlanFinderService,
) : CommandHandler<UpdateUseAvailableWalletBalanceForOrderCommand.Result, UpdateUseAvailableWalletBalanceForOrderCommand> {
    override val command = UpdateUseAvailableWalletBalanceForOrderCommand::class

    @Transactional
    @Lock(module = ORDER, lockName = UPDATE_ORDER)
    override fun invoke(
        @LockFieldParameter("orderId") command: UpdateUseAvailableWalletBalanceForOrderCommand,
    ): UpdateUseAvailableWalletBalanceForOrderCommand.Result {
        val order = orderFinderService.getByIdAndUserId(
            id = command.orderId,
            userId = command.userId
        )
        challengePlanFinderService.getById(order.challengePlanId).also { it.verifyIsNotSubscription() }

        val wallet = walletFinderService.getByUserId(userId = command.userId)
        val sumOfPendingWithdrawalRequests = withdrawalRequestFinderService.getSumOfPendingWithdrawalRequestsByUserId(
            userId = command.userId
        )
        order.updateUseMaxAvailableWalletBalance(
            shouldUse = command.shouldUse,
            walletBalance = wallet.balance,
            sumOfPendingWithdrawalRequests = sumOfPendingWithdrawalRequests
        )

        return UpdateUseAvailableWalletBalanceForOrderCommand.Result(
            orderId = order.id,
            price = order.price,
            amountUsed = order.amountUsedFromWallet ?: BigDecimal.ZERO,
            priceAfterWalletUsage = order.calculateFinalPrice()
        )
    }
}
