package com.cleevio.fortraders.application.module.discountcodechallengeplan.event.listener

import com.cleevio.fortraders.application.module.discountcodechallengeplan.DiscountCodeChallengePlanService
import com.cleevio.fortraders.domain.model.challengeplan.event.ChallengePlanCreatedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class ChallengePlanDiscountCodeChallengePlanEventListener(
    private val discountCodeChallengePlanService: DiscountCodeChallengePlanService,
) {
    @EventListener
    fun handleChallengePlanCreatedEvent(event: ChallengePlanCreatedEvent) {
        discountCodeChallengePlanService.applyDiscountCodeToNewChallengePlan(
            newChallengePlanId = event.challengePlanId,
            previousChallengePlanId = event.previousChallengePlanId
        )
    }
}
