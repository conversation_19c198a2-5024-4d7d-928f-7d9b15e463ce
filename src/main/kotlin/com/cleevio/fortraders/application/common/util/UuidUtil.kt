package com.cleevio.fortraders.application.common.util

import java.util.UUID

fun UUID.toAlphaNumeric(): String = toString().replace("-", "")

fun String.toUUID(): UUID = UUID.fromString(this)

fun String.toUUIDOrNull(): UUID? = runCatching {
    UUID.fromString(this)
}.getOrNull()

fun String.isValidUUID(): Boolean = matches(UUID_EXACT_REGEX)

const val UUID_REGEX_PATTERN = "[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}"
val UUID_EXACT_REGEX = "^$UUID_REGEX_PATTERN$".toRegex()
