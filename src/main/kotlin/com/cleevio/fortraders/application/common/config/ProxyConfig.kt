package com.cleevio.fortraders.application.common.config

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.properties.ConfigurationProperties

@ConditionalOnProperty(prefix = "integration.proxy", name = ["host", "port"])
@ConfigurationProperties(prefix = "integration.proxy")
data class ProxyConfig(
    val host: String,
    val port: Int,
)
