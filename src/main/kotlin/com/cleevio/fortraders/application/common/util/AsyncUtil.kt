package com.cleevio.fortraders.application.common.util

import org.springframework.core.task.TaskExecutor
import java.util.concurrent.CompletableFuture

fun <T> Iterable<T>.forEachAsync(taskExecutor: TaskExecutor, action: (T) -> Unit) {
    val futures = this.map {
        CompletableFuture.runAsync({ action(it) }, taskExecutor)
    }
    CompletableFuture.allOf(*futures.toTypedArray()).join()
}

fun <T> Iterable<T>.forEachAsyncBatched(taskExecutor: TaskExecutor, batchSize: Int, action: (T) -> Unit) {
    val futures = this.chunked(batchSize).map {
        CompletableFuture.runAsync({ it.forEach(action) }, taskExecutor)
    }
    CompletableFuture.allOf(*futures.toTypedArray()).join()
}

fun <K, V> Map<out K, V>.forEachAsync(taskExecutor: TaskExecutor, action: (Map.Entry<K, V>) -> Unit) {
    val futures = this.map {
        CompletableFuture.runAsync({ action(it) }, taskExecutor)
    }
    CompletableFuture.allOf(*futures.toTypedArray()).join()
}

fun <T, R> Iterable<T>.mapAsync(taskExecutor: TaskExecutor, action: (T) -> R): List<R> {
    val futures = this.map {
        CompletableFuture.supplyAsync({ action(it) }, taskExecutor)
    }
    CompletableFuture.allOf(*futures.toTypedArray()).join()
    return futures.map { it.get() }
}

fun <T, R> Iterable<T>.flatMapAsync(taskExecutor: TaskExecutor, action: (T) -> Iterable<R>): List<R> {
    val futures = this.map {
        CompletableFuture.supplyAsync({ action(it) }, taskExecutor)
    }
    CompletableFuture.allOf(*futures.toTypedArray()).join()
    return futures.flatMap { it.get() }
}
