package com.cleevio.fortraders.application.common.validation

import com.cleevio.fortraders.application.common.util.isZero
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import java.math.BigDecimal
import kotlin.reflect.KClass

@Constraint(validatedBy = [NotZeroValidator::class])
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class NotZero(
    val message: String = "must not be zero",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

class NotZeroValidator : ConstraintValidator<NotZero, BigDecimal> {
    override fun isValid(value: BigDecimal, context: ConstraintValidatorContext?) = !value.isZero()
}
