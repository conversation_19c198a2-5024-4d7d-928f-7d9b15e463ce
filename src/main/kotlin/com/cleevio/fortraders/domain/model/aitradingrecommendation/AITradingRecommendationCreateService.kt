package com.cleevio.fortraders.domain.model.aitradingrecommendation

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class AITradingRecommendationCreateService(
    private val aiTradingRecommendationRepository: AITradingRecommendationRepository,
) {
    @Transactional
    fun create(tradingAccountId: UUID): AITradingRecommendation = aiTradingRecommendationRepository.save(
        AITradingRecommendation(
            tradingAccountId = tradingAccountId
        )
    )
}
