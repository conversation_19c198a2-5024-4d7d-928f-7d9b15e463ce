package com.cleevio.fortraders.domain.model.tournament.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class TournamentNotFoundException : ForTradersApiException(
    reason = CustomErrorReasonTypes.TOURNAMENT_NOT_FOUND,
    message = "Tournament with given ID not found."
)
