package com.cleevio.fortraders.domain.model.challengeplan

import com.cleevio.fortraders.domain.BaseRepository
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanCategory
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanState
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface ChallengePlanRepository : BaseRepository<ChallengePlan> {

    fun findByIdAndChallengeId(id: UUID, challengeId: UUID): ChallengePlan?

    fun findByChallengeIdAndCategoryAndStepsAndState(
        challengeId: UUID,
        category: ChallengePlanCategory,
        steps: Int,
        state: ChallengePlanState,
    ): ChallengePlan?

    fun existsByChallengeIdAndStepsAndCategoryAndStateIn(
        challengeId: UUID,
        steps: Int,
        category: ChallengePlanCategory,
        states: Set<ChallengePlanState>,
    ): Boolean

    fun existsByChallengeIdAndCategoryAndStepsAndState(
        challengeId: UUID,
        category: ChallengePlanCategory,
        steps: Int,
        state: ChallengePlanState,
    ): Boolean
}
