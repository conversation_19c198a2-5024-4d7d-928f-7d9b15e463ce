package com.cleevio.fortraders.domain.model.aitradingrecommendation

import com.cleevio.fortraders.domain.BaseRepository
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

@Repository
interface AITradingRecommendationRepository : BaseRepository<AITradingRecommendation> {

    fun existsByTradingAccountIdAndCreatedAtAfter(tradingAccountId: UUID, createdAtAfter: Instant): <PERSON><PERSON><PERSON>
}
