package com.cleevio.fortraders.domain.model.challengestepsetting

import com.cleevio.fortraders.domain.UpdatableEntity
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.ChallengeStepSettingType
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.EVALUATION_STEP_ALLOWED_TYPES
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.FUNDED_ONLY_TYPES
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.FUNDED_STEP_ALLOWED_TYPES
import com.cleevio.fortraders.domain.model.challengestepsetting.exception.ChallengeStepSettingTypeNotAllowedForGivenStepException
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.util.UUID

@Entity
class ChallengeStepSetting(
    val challengeStepId: UUID,
    isFirstChallengeStep: Boolean,
    isFundedChallengeStep: Boolean,

    @Enumerated(EnumType.STRING)
    val type: ChallengeStepSettingType,
) : UpdatableEntity() {
    var useMovementsFromOrder: Boolean = shouldForceUseMovementsFromOrder(
        isFirstChallengeStep = isFirstChallengeStep,
        isFundedChallengeStep = isFundedChallengeStep
    )
        private set

    var isVisibleInConfigurator: Boolean = true
        private set

    init {
        verifyTypeValidity(
            isFundedChallengeStep = isFundedChallengeStep
        )
    }

    fun patchAdminSpecifiedProperties(
        isFirstChallengeStep: Boolean,
        isFundedChallengeStep: Boolean,
        useMovementsFromOrder: Boolean?,
        isVisibleInConfigurator: Boolean?,
    ) {
        useMovementsFromOrder?.let {
            this.useMovementsFromOrder = it || shouldForceUseMovementsFromOrder(
                isFirstChallengeStep = isFirstChallengeStep,
                isFundedChallengeStep = isFundedChallengeStep
            )
        }
        isVisibleInConfigurator?.let { this.isVisibleInConfigurator = it }
    }

    private fun shouldForceUseMovementsFromOrder(isFirstChallengeStep: Boolean, isFundedChallengeStep: Boolean): Boolean =
        isFirstChallengeStep || (isFundedChallengeStep && type in FUNDED_ONLY_TYPES)

    private fun verifyTypeValidity(isFundedChallengeStep: Boolean) {
        val allowedTypes = when (isFundedChallengeStep) {
            true -> FUNDED_STEP_ALLOWED_TYPES
            false -> EVALUATION_STEP_ALLOWED_TYPES
        }

        if (type !in allowedTypes) throw ChallengeStepSettingTypeNotAllowedForGivenStepException()
    }
}
