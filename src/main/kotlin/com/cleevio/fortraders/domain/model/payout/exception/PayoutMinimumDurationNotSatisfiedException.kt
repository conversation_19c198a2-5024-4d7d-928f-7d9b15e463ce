package com.cleevio.fortraders.domain.model.payout.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class PayoutMinimumDurationNotSatisfiedException : ForTradersApiException(
    reason = CustomErrorReasonTypes.PAYOUT_MINIMUM_DURATION_NOT_SATISFIED,
    message = "Payout minimum duration not satisfied."
)
