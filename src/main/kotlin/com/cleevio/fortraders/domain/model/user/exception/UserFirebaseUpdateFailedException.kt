package com.cleevio.fortraders.domain.model.user.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class UserFirebaseUpdateFailedException : ForTradersApiException(
    reason = CustomErrorReasonTypes.USER_FIREBASE_UPDATE_FAILED,
    message = "User Firebase update failed."
)
