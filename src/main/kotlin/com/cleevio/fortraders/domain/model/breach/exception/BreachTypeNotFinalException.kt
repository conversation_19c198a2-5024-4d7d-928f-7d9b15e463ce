package com.cleevio.fortraders.domain.model.breach.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class BreachTypeNotFinalException : ForTradersApiException(
    reason = CustomErrorReasonTypes.BREACH_NOT_IN_FINAL_STATE,
    message = "Breach is not in final state."
)
