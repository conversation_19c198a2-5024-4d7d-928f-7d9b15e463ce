package com.cleevio.fortraders.domain.model.challengeplan.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ChallengePlanPlatformsNotSpecifiedException : ForTradersApiException(
    reason = CustomErrorReasonTypes.CHALLENGE_PLAN_PLATFORMS_NOT_SPECIFIED,
    message = "ChallengePlan platforms not specified."
)
