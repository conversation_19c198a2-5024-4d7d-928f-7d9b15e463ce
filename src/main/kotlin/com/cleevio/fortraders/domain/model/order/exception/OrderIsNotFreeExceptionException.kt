package com.cleevio.fortraders.domain.model.order.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class OrderIsNotFreeExceptionException : ForTradersApiException(
    reason = CustomErrorReasonTypes.ORDER_IS_NOT_FREE,
    message = "Order is not free."
)
