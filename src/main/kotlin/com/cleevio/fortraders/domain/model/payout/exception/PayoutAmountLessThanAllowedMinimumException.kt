package com.cleevio.fortraders.domain.model.payout.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class PayoutAmountLessThanAllowedMinimumException : ForTradersApiException(
    reason = CustomErrorReasonTypes.PAYOUT_AMOUNT_LESS_THAN_ALLOWED_MINIMUM,
    message = "Payout amount less than allowed minimum."
)
