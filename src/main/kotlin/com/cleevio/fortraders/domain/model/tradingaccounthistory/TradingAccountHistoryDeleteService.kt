package com.cleevio.fortraders.domain.model.tradingaccounthistory

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.util.UUID

@Service
class TradingAccountHistoryDeleteService(
    private val tradingAccountHistoryRepository: TradingAccountHistoryRepository,
) {
    @Transactional
    fun delete(tradingAccountId: UUID, date: LocalDate) {
        tradingAccountHistoryRepository.deleteByTradingAccountIdAndDateIs(
            tradingAccountId = tradingAccountId,
            date = date
        )
    }
}
