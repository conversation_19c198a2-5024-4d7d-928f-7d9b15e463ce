package com.cleevio.fortraders.domain.model.orderchallengestepsettingmovement

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class OrderChallengeStepSettingMovementCreateService(
    private val orderChallengeStepSettingMovementRepository: OrderChallengeStepSettingMovementRepository,
) {
    @Transactional
    fun createAll(orderId: UUID, challengeStepSettingMovementIds: Set<UUID>): List<OrderChallengeStepSettingMovement> {
        challengeStepSettingMovementIds.ifEmpty { return emptyList() }
        return orderChallengeStepSettingMovementRepository.saveAll(
            challengeStepSettingMovementIds.map {
                OrderChallengeStepSettingMovement(
                    orderId = orderId,
                    challengeStepSettingMovementId = it
                )
            }
        )
    }
}
