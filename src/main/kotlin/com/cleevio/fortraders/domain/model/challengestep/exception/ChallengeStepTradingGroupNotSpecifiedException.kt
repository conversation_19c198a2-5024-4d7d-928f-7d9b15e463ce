package com.cleevio.fortraders.domain.model.challengestep.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ChallengeStepTradingGroupNotSpecifiedException : ForTradersApiException(
    reason = CustomErrorReasonTypes.CHALLENGE_STEP_GROUP_NOT_SPECIFIED,
    message = "ChallengeStep trading group is not specified."
)
