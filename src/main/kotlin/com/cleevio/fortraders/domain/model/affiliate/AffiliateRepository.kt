package com.cleevio.fortraders.domain.model.affiliate

import com.cleevio.fortraders.domain.BaseRepository
import com.cleevio.fortraders.domain.model.affiliate.constant.AffiliateState
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface AffiliateRepository : BaseRepository<Affiliate> {

    fun findByUserId(userId: UUID): Affiliate?

    fun existsByUserId(userId: UUID): Boolean

    fun existsByCouponCode(couponCode: String): Boolean

    fun findByCouponCodeAndState(couponCode: String, state: AffiliateState): Affiliate?
}
