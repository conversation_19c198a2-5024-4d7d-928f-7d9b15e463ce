package com.cleevio.fortraders.domain.model.challengestepsetting

import com.cleevio.fortraders.domain.BaseRepository
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.ChallengeStepSettingType
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface ChallengeStepSettingRepository : BaseRepository<ChallengeStepSetting> {

    fun findByChallengeStepIdAndType(challengeStepId: UUID, type: ChallengeStepSettingType): ChallengeStepSetting?

    fun findByChallengeStepId(challengeStepId: UUID): List<ChallengeStepSetting>

    /**
     * Settings specific for first and last step MUST have default movement
     * Settings in other steps must have either default movement or useMovementsFromOrder = true
     */
    @Query(
        """
        SELECT EXISTS(
            SELECT 1
            FROM ChallengeStepSetting css
            LEFT JOIN ChallengeStepSettingMovement cssm ON css.id = cssm.challengeStepSettingId
                AND cssm.deletedAt IS NULL
                AND cssm.isDefault IS TRUE
            JOIN ChallengeStep cs ON css.challengeStepId = cs.id
            JOIN ChallengePlan cp ON cs.challengePlanId = cp.id
            WHERE cp.id = :challengePlanId
            AND (
                (css.type IN :evaluationStepAllowedTypes AND cs.number = 1)
                OR css.type IN :fundedStepOnlyTypes
            )
            AND cssm.id IS NULL
        )
        OR
        EXISTS(
            SELECT 1
            FROM ChallengeStepSetting css
            LEFT JOIN ChallengeStepSettingMovement cssm ON css.id = cssm.challengeStepSettingId
                AND cssm.deletedAt IS NULL
                AND cssm.isDefault IS TRUE
            JOIN ChallengeStep cs ON css.challengeStepId = cs.id
            JOIN ChallengePlan cp ON cs.challengePlanId = cp.id
            WHERE cp.id = :challengePlanId
            AND css.useMovementsFromOrder IS FALSE
            AND cssm.id IS NULL
        )
        """
    )
    fun existsSettingWithoutDefaultNonDeletedMovement(
        challengePlanId: UUID,
        evaluationStepAllowedTypes: Set<ChallengeStepSettingType>,
        fundedStepOnlyTypes: Set<ChallengeStepSettingType>,
    ): Boolean
}
