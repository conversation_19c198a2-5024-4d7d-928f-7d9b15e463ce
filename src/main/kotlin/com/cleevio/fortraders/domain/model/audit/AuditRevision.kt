package com.cleevio.fortraders.domain.model.audit

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EntityListeners
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.envers.RevisionEntity
import org.hibernate.envers.RevisionNumber
import org.hibernate.envers.RevisionTimestamp
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.Instant
import java.util.UUID

@Entity
@Table(schema = "audit", name = "revision")
@RevisionEntity
@EntityListeners(AuditingEntityListener::class)
class AuditRevision {
    @Id
    @Column(updatable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @RevisionNumber
    val id: Long = -1

    @RevisionTimestamp
    @Column(updatable = false, nullable = false)
    val createdAt: Instant = Instant.now()

    @CreatedBy
    @Column(updatable = false, nullable = true)
    var createdBy: UUID? = null
        private set
}
