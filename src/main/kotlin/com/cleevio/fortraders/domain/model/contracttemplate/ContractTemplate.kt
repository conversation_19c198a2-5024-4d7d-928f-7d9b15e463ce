package com.cleevio.fortraders.domain.model.contracttemplate

import com.cleevio.fortraders.domain.UpdatableEntity
import com.cleevio.fortraders.domain.model.contracttemplate.constant.ContractTemplateVariable
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated

@Entity
class ContractTemplate(
    name: String,
    externalId: Long,
    supportedVariables: Set<ContractTemplateVariable>,
) : UpdatableEntity() {
    var name: String = name
        private set

    var externalId: Long = externalId
        private set

    @Enumerated(EnumType.STRING)
    var supportedVariables: Set<ContractTemplateVariable> = supportedVariables
        private set

    fun update(name: String, externalId: Long, supportedVariables: Set<ContractTemplateVariable>) {
        this.name = name
        this.externalId = externalId
        this.supportedVariables = supportedVariables
    }
}
