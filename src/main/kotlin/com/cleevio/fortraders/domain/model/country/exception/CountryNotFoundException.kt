package com.cleevio.fortraders.domain.model.country.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class CountryNotFoundException : ForTradersApiException(
    reason = CustomErrorReasonTypes.COUNTRY_NOT_FOUND,
    message = "Country not found."
)
