package com.cleevio.fortraders.domain.model.challengestepsetting.event.listener

import com.cleevio.fortraders.domain.model.challengestep.event.ChallengeStepCreatedEvent
import com.cleevio.fortraders.domain.model.challengestepsetting.ChallengeStepSettingCreateService
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class ChallengeStepChallengeStepSettingEventListener(
    private val challengeStepSettingCreateService: ChallengeStepSettingCreateService,
) {
    @EventListener
    fun handleChallengeStepCreatedEvent(event: ChallengeStepCreatedEvent) {
        challengeStepSettingCreateService.createAllSettingsForStep(
            challengeStepId = event.id,
            isFirstChallengeStep = event.isFirstStep,
            isFundedChallengeStep = event.isFundedStep
        )
    }
}
