package com.cleevio.fortraders.domain.model.affiliate.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.CONFLICT)
class AffiliateWithGivenCouponCodeAlreadyExistsException : ForTradersApiException(
    reason = CustomErrorReasonTypes.AFFILIATE_WITH_COUPON_CODE_ALREADY_EXISTS,
    message = "Affiliate with given coupon code already exists."
)
