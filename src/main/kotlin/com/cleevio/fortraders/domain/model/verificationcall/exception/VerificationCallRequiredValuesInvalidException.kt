package com.cleevio.fortraders.domain.model.verificationcall.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class VerificationCallRequiredValuesInvalidException : ForTradersApiException(
    reason = CustomErrorReasonTypes.VERIFICATION_CALL_REQUIRED_VALUES_INVALID,
    message = "VerificationCall required values are invalid."
)
