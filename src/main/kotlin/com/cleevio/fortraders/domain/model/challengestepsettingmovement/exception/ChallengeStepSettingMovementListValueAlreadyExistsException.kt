package com.cleevio.fortraders.domain.model.challengestepsettingmovement.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.CONFLICT)
class ChallengeStepSettingMovementListValueAlreadyExistsException : ForTradersApiException(
    reason = CustomErrorReasonTypes.CHALLENGE_STEP_SETTING_MOVEMENT_LIST_VALUE_ALREADY_EXISTS,
    message = "ChallengeStepSettingMovement with given list value already exists."
)
