package com.cleevio.fortraders.domain.model.challengestep

import com.cleevio.fortraders.domain.BaseRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface ChallengeStepRepository : BaseRepository<ChallengeStep> {

    fun findByChallengePlanIdAndNumber(challengePlanId: UUID, number: Int): ChallengeStep?

    fun findByChallengePlanId(challengePlanId: UUID): List<ChallengeStep>
}
