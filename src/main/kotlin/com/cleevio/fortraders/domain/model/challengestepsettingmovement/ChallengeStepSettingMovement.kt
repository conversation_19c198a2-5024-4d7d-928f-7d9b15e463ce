package com.cleevio.fortraders.domain.model.challengestepsettingmovement

import com.cleevio.fortraders.application.common.util.percentageToDecimal
import com.cleevio.fortraders.application.common.util.roundUp
import com.cleevio.fortraders.domain.DeletableEntity
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.ChallengeStepSettingType
import com.cleevio.fortraders.domain.model.challengestepsettingmovement.constant.ChallengeStepSettingMovementType
import com.cleevio.fortraders.domain.model.challengestepsettingmovement.exception.ChallengeStepSettingMovementTypeNotAllowedForGivenSettingException
import com.cleevio.fortraders.domain.model.challengestepsettingmovement.exception.ChallengeStepSettingMovementTypeRequiredValuesInvalidException
import com.cleevio.fortraders.domain.model.order.constant.PayoutsType
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.math.BigDecimal
import java.util.UUID

@Entity
class ChallengeStepSettingMovement(
    val challengeStepSettingId: UUID,
    challengePlanBasePrice: BigDecimal,
    type: ChallengeStepSettingMovementType,
    settingType: ChallengeStepSettingType,
    percentageValue: Int?,
    listValue: String?,
    movementPercentageValue: Int?,
    movementAbsoluteValue: BigDecimal?,
    isDefault: Boolean,
) : DeletableEntity() {
    @Enumerated(EnumType.STRING)
    var type: ChallengeStepSettingMovementType = type
        private set

    var percentageValue: Int? = percentageValue
        private set

    var listValue: String? = listValue
        private set

    var movementPercentageValue: Int? = movementPercentageValue
        private set

    var movementAbsoluteValue: BigDecimal = calculateMovementAbsoluteValue(
        movementPercentageValue = movementPercentageValue,
        movementAbsoluteValue = movementAbsoluteValue,
        challengePlanBasePrice = challengePlanBasePrice
    )
        private set

    var isDefault: Boolean = isDefault
        private set

    init {
        verifyValidityOfProperties(settingType)
    }

    fun disableDefault() {
        isDefault = false
    }

    fun updateProperties(
        challengePlanBasePrice: BigDecimal,
        type: ChallengeStepSettingMovementType,
        settingType: ChallengeStepSettingType,
        percentageValue: Int?,
        listValue: String?,
        movementPercentageValue: Int?,
        movementAbsoluteValue: BigDecimal?,
        isDefault: Boolean,
    ) {
        this.type = type
        this.isDefault = isDefault
        when (type) {
            ChallengeStepSettingMovementType.PERCENTAGE -> {
                this.listValue = null
                this.percentageValue = percentageValue
            }

            ChallengeStepSettingMovementType.LIST -> {
                this.listValue = listValue
                this.percentageValue = null
            }

            ChallengeStepSettingMovementType.NONE -> {
                this.listValue = null
                this.percentageValue = null
            }
        }
        updateMovementValues(
            movementPercentageValue = movementPercentageValue,
            movementAbsoluteValue = movementAbsoluteValue,
            challengePlanBasePrice = challengePlanBasePrice
        )
        verifyValidityOfProperties(settingType)
    }

    private fun verifyValidityOfProperties(settingType: ChallengeStepSettingType) {
        val allowedMovementTypes = when (settingType) {
            ChallengeStepSettingType.PROFIT_TARGET,
            ChallengeStepSettingType.MAX_DRAWDOWN,
            ChallengeStepSettingType.PROFIT_SPLIT,
            -> setOf(ChallengeStepSettingMovementType.PERCENTAGE)

            ChallengeStepSettingType.DAILY_DRAWDOWN,
            ChallengeStepSettingType.DAILY_PAUSE,
            ChallengeStepSettingType.DAILY_PROFIT_CAP,
            ChallengeStepSettingType.REFUND,
            -> setOf(
                ChallengeStepSettingMovementType.PERCENTAGE,
                ChallengeStepSettingMovementType.NONE
            )

            ChallengeStepSettingType.PAYOUTS -> setOf(
                ChallengeStepSettingMovementType.LIST
            )
        }
        if (type !in allowedMovementTypes) {
            throw ChallengeStepSettingMovementTypeNotAllowedForGivenSettingException()
        }

        when (type) {
            ChallengeStepSettingMovementType.PERCENTAGE -> listOf(
                listValue == null,
                percentageValue != null
            )

            ChallengeStepSettingMovementType.LIST -> listOf(
                listValue != null,
                percentageValue == null
            )

            ChallengeStepSettingMovementType.NONE -> listOf(
                listValue == null,
                percentageValue == null
            )
        }.also { conditions ->
            val movementPercentageValueValid = movementPercentageValue?.let { it in (-99..99) } ?: true
            val listValueValid = when (settingType) {
                ChallengeStepSettingType.PAYOUTS -> listValue in PayoutsType.entries.map { it.name }
                else -> true
            }
            if (conditions.any { !it } || !movementPercentageValueValid || !listValueValid) {
                throw ChallengeStepSettingMovementTypeRequiredValuesInvalidException()
            }
        }
    }

    private fun updateMovementValues(
        movementPercentageValue: Int?,
        movementAbsoluteValue: BigDecimal?,
        challengePlanBasePrice: BigDecimal,
    ) {
        this.movementPercentageValue = movementPercentageValue
        this.movementAbsoluteValue = calculateMovementAbsoluteValue(
            movementPercentageValue = movementPercentageValue,
            movementAbsoluteValue = movementAbsoluteValue,
            challengePlanBasePrice = challengePlanBasePrice
        )
    }

    private fun calculateMovementAbsoluteValue(
        movementPercentageValue: Int?,
        movementAbsoluteValue: BigDecimal?,
        challengePlanBasePrice: BigDecimal,
    ): BigDecimal = movementPercentageValue?.let { (challengePlanBasePrice * it.percentageToDecimal()).roundUp() }
        ?: movementAbsoluteValue
        ?: throw ChallengeStepSettingMovementTypeRequiredValuesInvalidException()
}
