package com.cleevio.fortraders.domain.model.tradingaccounthistorycalculationtime

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Service
class TradingAccountHistoryCalculationTimeCreateService(
    private val tradingAccountHistoryCalculationTimeRepository: TradingAccountHistoryCalculationTimeRepository,
) {
    @Transactional
    fun create(calculatedAt: Instant): TradingAccountHistoryCalculationTime = tradingAccountHistoryCalculationTimeRepository.save(
        TradingAccountHistoryCalculationTime(
            calculatedAt = calculatedAt
        )
    )
}
