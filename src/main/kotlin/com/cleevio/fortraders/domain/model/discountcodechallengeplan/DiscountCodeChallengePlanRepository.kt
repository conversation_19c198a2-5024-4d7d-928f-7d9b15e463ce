package com.cleevio.fortraders.domain.model.discountcodechallengeplan

import com.cleevio.fortraders.domain.BaseRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface DiscountCodeChallengePlanRepository : BaseRepository<DiscountCodeChallengePlan> {

    fun deleteAllByDiscountCodeId(discountCodeId: UUID)

    /**
     * If discount code is not assigned to any challenge plan, it is considered as assigned to all challenge plans.
     */
    @Query(
        """
        SELECT
        CASE
            WHEN EXISTS (
                SELECT 1
                FROM DiscountCodeChallengePlan dcchp
                WHERE dcchp.discountCodeId = :discountCodeId
            ) THEN EXISTS (
                SELECT 1
                FROM DiscountCodeChallengePlan dcchp
                WHERE dcchp.discountCodeId = :discountCodeId
                AND dcchp.challengePlanId = :challengePlanId
            )
            ELSE true
        END
        """
    )
    fun isDiscountCodeIdApplicableToChallengePlanId(discountCodeId: UUID, challengePlanId: UUID): Boolean

    @Query(
        """
        SELECT dcchp.discountCodeId
        FROM DiscountCodeChallengePlan dcchp
        WHERE dcchp.challengePlanId = :challengePlanId
        """
    )
    fun findDiscountCodeIdsByChallengePlanId(challengePlanId: UUID): Set<UUID>
}
