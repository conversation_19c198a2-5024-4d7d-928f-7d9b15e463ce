package com.cleevio.fortraders.domain.model.user

import com.cleevio.fortraders.application.common.util.sanitizeEmail
import com.cleevio.fortraders.domain.UpdatableEntity
import com.cleevio.fortraders.domain.model.user.constant.PreferredLanguage
import com.cleevio.fortraders.domain.model.user.constant.UserKycState
import com.cleevio.fortraders.domain.model.user.constant.UserRole
import com.cleevio.fortraders.domain.model.user.constant.UserState
import com.cleevio.fortraders.domain.model.user.exception.UserKycVerificationAlreadyFinishedException
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import org.hibernate.envers.Audited
import java.time.Instant
import java.util.Optional
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

@Entity
@Table(name = "\"user\"")
class User(
    email: String,
    val firebaseId: String,
    role: UserRole?,
    firstName: String?,
    lastName: String?,
    profileImageFileId: UUID?,
) : UpdatableEntity() {
    @Audited
    var email: String = email.sanitizeEmail()
        private set

    @Audited
    @Enumerated(EnumType.STRING)
    var role: UserRole? = role
        private set

    @Audited
    var firstName: String? = firstName
        private set

    @Audited
    var lastName: String? = lastName
        private set

    var profileImageFileId: UUID? = profileImageFileId
        private set

    @Audited
    @Enumerated(EnumType.STRING)
    var kycState: UserKycState = UserKycState.NOT_REQUESTED
        private set

    @Audited
    @Enumerated(EnumType.STRING)
    var state: UserState = UserState.ENABLED
        private set

    var referrerAffiliateId: UUID? = null
        private set

    @Audited
    var comment: String? = null
        private set

    @Audited
    var lastPasswordUpdateByAdmin: Instant? = null
        private set

    var feedOnboardingUrl: String? = null
        private set

    var lastLoginIp: String? = null
        private set

    @Enumerated(EnumType.STRING)
    var preferredLanguage: PreferredLanguage = PreferredLanguage.ENGLISH
        private set

    @Audited
    var labels: Set<UUID> = emptySet()
        private set

    fun patchAdminSpecifiedProperties(
        role: Optional<UserRole>?,
        kycState: UserKycState?,
        state: UserState?,
        comment: Optional<String>?,
        password: String?,
        labels: Set<UUID>?,
    ) {
        role?.let { this.role = it.getOrNull() }
        kycState?.let { this.kycState = it }
        state?.let { this.state = it }
        comment?.let { this.comment = it.getOrNull() }
        password?.let { this.lastPasswordUpdateByAdmin = Instant.now() }
        labels?.let { this.labels = it }
    }

    fun updateFullName(firstName: String, lastName: String) {
        this.firstName = firstName
        this.lastName = lastName
    }

    fun updateLastLoginIp(ip: String) {
        this.lastLoginIp = ip
    }

    fun getFullName(): String = getFullName(
        firstName = firstName,
        lastName = lastName
    )

    fun getAnonymizedName(): String = if (firstName.isNullOrBlank() && lastName.isNullOrBlank()) {
        error("User has null name.")
    } else {
        "${firstName ?: ""} ${lastName?.firstOrNull()?.plus(".") ?: ""}".trim()
    }

    fun updateProfileImage(profileImageFileId: UUID?) {
        this.profileImageFileId = profileImageFileId
    }

    fun requestKycVerification() {
        when (kycState) {
            UserKycState.PENDING -> return
            UserKycState.APPROVED, UserKycState.FAILED -> throw UserKycVerificationAlreadyFinishedException(kycState)
            UserKycState.NOT_REQUESTED -> {}
        }
        kycState = UserKycState.PENDING
    }

    fun setIsKycVerified(isVerified: Boolean) {
        kycState = if (isVerified) UserKycState.APPROVED else UserKycState.FAILED
    }

    fun resetKycVerification() {
        kycState = UserKycState.NOT_REQUESTED
    }

    fun assignReferrerAffiliate(affiliateId: UUID) {
        require(this.referrerAffiliateId == null) { "User already has a referrer affiliate." }
        this.referrerAffiliateId = affiliateId
    }

    fun setFeedOnboardingUrl(feedOnboardingUrl: String) {
        this.feedOnboardingUrl = feedOnboardingUrl
    }

    fun clearFeedOnboardingUrl() {
        this.feedOnboardingUrl = null
    }

    fun updatePreferredLanguage(preferredLanguage: PreferredLanguage) {
        this.preferredLanguage = preferredLanguage
    }

    fun updateEmail(email: String) {
        this.email = email.sanitizeEmail()
    }
}

fun getFullName(firstName: String?, lastName: String?) = if (firstName.isNullOrBlank() && lastName.isNullOrBlank()) {
    error("User has null name.")
} else {
    "${firstName ?: ""} ${lastName ?: ""}".trim()
}
