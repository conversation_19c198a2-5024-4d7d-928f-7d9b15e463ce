package com.cleevio.fortraders.domain.model.affiliatecommission

import com.cleevio.fortraders.domain.model.affiliatecommission.event.AffiliateCommissionCreatedEvent
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.util.UUID

@Service
class AffiliateCommissionCreateService(
    private val affiliateCommissionRepository: AffiliateCommissionRepository,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    @Transactional
    fun create(userId: UUID, affiliateId: UUID, orderId: UUID, amount: BigDecimal): AffiliateCommission =
        affiliateCommissionRepository.save(
            AffiliateCommission(
                affiliateId = affiliateId,
                orderId = orderId,
                amount = amount
            )
        ).also {
            applicationEventPublisher.publishEvent(
                AffiliateCommissionCreatedEvent(
                    affiliateCommissionId = it.id,
                    affiliateId = it.affiliateId,
                    userId = userId,
                    amount = it.amount
                )
            )
        }
}
