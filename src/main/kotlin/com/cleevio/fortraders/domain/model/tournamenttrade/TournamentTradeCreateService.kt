package com.cleevio.fortraders.domain.model.tournamenttrade

import com.cleevio.fortraders.domain.model.tournamenttrade.constant.TournamentTradeType
import com.cleevio.fortraders.domain.model.tournamenttrade.event.TournamentTradeCreatedEvent
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.util.UUID

@Service
class TournamentTradeCreateService(
    private val tournamentTradeRepository: TournamentTradeRepository,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {

    @Transactional
    fun create(
        tournamentId: UUID,
        userId: UUID,
        instrumentId: UUID,
        type: TournamentTradeType,
        leverage: Int,
        tournamentLeverageEnabled: Boolean,
        askPrice: BigDecimal,
        bidPrice: BigDecimal,
        stopLoss: BigDecimal?,
        takeProfit: BigDecimal?,
    ): TournamentTrade = tournamentTradeRepository.save(
        TournamentTrade(
            tournamentId = tournamentId,
            userId = userId,
            instrumentId = instrumentId,
            type = type,
            leverage = leverage,
            tournamentLeverageEnabled = tournamentLeverageEnabled,
            askPrice = askPrice,
            bidPrice = bidPrice,
            stopLoss = stopLoss,
            takeProfit = takeProfit
        )
    ).also {
        applicationEventPublisher.publishEvent(
            TournamentTradeCreatedEvent(tournamentTradeId = it.id)
        )
    }
}
