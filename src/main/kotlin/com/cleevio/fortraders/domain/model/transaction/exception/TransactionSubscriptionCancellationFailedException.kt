package com.cleevio.fortraders.domain.model.transaction.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class TransactionSubscriptionCancellationFailedException : ForTradersApiException(
    reason = CustomErrorReasonTypes.TRANSACTION_SUBSCRIPTION_CANCELLATION_FAILED,
    message = "Transaction subscription cancellation failed."
)
