package com.cleevio.fortraders.domain.model.affiliatepayout.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class AffiliatePayoutStateChangeNotAllowedException : ForTradersApiException(
    reason = CustomErrorReasonTypes.AFFILIATE_PAYOUT_STATE_CHANGE_NOT_ALLOWED,
    message = "AffiliatePayout state change is not allowed."
)
