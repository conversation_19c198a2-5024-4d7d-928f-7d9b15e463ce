package com.cleevio.fortraders.domain.model.tradingaccountpause.event

import com.cleevio.fortraders.domain.model.tradingaccountpause.constant.TradingAccountPauseType
import java.math.BigDecimal
import java.util.UUID

data class TradingAccountPauseCreatedEvent(
    val tradingAccountId: UUID,
    val pauseType: TradingAccountPauseType,
    val accountBalance: BigDecimal,
    val accountEquity: BigDecimal,
)
