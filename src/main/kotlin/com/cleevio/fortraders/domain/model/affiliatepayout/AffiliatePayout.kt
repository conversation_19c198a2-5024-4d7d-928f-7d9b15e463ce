package com.cleevio.fortraders.domain.model.affiliatepayout

import com.cleevio.fortraders.domain.UpdatableEntity
import com.cleevio.fortraders.domain.model.affiliatepayout.constant.AffiliatePayoutState
import com.cleevio.fortraders.domain.model.affiliatepayout.exception.AffiliatePayoutAmountLessThanAllowedMinimumException
import com.cleevio.fortraders.domain.model.affiliatepayout.exception.AffiliatePayoutMinimumDurationNotSatisfiedException
import com.cleevio.fortraders.domain.model.affiliatepayout.exception.AffiliatePayoutStateChangeNotAllowedException
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

@Entity
class AffiliatePayout(
    val affiliateId: UUID,
    minimalPayoutAmount: BigDecimal,
    val amount: BigDecimal,
    nextAffiliatePayoutAvailableAt: Instant,
) : UpdatableEntity() {
    init {
        if (amount < minimalPayoutAmount) throw AffiliatePayoutAmountLessThanAllowedMinimumException()
        if (nextAffiliatePayoutAvailableAt > Instant.now()) throw AffiliatePayoutMinimumDurationNotSatisfiedException()
    }

    var transactionId: UUID? = null
        private set

    @Enumerated(EnumType.STRING)
    var state: AffiliatePayoutState = AffiliatePayoutState.REQUESTED
        private set

    var internalNote: String? = null
        private set

    var externalNote: String? = null
        private set

    fun approve(transactionId: UUID, internalNote: String?, externalNote: String?) {
        if (state != AffiliatePayoutState.REQUESTED) throw AffiliatePayoutStateChangeNotAllowedException()

        this.state = AffiliatePayoutState.APPROVED
        this.transactionId = transactionId
        this.internalNote = internalNote
        this.externalNote = externalNote
    }

    fun decline(internalNote: String?, externalNote: String?) {
        if (state != AffiliatePayoutState.REQUESTED) throw AffiliatePayoutStateChangeNotAllowedException()

        this.state = AffiliatePayoutState.DECLINED
        this.internalNote = internalNote
        this.externalNote = externalNote
    }
}
