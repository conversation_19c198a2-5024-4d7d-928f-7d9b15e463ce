package com.cleevio.fortraders.infrastructure.security

import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.web.util.matcher.RequestMatcher
import org.springframework.web.filter.OncePerRequestFilter
import org.springframework.web.util.ContentCachingRequestWrapper
import org.springframework.web.util.ContentCachingResponseWrapper

class LoggingFilter(
    private val requestMatcher: RequestMatcher,
) : OncePerRequestFilter() {

    override fun shouldNotFilter(request: HttpServletRequest): Boolean {
        return !requestMatcher.matches(request)
    }

    override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain) {
        val wrappedRequest = ContentCachingRequestWrapper(request)
        val wrappedResponse = ContentCachingResponseWrapper(response)

        filterChain.doFilter(wrappedRequest, wrappedResponse)

        val requestBody = getTruncatedBody(wrappedRequest.contentAsByteArray)
        val responseBody = getTruncatedBody(wrappedResponse.contentAsByteArray)

        logger.info("Request: ${wrappedRequest.method} ${wrappedRequest.requestURI}: $requestBody")
        logger.info("Response: ${wrappedRequest.method} ${wrappedRequest.requestURI} ${wrappedResponse.status}: $responseBody")

        wrappedResponse.copyBodyToResponse()
    }
}

private fun String.truncate(limit: Int): String = if (length < limit) this else (take(limit) + "...")

private fun String.toSingleLine(): String = this.replace(NEWLINES_REGEX, " ").trim()

private fun getTruncatedBody(body: ByteArray): String {
    if (body.isEmpty()) return "null"

    return body.toString(Charsets.UTF_8).truncate(10000).toSingleLine()
}

private val NEWLINES_REGEX = Regex("\\s*\n\\s*")
