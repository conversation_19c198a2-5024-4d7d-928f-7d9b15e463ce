package com.cleevio.fortraders.infrastructure.security

import com.cleevio.fortraders.application.common.util.createHmacSha256Instance
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import org.springframework.util.StreamUtils
import org.springframework.web.filter.OncePerRequestFilter
import javax.crypto.Mac

class HmacSignatureFilter(
    private val requestMatcher: AntPathRequestMatcher,
    secretKey: String,
    private val signatureHeaderName: String,
    private val generateHmacSignature: Mac.(ByteArray) -> String,
) : OncePerRequestFilter() {
    private val macInstance = createHmacSha256Instance(secretKey)

    override fun shouldNotFilter(request: HttpServletRequest): Boolean {
        return !requestMatcher.matches(request)
    }

    override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain) {
        val signatureHeaderValue = request.getHeader(signatureHeaderName)
        val cachedRequest = CachedBodyHttpServletRequestWrapper(request)
        val requestBody = StreamUtils.copyToByteArray(cachedRequest.getInputStream())

        if (macInstance.generateHmacSignature(requestBody) != signatureHeaderValue) {
            response.status = HttpServletResponse.SC_UNAUTHORIZED
            return
        }
        filterChain.doFilter(cachedRequest, response)
    }
}
