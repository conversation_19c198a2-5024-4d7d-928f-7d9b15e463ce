package com.cleevio.fortraders.infrastructure.config

import com.cleevio.fortraders.application.common.util.UUID_REGEX_PATTERN
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.infrastructure.exception.AccessDeniedException
import com.cleevio.fortraders.infrastructure.security.toJwtUserAuthToken
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders
import org.springframework.messaging.Message
import org.springframework.messaging.MessageChannel
import org.springframework.messaging.converter.MappingJackson2MessageConverter
import org.springframework.messaging.converter.MessageConverter
import org.springframework.messaging.simp.config.ChannelRegistration
import org.springframework.messaging.simp.config.MessageBrokerRegistry
import org.springframework.messaging.simp.stomp.StompCommand
import org.springframework.messaging.simp.stomp.StompHeaderAccessor
import org.springframework.messaging.support.ChannelInterceptor
import org.springframework.messaging.support.MessageHeaderAccessor
import org.springframework.security.oauth2.jwt.JwtDecoder
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker
import org.springframework.web.socket.config.annotation.StompEndpointRegistry
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer
import java.util.UUID

@Configuration
@EnableWebSocketMessageBroker
class WebSocketConfig(
    private val mappingJackson2MessageConverter: MappingJackson2MessageConverter,
    private val jwtDecoder: JwtDecoder,
) : WebSocketMessageBrokerConfigurer {

    override fun registerStompEndpoints(registry: StompEndpointRegistry) {
        registry.addEndpoint("/ws")
            .setAllowedOriginPatterns("*")
    }

    override fun configureMessageBroker(registry: MessageBrokerRegistry) {
        registry.enableSimpleBroker("/topic", "/queue", "/user")
        registry.setApplicationDestinationPrefixes("/app")
        registry.setUserDestinationPrefix("/user")
    }

    override fun configureClientInboundChannel(registration: ChannelRegistration) {
        registration.interceptors(object : ChannelInterceptor {
            override fun preSend(message: Message<*>, channel: MessageChannel): Message<*> {
                MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor::class.java)?.let {
                    if (it.command == StompCommand.DISCONNECT) return message

                    val jwt = it.getAuthorizationHeader() ?: throw AccessDeniedException()
                    val jwtUserAuthToken = jwtDecoder.decode(jwt).toJwtUserAuthToken()
                    it.user = jwtUserAuthToken

                    // verify that the user is not trying to access another user's destination
                    it.destination?.let { destination ->
                        if (destination.isStompUserPath() && destination.parseStompUserId() != jwtUserAuthToken.principal) {
                            throw AccessDeniedException()
                        }
                    }
                }
                return message
            }
        })
    }

    override fun configureMessageConverters(messageConverters: MutableList<MessageConverter>): Boolean {
        messageConverters.add(mappingJackson2MessageConverter)
        return false
    }
}

private fun StompHeaderAccessor.getAuthorizationHeader(): String? =
    getFirstNativeHeader(HttpHeaders.AUTHORIZATION)?.substring("Bearer ".length)

private fun String.isStompUserPath() = this.startsWith("/user")

private fun String.parseStompUserId(): UUID? = STOMP_USER_PATH_REGEX.find(this)?.groupValues?.getOrNull(1)?.toUUID()

private val STOMP_USER_PATH_REGEX = Regex("/user/($UUID_REGEX_PATTERN)/")
