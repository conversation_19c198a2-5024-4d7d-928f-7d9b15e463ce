package com.cleevio.fortraders.infrastructure.config

import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.stereotype.Component

@Aspect
@Component
class LoggingAspect {

    private val logger = logger()

    @Around("@annotation(com.cleevio.fortraders.infrastructure.config.Loggable)")
    fun logMethodExecution(joinPoint: ProceedingJoinPoint): Any? {
        val methodSignature = joinPoint.signature as MethodSignature
        val methodName = methodSignature.method.name
        val className = methodSignature.declaringType.simpleName
        val returnType = methodSignature.returnType.name

        logger.info("Entering method: $className.$methodName with arguments: ${joinPoint.args.joinToString()}")

        return try {
            val result = joinPoint.proceed()
            if (returnType == "void" || returnType == "kotlin.Unit") {
                logger.info("Method: $className.$methodName completed without a return value.")
            } else {
                logger.info("Method: $className.$methodName completed with result: $result")
            }
            result
        } catch (e: Throwable) {
            logger.error("Exception in $className.$methodName with cause: ${e.cause}")
            throw e
        }
    }
}

@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class Loggable
