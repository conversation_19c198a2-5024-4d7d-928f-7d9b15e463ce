package com.cleevio.fortraders.infrastructure.exception

import com.cleevio.library.exceptionhandler.service.model.ErrorCode
import com.cleevio.library.exceptionhandler.service.model.ErrorReason
import com.cleevio.library.exceptionhandler.service.model.ErrorReasonTypes

object CustomErrorReasonTypes : ErrorReasonTypes() {
    val USER_NOT_FOUND = ExtendedErrorReasonType.USER_NOT_FOUND
    val USER_EMAIL_ALREADY_EXISTS = ExtendedErrorReasonType.USER_EMAIL_ALREADY_EXISTS
    val USER_FIREBASE_UPDATE_FAILED = ExtendedErrorReasonType.USER_FIREBASE_UPDATE_FAILED
    val USER_KYC_VERIFICATION_ALREADY_FINISHED = ExtendedErrorReasonType.USER_KYC_VERIFICATION_ALREADY_FINISHED
    val CONTACT_NOT_FOUND = ExtendedErrorReasonType.CONTACT_NOT_FOUND
    val CONTACT_PHONE_NUMBER_INVALID = ExtendedErrorReasonType.CONTACT_PHONE_NUMBER_INVALID
    val FILE_NOT_FOUND = ExtendedErrorReasonType.FILE_NOT_FOUND
    val FILE_NOT_DELETED = ExtendedErrorReasonType.FILE_NOT_DELETED
    val WALLET_NOT_FOUND = ExtendedErrorReasonType.WALLET_NOT_FOUND
    val WALLET_BALANCE_NOT_ENOUGH = ExtendedErrorReasonType.WALLET_BALANCE_NOT_ENOUGH
    val CHALLENGE_NOT_FOUND = ExtendedErrorReasonType.CHALLENGE_NOT_FOUND
    val CHALLENGE_WITH_STARTING_BALANCE_AND_TYPE_ALREADY_EXISTS =
        ExtendedErrorReasonType.CHALLENGE_WITH_STARTING_BALANCE_AND_TYPE_ALREADY_EXISTS
    val CHALLENGE_PLAN_NOT_FOUND = ExtendedErrorReasonType.CHALLENGE_PLAN_NOT_FOUND
    val CHALLENGE_PLAN_NOT_DRAFT = ExtendedErrorReasonType.CHALLENGE_PLAN_NOT_DRAFT
    val CHALLENGE_PLAN_ALREADY_ARCHIVED = ExtendedErrorReasonType.CHALLENGE_PLAN_ALREADY_ARCHIVED
    val CHALLENGE_PLAN_ALREADY_EXISTS = ExtendedErrorReasonType.CHALLENGE_PLAN_ALREADY_EXISTS
    val CHALLENGE_PLAN_HAS_SUBSCRIPTION_PRICING = ExtendedErrorReasonType.CHALLENGE_PLAN_HAS_SUBSCRIPTION_PRICING
    val CHALLENGE_PLAN_PLATFORMS_NOT_SPECIFIED = ExtendedErrorReasonType.CHALLENGE_PLAN_PLATFORMS_NOT_SPECIFIED
    val CHALLENGE_STEP_NOT_FOUND = ExtendedErrorReasonType.CHALLENGE_STEP_NOT_FOUND
    val CHALLENGE_STEP_NOT_FUNDED = ExtendedErrorReasonType.CHALLENGE_STEP_NOT_FUNDED
    val CHALLENGE_STEP_SETTING_NOT_FOUND = ExtendedErrorReasonType.CHALLENGE_STEP_SETTING_NOT_FOUND
    val CHALLENGE_STEP_SETTING_NO_DEFAULT_MOVEMENT = ExtendedErrorReasonType.CHALLENGE_STEP_SETTING_NO_DEFAULT_MOVEMENT
    val CHALLENGE_STEP_SETTING_ALREADY_EXISTS = ExtendedErrorReasonType.CHALLENGE_STEP_SETTING_ALREADY_EXISTS
    val CHALLENGE_STEP_SETTING_TYPE_NOT_ALLOWED = ExtendedErrorReasonType.CHALLENGE_STEP_SETTING_TYPE_NOT_ALLOWED
    val CHALLENGE_STEP_NUMBER_OUT_OF_PLAN_RANGE = ExtendedErrorReasonType.CHALLENGE_STEP_NUMBER_OUT_OF_PLAN_RANGE
    val CHALLENGE_STEP_GROUP_NOT_SPECIFIED = ExtendedErrorReasonType.CHALLENGE_STEP_GROUP_NOT_SPECIFIED
    val CHALLENGE_STEP_SETTING_MOVEMENT_NOT_FOUND = ExtendedErrorReasonType.CHALLENGE_STEP_SETTING_MOVEMENT_NOT_FOUND
    val CHALLENGE_STEP_SETTING_MOVEMENT_NOT_ALLOWED_FOR_SETTING =
        ExtendedErrorReasonType.CHALLENGE_STEP_SETTING_MOVEMENT_NOT_ALLOWED_FOR_SETTING
    val CHALLENGE_STEP_SETTING_MOVEMENT_PERCENTAGE_VALUE_ALREADY_EXISTS =
        ExtendedErrorReasonType.CHALLENGE_STEP_SETTING_MOVEMENT_PERCENTAGE_VALUE_ALREADY_EXISTS
    val CHALLENGE_STEP_SETTING_MOVEMENT_LIST_VALUE_ALREADY_EXISTS =
        ExtendedErrorReasonType.CHALLENGE_STEP_SETTING_MOVEMENT_LIST_VALUE_ALREADY_EXISTS
    val CHALLENGE_STEP_SETTING_MOVEMENT_TYPE_REQUIRED_VALUES_INVALID =
        ExtendedErrorReasonType.CHALLENGE_STEP_SETTING_MOVEMENT_TYPE_REQUIRED_VALUES_INVALID
    val CHALLENGE_STEP_SETTING_MOVEMENT_TYPE_NOT_ALLOWED_FOR_GIVEN_SETTING =
        ExtendedErrorReasonType.CHALLENGE_STEP_SETTING_MOVEMENT_TYPE_NOT_ALLOWED_FOR_GIVEN_SETTING
    val INSTRUMENT_ALREADY_EXISTS = ExtendedErrorReasonType.INSTRUMENT_ALREADY_EXISTS
    val INSTRUMENT_NOT_FOUND = ExtendedErrorReasonType.INSTRUMENT_NOT_FOUND
    val INSTRUMENT_NOT_ASSIGNED_FOR_TOURNAMENT = ExtendedErrorReasonType.INSTRUMENT_NOT_ASSIGNED_FOR_TOURNAMENT
    val TRANSACTION_CREATION_FAILED = ExtendedErrorReasonType.TRANSACTION_CREATION_FAILED
    val TRANSACTION_NOT_FOUND = ExtendedErrorReasonType.TRANSACTION_NOT_FOUND
    val TRANSACTION_EXECUTION_FAILED = ExtendedErrorReasonType.TRANSACTION_EXECUTION_FAILED
    val TRANSACTION_NOT_REGISTERED_IN_PAYMENT_PROVIDER = ExtendedErrorReasonType.TRANSACTION_NOT_REGISTERED_IN_PAYMENT_PROVIDER
    val TRANSACTION_SUBSCRIPTION_CANCELLATION_FAILED = ExtendedErrorReasonType.TRANSACTION_SUBSCRIPTION_CANCELLATION_FAILED
    val GAME_NOT_FOUND = ExtendedErrorReasonType.GAME_NOT_FOUND
    val TOURNAMENT_WITH_INVALID_INITIAL_PRIZE_POOL = ExtendedErrorReasonType.TOURNAMENT_WITH_INVALID_INITIAL_PRIZE_POOL
    val TOURNAMENT_WITH_INVALID_START_END_TIMESTAMPS = ExtendedErrorReasonType.TOURNAMENT_WITH_INVALID_START_END_TIMESTAMPS
    val TOURNAMENT_NOT_FOUND = ExtendedErrorReasonType.TOURNAMENT_NOT_FOUND
    val TOURNAMENT_HAS_ENDED = ExtendedErrorReasonType.TOURNAMENT_HAS_ENDED
    val TOURNAMENT_NOT_IN_PROGRESS = ExtendedErrorReasonType.TOURNAMENT_NOT_IN_PROGRESS
    val TOURNAMENT_USER_NOT_FOUND = ExtendedErrorReasonType.TOURNAMENT_USER_NOT_FOUND
    val VALID_TOURNAMENT_USER_NOT_FOUND = ExtendedErrorReasonType.VALID_TOURNAMENT_USER_NOT_FOUND
    val TOURNAMENT_USER_ALREADY_JOINED = ExtendedErrorReasonType.TOURNAMENT_USER_ALREADY_JOINED
    val TOURNAMENT_INSTRUMENT_NOT_FOUND = ExtendedErrorReasonType.TOURNAMENT_INSTRUMENT_NOT_FOUND
    val TOURNAMENT_INSTRUMENT_CANNOT_BE_CHANGED_AFTER_TOURNAMENT_STARTED =
        ExtendedErrorReasonType.TOURNAMENT_INSTRUMENT_CANNOT_BE_CHANGED_AFTER_TOURNAMENT_STARTED
    val WITHDRAWAL_REQUEST_NOT_FOUND = ExtendedErrorReasonType.WITHDRAWAL_REQUEST_NOT_FOUND
    val WITHDRAWAL_REQUEST_ALREADY_EXISTS = ExtendedErrorReasonType.WITHDRAWAL_REQUEST_ALREADY_EXISTS
    val WITHDRAWAL_REQUEST_TYPE_REQUIRED_VALUES_INVALID = ExtendedErrorReasonType.WITHDRAWAL_REQUEST_TYPE_REQUIRED_VALUES_INVALID
    val WITHDRAWAL_REQUEST_NOT_PENDING = ExtendedErrorReasonType.WITHDRAWAL_REQUEST_NOT_PENDING
    val WITHDRAWAL_FREQUENCY_LIMIT_EXCEEDED = ExtendedErrorReasonType.WITHDRAWAL_FREQUENCY_LIMIT_EXCEEDED
    val COUNTRY_NOT_FOUND = ExtendedErrorReasonType.COUNTRY_NOT_FOUND
    val COUNTRY_NOT_ALLOWED = ExtendedErrorReasonType.COUNTRY_NOT_ALLOWED
    val TOURNAMENT_TRADE_NOT_FOUND = ExtendedErrorReasonType.TOURNAMENT_TRADE_NOT_FOUND
    val TOURNAMENT_TRADE_STOP_LOSS_OR_TAKE_PROFIT_INVALID =
        ExtendedErrorReasonType.TOURNAMENT_TRADE_STOP_LOSS_OR_TAKE_PROFIT_INVALID
    val OPEN_TOURNAMENT_TRADE_ALREADY_EXISTS = ExtendedErrorReasonType.OPEN_TOURNAMENT_TRADE_ALREADY_EXISTS
    val CACHED_INSTRUMENT_PRICE_NOT_FOUND = ExtendedErrorReasonType.CACHED_INSTRUMENT_PRICE_NOT_FOUND
    val CACHED_INSTRUMENT_PRICES_FOR_TOURNAMENT_NOT_FOUND =
        ExtendedErrorReasonType.CACHED_INSTRUMENT_PRICES_FOR_TOURNAMENT_NOT_FOUND
    val USER_CUSTOM_TOKEN_CREATION_FAILED = ExtendedErrorReasonType.USER_CUSTOM_TOKEN_CREATION_FAILED
    val TRADING_ACCOUNT_NOT_FOUND = ExtendedErrorReasonType.TRADING_ACCOUNT_NOT_FOUND
    val TRADING_ACCOUNT_HAS_OPEN_TRADES = ExtendedErrorReasonType.TRADING_ACCOUNT_HAS_OPEN_TRADES
    val TRADING_ACCOUNT_PROFIT_NOT_ENOUGH = ExtendedErrorReasonType.TRADING_ACCOUNT_PROFIT_NOT_ENOUGH
    val TRADING_ACCOUNT_IS_PAUSED = ExtendedErrorReasonType.TRADING_ACCOUNT_IS_PAUSED
    val TRADING_ACCOUNT_IS_NOT_PAUSED = ExtendedErrorReasonType.TRADING_ACCOUNT_IS_NOT_PAUSED
    val TRADING_ACCOUNT_PATCH_FAILED = ExtendedErrorReasonType.TRADING_ACCOUNT_PATCH_FAILED
    val TOURNAMENT_BUY_INS_LIMIT_EXCEEDED = ExtendedErrorReasonType.TOURNAMENT_BUY_INS_LIMIT_EXCEEDED
    val BREACH_NOT_FOUND = ExtendedErrorReasonType.BREACH_NOT_FOUND
    val BREACH_NOT_IN_FINAL_STATE = ExtendedErrorReasonType.BREACH_NOT_IN_FINAL_STATE
    val ORDER_NOT_FOUND = ExtendedErrorReasonType.ORDER_NOT_FOUND
    val ORDER_IS_NOT_FREE = ExtendedErrorReasonType.ORDER_IS_NOT_FREE
    val ORDER_MOVEMENT_REQUIRED = ExtendedErrorReasonType.ORDER_MOVEMENT_REQUIRED
    val ORDER_DISCOUNTED_PRICE_HIGHER_THAN_CURRENT_PRICE =
        ExtendedErrorReasonType.ORDER_DISCOUNTED_PRICE_HIGHER_THAN_CURRENT_PRICE
    val ORDER_PLATFORM_NOT_ALLOWED = ExtendedErrorReasonType.ORDER_PLATFORM_NOT_ALLOWED
    val DISCOUNT_CODE_NOT_FOUND = ExtendedErrorReasonType.DISCOUNT_CODE_NOT_FOUND
    val DISCOUNT_CODE_ALREADY_EXISTS = ExtendedErrorReasonType.DISCOUNT_CODE_ALREADY_EXISTS
    val DISCOUNT_CODE_REQUIRED_VALUES_INVALID = ExtendedErrorReasonType.DISCOUNT_CODE_REQUIRED_VALUES_INVALID
    val DISCOUNT_CODE_NOT_APPLICABLE = ExtendedErrorReasonType.DISCOUNT_CODE_NOT_APPLICABLE
    val DISCOUNT_CODE_COUNTRY_LIMIT_REACHED = ExtendedErrorReasonType.DISCOUNT_CODE_COUNTRY_LIMIT_REACHED
    val DISCOUNT_CODE_ALREADY_USED = ExtendedErrorReasonType.DISCOUNT_CODE_ALREADY_USED
    val TRADING_ACCOUNT_CREATION_FAILED = ExtendedErrorReasonType.TRADING_ACCOUNT_CREATION_FAILED
    val TRADING_ACCOUNT_POSITION_CLOSING_FAILED = ExtendedErrorReasonType.TRADING_ACCOUNT_POSITION_CLOSING_FAILED
    val TRADING_ACCOUNT_RESTRICTION_FAILED = ExtendedErrorReasonType.TRADING_ACCOUNT_RESTRICTION_FAILED
    val TRADING_ACCOUNT_ACTIVATION_FAILED = ExtendedErrorReasonType.TRADING_ACCOUNT_ACTIVATION_FAILED
    val TRADING_ACCOUNT_WITHDRAWAL_FAILED = ExtendedErrorReasonType.TRADING_ACCOUNT_WITHDRAWAL_FAILED
    val TRADING_ACCOUNT_ALREADY_EXISTS = ExtendedErrorReasonType.TRADING_ACCOUNT_ALREADY_EXISTS
    val TRADING_ACCOUNT_ALREADY_BREACHED = ExtendedErrorReasonType.TRADING_ACCOUNT_ALREADY_BREACHED
    val TRADING_ACCOUNT_ALREADY_UPGRADED = ExtendedErrorReasonType.TRADING_ACCOUNT_ALREADY_UPGRADED
    val TRADING_ACCOUNT_NOT_ACTIVE = ExtendedErrorReasonType.TRADING_ACCOUNT_NOT_ACTIVE
    val TRADING_ACCOUNT_SAME_PLATFORM_SELECTED = ExtendedErrorReasonType.TRADING_ACCOUNT_SAME_PLATFORM_SELECTED
    val TRADING_ACCOUNT_PLATFORM_NOT_IN_PLAN = ExtendedErrorReasonType.TRADING_ACCOUNT_PLATFORM_NOT_IN_PLAN
    val TRADING_ACCOUNT_CANNOT_BE_UPGRADED = ExtendedErrorReasonType.TRADING_ACCOUNT_CANNOT_BE_UPGRADED
    val TRADE_NOT_FOUND = ExtendedErrorReasonType.TRADE_NOT_FOUND
    val INVALID_ORDER_STATE = ExtendedErrorReasonType.INVALID_ORDER_STATE
    val TRADING_ACCOUNT_HISTORY_NOT_FOUND = ExtendedErrorReasonType.TRADING_ACCOUNT_HISTORY_NOT_FOUND
    val PRIZE_DISTRIBUTION_NOT_FOUND = ExtendedErrorReasonType.PRIZE_DISTRIBUTION_NOT_FOUND
    val PRIZE_DISTRIBUTION_ALREADY_EXISTS = ExtendedErrorReasonType.PRIZE_DISTRIBUTION_ALREADY_EXISTS
    val PRIZE_DISTRIBUTION_SNAPSHOT_NOT_FOUND = ExtendedErrorReasonType.PRIZE_DISTRIBUTION_SNAPSHOT_NOT_FOUND
    val PAYOUT_NOT_FOUND = ExtendedErrorReasonType.PAYOUT_NOT_FOUND
    val PAYOUT_REQUEST_ALREADY_EXISTS = ExtendedErrorReasonType.PAYOUT_REQUEST_ALREADY_EXISTS
    val DISCOUNT_CODE_CHALLENGE_PLAN_NOT_FOUND = ExtendedErrorReasonType.DISCOUNT_CODE_CHALLENGE_PLAN_NOT_FOUND
    val DISCOUNT_CODE_USER_NOT_FOUND = ExtendedErrorReasonType.DISCOUNT_CODE_USER_NOT_FOUND
    val PAYOUT_AMOUNT_LESS_THAN_ALLOWED_MINIMUM = ExtendedErrorReasonType.PAYOUT_AMOUNT_LESS_THAN_ALLOWED_MINIMUM
    val PAYOUT_AMOUNT_EXCEEDS_MAXIMUM = ExtendedErrorReasonType.PAYOUT_AMOUNT_EXCEEDS_MAXIMUM
    val PAYOUT_MINIMUM_DURATION_NOT_SATISFIED = ExtendedErrorReasonType.PAYOUT_MINIMUM_DURATION_NOT_SATISFIED
    val PAYOUT_MIN_PROFITABLE_TRADING_DAYS_NOT_SATISFIED =
        ExtendedErrorReasonType.PAYOUT_MIN_PROFITABLE_TRADING_DAYS_NOT_SATISFIED
    val PAYOUT_CONSISTENCY_TARGET_NOT_SATISFIED = ExtendedErrorReasonType.PAYOUT_CONSISTENCY_TARGET_NOT_SATISFIED
    val PAYOUT_STATE_CHANGE_NOT_ALLOWED = ExtendedErrorReasonType.PAYOUT_STATE_CHANGE_NOT_ALLOWED
    val CERTIFICATE_NOT_FOUND = ExtendedErrorReasonType.CERTIFICATE_NOT_FOUND
    val AFFILIATE_NOT_FOUND = ExtendedErrorReasonType.AFFILIATE_NOT_FOUND
    val AFFILIATE_FOR_USER_ALREADY_EXISTS = ExtendedErrorReasonType.AFFILIATE_FOR_USER_ALREADY_EXISTS
    val AFFILIATE_WITH_COUPON_CODE_ALREADY_EXISTS = ExtendedErrorReasonType.AFFILIATE_WITH_COUPON_CODE_ALREADY_EXISTS
    val AFFILIATE_COMMISSION_NOT_FOUND = ExtendedErrorReasonType.AFFILIATE_COMMISSION_NOT_FOUND
    val AFFILIATE_COMMISSION_BALANCE_NOT_ENOUGH = ExtendedErrorReasonType.AFFILIATE_COMMISSION_BALANCE_NOT_ENOUGH
    val AFFILIATE_PAYOUT_NOT_FOUND = ExtendedErrorReasonType.AFFILIATE_PAYOUT_NOT_FOUND
    val AFFILIATE_PAYOUT_REQUEST_ALREADY_EXISTS = ExtendedErrorReasonType.AFFILIATE_PAYOUT_REQUEST_ALREADY_EXISTS
    val AFFILIATE_PAYOUT_AMOUNT_LESS_THAN_ALLOWED_MINIMUM =
        ExtendedErrorReasonType.AFFILIATE_PAYOUT_AMOUNT_LESS_THAN_ALLOWED_MINIMUM
    val AFFILIATE_PAYOUT_MINIMUM_DURATION_NOT_SATISFIED = ExtendedErrorReasonType.AFFILIATE_PAYOUT_MINIMUM_DURATION_NOT_SATISFIED
    val AFFILIATE_PAYOUT_STATE_CHANGE_NOT_ALLOWED = ExtendedErrorReasonType.AFFILIATE_PAYOUT_STATE_CHANGE_NOT_ALLOWED
    val AFFILIATE_CANNOT_BE_APPLIED_TO_YOURSELF = ExtendedErrorReasonType.AFFILIATE_CANNOT_BE_APPLIED_TO_YOURSELF
    val FUTURE_ORDER_NOT_FOUND = ExtendedErrorReasonType.FUTURE_ORDER_NOT_FOUND
    val MAIL_TEMPLATE_NOT_FOUND = ExtendedErrorReasonType.MAIL_TEMPLATE_NOT_FOUND
    val ORDER_BLACKLIST_NOT_FOUND = ExtendedErrorReasonType.ORDER_BLACKLIST_NOT_FOUND
    val ORDER_BLACKLIST_CONTAINS_EMAIL = ExtendedErrorReasonType.ORDER_BLACKLIST_CONTAINS_EMAIL
    val TRADING_ACCOUNT_FOR_REVIEW_NOT_PENDING = ExtendedErrorReasonType.TRADING_ACCOUNT_FOR_REVIEW_NOT_PENDING
    val TRADING_ACCOUNT_FOR_REVIEW_NOT_APPROVED = ExtendedErrorReasonType.TRADING_ACCOUNT_FOR_REVIEW_NOT_APPROVED
    val TRADING_ACCOUNT_FOR_REVIEW_NOT_FINALISED = ExtendedErrorReasonType.TRADING_ACCOUNT_FOR_REVIEW_NOT_FINALISED
    val TRADING_ACCOUNT_FOR_REVIEW_NOT_FOUND = ExtendedErrorReasonType.TRADING_ACCOUNT_FOR_REVIEW_NOT_FOUND
    val CONTRACT_TEMPLATE_NOT_FOUND = ExtendedErrorReasonType.CONTRACT_TEMPLATE_NOT_FOUND
    val USER_CONTRACT_NOT_FOUND = ExtendedErrorReasonType.USER_CONTRACT_NOT_FOUND
    val USER_CONTRACT_CREATION_FAILED = ExtendedErrorReasonType.USER_CONTRACT_CREATION_FAILED
    val EMAIL_LOG_NOT_FOUND = ExtendedErrorReasonType.EMAIL_LOG_NOT_FOUND
    val SYSTEM_SETTING_NOT_FOUND = ExtendedErrorReasonType.SYSTEM_SETTING_NOT_FOUND
    val LABEL_NOT_FOUND = ExtendedErrorReasonType.LABEL_NOT_FOUND
    val VERIFICATION_CALL_REQUIRED_VALUES_INVALID = ExtendedErrorReasonType.VERIFICATION_CALL_REQUIRED_VALUES_INVALID
    val VERIFICATION_CALL_NOT_FOUND = ExtendedErrorReasonType.VERIFICATION_CALL_NOT_FOUND
}

enum class ExtendedErrorReasonType : ErrorReason {
    USER_NOT_FOUND,
    USER_EMAIL_ALREADY_EXISTS,
    USER_FIREBASE_UPDATE_FAILED,
    USER_KYC_VERIFICATION_ALREADY_FINISHED,
    CONTACT_NOT_FOUND,
    CONTACT_PHONE_NUMBER_INVALID,
    FILE_NOT_FOUND,
    FILE_NOT_DELETED,
    WALLET_NOT_FOUND,
    WALLET_BALANCE_NOT_ENOUGH,
    CHALLENGE_NOT_FOUND,
    CHALLENGE_WITH_STARTING_BALANCE_AND_TYPE_ALREADY_EXISTS,
    CHALLENGE_PLAN_NOT_FOUND,
    CHALLENGE_PLAN_NOT_DRAFT,
    CHALLENGE_PLAN_ALREADY_ARCHIVED,
    CHALLENGE_PLAN_ALREADY_EXISTS,
    CHALLENGE_PLAN_HAS_SUBSCRIPTION_PRICING,
    CHALLENGE_PLAN_PLATFORMS_NOT_SPECIFIED,
    CHALLENGE_STEP_NOT_FOUND,
    CHALLENGE_STEP_NOT_FUNDED,
    CHALLENGE_STEP_SETTING_NOT_FOUND,
    CHALLENGE_STEP_SETTING_NO_DEFAULT_MOVEMENT,
    CHALLENGE_STEP_SETTING_ALREADY_EXISTS,
    CHALLENGE_STEP_SETTING_TYPE_NOT_ALLOWED,
    CHALLENGE_STEP_NUMBER_OUT_OF_PLAN_RANGE,
    CHALLENGE_STEP_GROUP_NOT_SPECIFIED,
    CHALLENGE_STEP_SETTING_MOVEMENT_NOT_FOUND,
    CHALLENGE_STEP_SETTING_MOVEMENT_NOT_ALLOWED_FOR_SETTING,
    CHALLENGE_STEP_SETTING_MOVEMENT_PERCENTAGE_VALUE_ALREADY_EXISTS,
    CHALLENGE_STEP_SETTING_MOVEMENT_LIST_VALUE_ALREADY_EXISTS,
    CHALLENGE_STEP_SETTING_MOVEMENT_TYPE_REQUIRED_VALUES_INVALID,
    CHALLENGE_STEP_SETTING_MOVEMENT_TYPE_NOT_ALLOWED_FOR_GIVEN_SETTING,
    INSTRUMENT_ALREADY_EXISTS,
    INSTRUMENT_NOT_FOUND,
    INSTRUMENT_NOT_ASSIGNED_FOR_TOURNAMENT,
    TRANSACTION_CREATION_FAILED,
    TRANSACTION_NOT_FOUND,
    TRANSACTION_EXECUTION_FAILED,
    TRANSACTION_NOT_REGISTERED_IN_PAYMENT_PROVIDER,
    TRANSACTION_SUBSCRIPTION_CANCELLATION_FAILED,
    GAME_NOT_FOUND,
    TOURNAMENT_WITH_INVALID_INITIAL_PRIZE_POOL,
    TOURNAMENT_WITH_INVALID_START_END_TIMESTAMPS,
    TOURNAMENT_NOT_FOUND,
    TOURNAMENT_HAS_ENDED,
    TOURNAMENT_NOT_IN_PROGRESS,
    TOURNAMENT_USER_NOT_FOUND,
    VALID_TOURNAMENT_USER_NOT_FOUND,
    TOURNAMENT_USER_ALREADY_JOINED,
    TOURNAMENT_INSTRUMENT_NOT_FOUND,
    TOURNAMENT_INSTRUMENT_CANNOT_BE_CHANGED_AFTER_TOURNAMENT_STARTED,
    WITHDRAWAL_REQUEST_NOT_FOUND,
    WITHDRAWAL_REQUEST_ALREADY_EXISTS,
    WITHDRAWAL_REQUEST_TYPE_REQUIRED_VALUES_INVALID,
    WITHDRAWAL_REQUEST_NOT_PENDING,
    WITHDRAWAL_FREQUENCY_LIMIT_EXCEEDED,
    COUNTRY_NOT_FOUND,
    COUNTRY_NOT_ALLOWED,
    TOURNAMENT_TRADE_NOT_FOUND,
    TOURNAMENT_TRADE_STOP_LOSS_OR_TAKE_PROFIT_INVALID,
    OPEN_TOURNAMENT_TRADE_ALREADY_EXISTS,
    CACHED_INSTRUMENT_PRICE_NOT_FOUND,
    CACHED_INSTRUMENT_PRICES_FOR_TOURNAMENT_NOT_FOUND,
    USER_CUSTOM_TOKEN_CREATION_FAILED,
    TRADING_ACCOUNT_NOT_FOUND,
    TRADING_ACCOUNT_HAS_OPEN_TRADES,
    TRADING_ACCOUNT_PROFIT_NOT_ENOUGH,
    TRADING_ACCOUNT_IS_PAUSED,
    TRADING_ACCOUNT_IS_NOT_PAUSED,
    TRADING_ACCOUNT_PATCH_FAILED,
    TOURNAMENT_BUY_INS_LIMIT_EXCEEDED,
    BREACH_NOT_FOUND,
    BREACH_NOT_IN_FINAL_STATE,
    ORDER_NOT_FOUND,
    ORDER_IS_NOT_FREE,
    ORDER_MOVEMENT_REQUIRED,
    ORDER_DISCOUNTED_PRICE_HIGHER_THAN_CURRENT_PRICE,
    ORDER_PLATFORM_NOT_ALLOWED,
    DISCOUNT_CODE_NOT_FOUND,
    DISCOUNT_CODE_ALREADY_EXISTS,
    DISCOUNT_CODE_REQUIRED_VALUES_INVALID,
    DISCOUNT_CODE_NOT_APPLICABLE,
    DISCOUNT_CODE_COUNTRY_LIMIT_REACHED,
    DISCOUNT_CODE_ALREADY_USED,
    TRADING_ACCOUNT_CREATION_FAILED,
    TRADING_ACCOUNT_POSITION_CLOSING_FAILED,
    TRADING_ACCOUNT_ALREADY_EXISTS,
    TRADING_ACCOUNT_ALREADY_BREACHED,
    TRADING_ACCOUNT_ALREADY_UPGRADED,
    TRADING_ACCOUNT_NOT_ACTIVE,
    TRADING_ACCOUNT_SAME_PLATFORM_SELECTED,
    TRADING_ACCOUNT_PLATFORM_NOT_IN_PLAN,
    TRADING_ACCOUNT_CANNOT_BE_UPGRADED,
    TRADING_ACCOUNT_RESTRICTION_FAILED,
    TRADING_ACCOUNT_ACTIVATION_FAILED,
    TRADING_ACCOUNT_WITHDRAWAL_FAILED,
    TRADE_NOT_FOUND,
    INVALID_ORDER_STATE,
    TRADING_ACCOUNT_HISTORY_NOT_FOUND,
    PRIZE_DISTRIBUTION_NOT_FOUND,
    PRIZE_DISTRIBUTION_ALREADY_EXISTS,
    PRIZE_DISTRIBUTION_SNAPSHOT_NOT_FOUND,
    PAYOUT_NOT_FOUND,
    PAYOUT_REQUEST_ALREADY_EXISTS,
    DISCOUNT_CODE_CHALLENGE_PLAN_NOT_FOUND,
    DISCOUNT_CODE_USER_NOT_FOUND,
    PAYOUT_AMOUNT_LESS_THAN_ALLOWED_MINIMUM,
    PAYOUT_AMOUNT_EXCEEDS_MAXIMUM,
    PAYOUT_MINIMUM_DURATION_NOT_SATISFIED,
    PAYOUT_MIN_PROFITABLE_TRADING_DAYS_NOT_SATISFIED,
    PAYOUT_CONSISTENCY_TARGET_NOT_SATISFIED,
    PAYOUT_STATE_CHANGE_NOT_ALLOWED,
    CERTIFICATE_NOT_FOUND,
    AFFILIATE_NOT_FOUND,
    AFFILIATE_FOR_USER_ALREADY_EXISTS,
    AFFILIATE_WITH_COUPON_CODE_ALREADY_EXISTS,
    AFFILIATE_COMMISSION_NOT_FOUND,
    AFFILIATE_COMMISSION_BALANCE_NOT_ENOUGH,
    AFFILIATE_PAYOUT_NOT_FOUND,
    AFFILIATE_PAYOUT_REQUEST_ALREADY_EXISTS,
    AFFILIATE_PAYOUT_AMOUNT_LESS_THAN_ALLOWED_MINIMUM,
    AFFILIATE_PAYOUT_MINIMUM_DURATION_NOT_SATISFIED,
    AFFILIATE_PAYOUT_STATE_CHANGE_NOT_ALLOWED,
    AFFILIATE_CANNOT_BE_APPLIED_TO_YOURSELF,
    FUTURE_ORDER_NOT_FOUND,
    MAIL_TEMPLATE_NOT_FOUND,
    ORDER_BLACKLIST_NOT_FOUND,
    ORDER_BLACKLIST_CONTAINS_EMAIL,
    TRADING_ACCOUNT_FOR_REVIEW_NOT_PENDING,
    TRADING_ACCOUNT_FOR_REVIEW_NOT_APPROVED,
    TRADING_ACCOUNT_FOR_REVIEW_NOT_FINALISED,
    TRADING_ACCOUNT_FOR_REVIEW_NOT_FOUND,
    CONTRACT_TEMPLATE_NOT_FOUND,
    USER_CONTRACT_NOT_FOUND,
    USER_CONTRACT_CREATION_FAILED,
    EMAIL_LOG_NOT_FOUND,
    SYSTEM_SETTING_NOT_FOUND,
    LABEL_NOT_FOUND,
    VERIFICATION_CALL_REQUIRED_VALUES_INVALID,
    VERIFICATION_CALL_NOT_FOUND,
    ;

    override val errorCode: ErrorCode
        get() = ErrorCode(this.name)
}
