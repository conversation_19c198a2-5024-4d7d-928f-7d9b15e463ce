package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.tradingaccount.command.AdminMigrateTradingAccountPlatformCommand
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import java.util.UUID

data class AdminMigrateTradingAccountPlatformRequest(
    val newPlatform: PlatformType,
) {
    fun toCommand(tradingAccountId: UUID) = AdminMigrateTradingAccountPlatformCommand(
        tradingAccountId = tradingAccountId,
        newPlatform = newPlatform
    )
}
