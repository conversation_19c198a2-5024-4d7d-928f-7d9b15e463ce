package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.systemsetting.command.AdminUpdateSystemSettingCommand
import com.cleevio.fortraders.domain.model.systemSetting.constant.SystemSettingType

data class AdminUpdateSystemSettingRequest(
    val value: String,
) {
    fun toCommand(type: SystemSettingType) = AdminUpdateSystemSettingCommand(
        type = type,
        value = value
    )
}
