package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.user.command.UpdateUserKycStateCommand
import java.util.UUID

data class VeriffFullAutoVerificationRequest(
    val vendorData: UUID,
    val data: VerificationDataResult,
) {
    data class VerificationDataResult(
        val verification: VerificationResult,
    )

    data class VerificationResult(
        val decision: String,
    )

    fun toCommand() = UpdateUserKycStateCommand(
        userId = vendorData,
        isVerified = data.verification.decision == "approved"
    )
}
