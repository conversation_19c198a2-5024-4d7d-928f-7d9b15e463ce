package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.AdminApprovePayoutRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminDeclinePayoutRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminPatchPayoutRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminSearchPayoutsRequest
import com.cleevio.fortraders.application.common.command.CommandBus
import com.cleevio.fortraders.application.common.dto.SimplePage
import com.cleevio.fortraders.application.common.dto.toSimplePage
import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.payout.query.AdminGetPayoutQuery
import com.cleevio.fortraders.application.module.payout.query.AdminSearchPayoutsQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Admin payouts")
@RestController
@RequestMapping("/admin-app/payouts")
class AdminPayoutsController(
    private val queryBus: QueryBus,
    private val commandBus: CommandBus,
) {
    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun adminSearchPayouts(
        @ParameterObject @PageableDefaultSort pageable: Pageable,
        @RequestBody request: AdminSearchPayoutsRequest,
    ): SimplePage<AdminSearchPayoutsQuery.ResultWithTotalPayouts> =
        queryBus(AdminSearchPayoutsQuery(pageable = pageable, filter = request.toFilter())).toSimplePage()

    @GetMapping("/{payoutId}", produces = [ApiVersion.VERSION_1_JSON])
    fun adminGetPayout(@PathVariable payoutId: UUID): AdminGetPayoutQuery.Result = queryBus(AdminGetPayoutQuery(payoutId))

    @PatchMapping("/{payoutId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun adminPatchPayout(@PathVariable payoutId: UUID, @RequestBody request: AdminPatchPayoutRequest): Unit = commandBus(
        request.toCommand(
            payoutId = payoutId
        )
    )

    @PostMapping("/{payoutId}/approve", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun adminApprovePayout(@PathVariable payoutId: UUID, @RequestBody request: AdminApprovePayoutRequest): Unit = commandBus(
        request.toCommand(
            payoutId = payoutId
        )
    )

    @PostMapping("/{payoutId}/decline", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun adminDeclinePayout(@PathVariable payoutId: UUID, @RequestBody request: AdminDeclinePayoutRequest): Unit = commandBus(
        request.toCommand(
            payoutId = payoutId
        )
    )
}
