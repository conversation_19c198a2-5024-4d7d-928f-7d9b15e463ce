package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.tournamenttrade.command.CreateTournamentTradeCommand
import com.cleevio.fortraders.domain.model.tournamenttrade.constant.TournamentTradeType
import java.math.BigDecimal
import java.util.UUID

data class CreateTournamentTradeRequest(
    val instrumentId: UUID,
    val type: TournamentTradeType,
    val leverage: Int = 1,
    val stopLoss: BigDecimal?,
    val takeProfit: BigDecimal?,
) {
    fun toCommand(tournamentId: UUID, userId: UUID, gameId: UUID) = CreateTournamentTradeCommand(
        gameId = gameId,
        tournamentId = tournamentId,
        userId = userId,
        instrumentId = instrumentId,
        type = type,
        leverage = leverage,
        stopLoss = stopLoss,
        takeProfit = takeProfit
    )
}
