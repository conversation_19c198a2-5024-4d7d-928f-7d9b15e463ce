package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.country.query.GetCountriesQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Tag(name = "Countries")
@RestController
@RequestMapping("/web-app/countries")
class CountryController(
    private val queryBus: QueryBus,
) {
    @GetMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun getCountries(@RequestParam(required = false) accessAllowed: Boolean?): List<GetCountriesQuery.Result> = queryBus(
        GetCountriesQuery(accessAllowed = accessAllowed)
    )
}
