package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.AdminCreateLabelRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminSearchLabelsRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminUpdateLabelRequest
import com.cleevio.fortraders.application.common.command.CommandBus
import com.cleevio.fortraders.application.common.dto.SimplePage
import com.cleevio.fortraders.application.common.dto.toSimplePage
import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.label.command.AdminDeleteLabelCommand
import com.cleevio.fortraders.application.module.label.query.AdminSearchLabelsQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Admin labels")
@RestController
@RequestMapping("/admin-app/labels")
class AdminLabelsController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {
    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun searchLabels(
        @ParameterObject @PageableDefaultSort pageable: Pageable,
        @RequestBody request: AdminSearchLabelsRequest,
    ): SimplePage<AdminSearchLabelsQuery.Result> = queryBus(
        request.toQuery(pageable)
    ).toSimplePage()

    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun createLabel(@RequestBody request: AdminCreateLabelRequest): Unit = commandBus(request.toCommand())

    @PutMapping("/{labelId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun updateLabel(@PathVariable labelId: UUID, @RequestBody request: AdminUpdateLabelRequest): Unit =
        commandBus(request.toCommand(labelId))

    @DeleteMapping("/{labelId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteLabel(@PathVariable labelId: UUID): Unit = commandBus(AdminDeleteLabelCommand(labelId))
}
