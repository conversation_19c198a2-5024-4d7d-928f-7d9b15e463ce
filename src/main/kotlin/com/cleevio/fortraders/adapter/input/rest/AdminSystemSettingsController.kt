package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.AdminUpdateSystemSettingRequest
import com.cleevio.fortraders.application.common.command.CommandBus
import com.cleevio.fortraders.domain.model.systemSetting.constant.SystemSettingType
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController

@Tag(name = "Admin system settings")
@RestController
@RequestMapping("/admin-app/system-settings")
class AdminSystemSettingsController(
    private val commandBus: CommandBus,
) {
    @PutMapping("/{type}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun updateSystemSetting(@PathVariable type: SystemSettingType, @RequestBody request: AdminUpdateSystemSettingRequest): Unit =
        commandBus(request.toCommand(type))
}
