package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.CreatePayoutRequest
import com.cleevio.fortraders.application.common.command.CommandBus
import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.payout.query.GetOrderPayoutsQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Payouts")
@RestController
@RequestMapping("/web-app/users/me/orders/{orderId}/payouts")
class UserOrderPayoutsController(
    private val queryBus: QueryBus,
    private val commandBus: CommandBus,
) {
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun createPayout(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable orderId: UUID,
        @RequestBody request: CreatePayoutRequest,
    ): Unit = commandBus(
        request.toCommand(userId = userId, orderId = orderId)
    )

    @GetMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun getPayouts(@AuthenticationPrincipal userId: UUID, @PathVariable orderId: UUID): GetOrderPayoutsQuery.Result = queryBus(
        GetOrderPayoutsQuery(
            userId = userId,
            orderId = orderId
        )
    )
}
