package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.affiliate.query.GetUserAffiliateQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "User affiliate")
@RestController
@RequestMapping("/web-app/users/me/affiliate")
class UserAffiliateController(
    private val queryBus: QueryBus,
) {
    @GetMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun getUserAffiliate(@AuthenticationPrincipal userId: UUID): GetUserAffiliateQuery.Result = queryBus(
        GetUserAffiliateQuery(userId = userId)
    )
}
