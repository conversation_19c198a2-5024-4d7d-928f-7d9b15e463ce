package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.report.query.AdminSearchInstantAccountPayoutsReportQuery
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import java.util.UUID

data class AdminSearchInstantAccountPayoutsReportRequest(
    val challengePlanIds: Set<UUID>?,
    val payoutProbabilityFrom: Int?,
    val payoutProbabilityTo: Int?,
    val labels: Set<UUID>?,
    val challengeTypes: Set<ChallengeType>?,
) {
    fun toFilter() = AdminSearchInstantAccountPayoutsReportQuery.Filter(
        challengePlanIds = challengePlanIds,
        payoutProbabilityFrom = payoutProbabilityFrom,
        payoutProbabilityTo = payoutProbabilityTo,
        labels = labels,
        challengeTypes = challengeTypes
    )
}
