package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.tradingaccount.command.PatchTradingAccountCommand
import java.util.UUID

data class PatchTradingAccountRequest(
    val hasPublicTradingJournal: Boolean?,
) {
    fun toCommand(userId: UUID, tradingAccountId: UUID): PatchTradingAccountCommand = PatchTradingAccountCommand(
        userId = userId,
        tradingAccountId = tradingAccountId,
        hasPublicTradingJournal = hasPublicTradingJournal
    )
}
