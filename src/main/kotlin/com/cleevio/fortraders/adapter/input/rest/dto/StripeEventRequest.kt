package com.cleevio.fortraders.adapter.input.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

data class StripeEventRequest(
    val data: DataInput,
) {
    data class DataInput(
        val `object`: ObjectInput,
    )

    @JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "object",
        visible = true
    )
    @JsonSubTypes(
        JsonSubTypes.Type(value = ObjectInput.PaymentIntentInput::class, name = "payment_intent")
    )
    sealed class ObjectInput {
        abstract val eventType: String

        data class PaymentIntentInput(
            @JsonProperty("object") override val eventType: String,
            val status: String,
            val metadata: Map<String, String>,
        ) : ObjectInput()
    }
}
