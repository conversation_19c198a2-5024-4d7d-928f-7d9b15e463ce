package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.verificationcall.command.AdminCreateVerificationCallCommand
import com.cleevio.fortraders.domain.model.verificationcall.constant.VerificationCallType
import java.util.UUID

data class AdminCreateVerificationCallRequest(
    val type: VerificationCallType,
    val tradingAccountId: UUID,
    val payoutId: UUID?,
    val note: String?,
    val emailContent: String?,
    val supportUserIds: Set<UUID>?,
) {
    fun toCommand() = AdminCreateVerificationCallCommand(
        type = type,
        tradingAccountId = tradingAccountId,
        payoutId = payoutId,
        note = note,
        emailContent = emailContent,
        supportUserIds = supportUserIds
    )
}
