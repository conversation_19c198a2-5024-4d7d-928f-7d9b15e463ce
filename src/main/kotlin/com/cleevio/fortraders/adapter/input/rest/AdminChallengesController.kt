package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.AdminCreateChallengeRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminPatchChallengeRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminSearchChallengesRequest
import com.cleevio.fortraders.application.common.command.CommandBus
import com.cleevio.fortraders.application.common.dto.SimplePage
import com.cleevio.fortraders.application.common.dto.toSimplePage
import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.challenge.query.AdminGetChallengeQuery
import com.cleevio.fortraders.application.module.challenge.query.AdminSearchChallengesQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Admin challenges")
@RestController
@RequestMapping("/admin-app/challenges")
class AdminChallengesController(
    private val queryBus: QueryBus,
    private val commandBus: CommandBus,
) {
    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun adminSearchChallenges(
        @ParameterObject @PageableDefaultSort pageable: Pageable,
        @RequestBody request: AdminSearchChallengesRequest,
    ): SimplePage<AdminSearchChallengesQuery.Result> =
        queryBus(AdminSearchChallengesQuery(pageable = pageable, filter = request.toFilter())).toSimplePage()

    @GetMapping("/{challengeId}", produces = [ApiVersion.VERSION_1_JSON])
    fun adminGetChallenge(@PathVariable challengeId: UUID): AdminGetChallengeQuery.Result =
        queryBus(AdminGetChallengeQuery(challengeId = challengeId))

    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun adminCreateChallenge(@RequestBody request: AdminCreateChallengeRequest): Unit = commandBus(request.toCommand())

    @PatchMapping("/{challengeId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun adminPatchChallenge(@PathVariable challengeId: UUID, @RequestBody request: AdminPatchChallengeRequest): Unit =
        commandBus(request.toCommand(challengeId = challengeId))
}
