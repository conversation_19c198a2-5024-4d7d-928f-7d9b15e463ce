package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.CalculateChallengePriceRequest
import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.challenge.query.CalculateChallengePriceQuery
import com.cleevio.fortraders.application.module.challenge.query.GetChallengesQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Challenges")
@RestController
@RequestMapping("/web-app/challenges")
class ChallengesController(
    private val queryBus: QueryBus,
) {
    @GetMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun getChallenges(@RequestParam(required = false) selectChallengePlanId: String?): List<GetChallengesQuery.Result> = queryBus(
        GetChallengesQuery(
            selectChallengePlanId = selectChallengePlanId
        )
    )

    @PostMapping("/{challengeId}/price", produces = [ApiVersion.VERSION_1_JSON])
    fun calculateChallengePrice(
        @AuthenticationPrincipal userId: UUID?,
        @PathVariable challengeId: UUID,
        @RequestBody request: CalculateChallengePriceRequest,
    ): CalculateChallengePriceQuery.Result = queryBus(request.toQuery(userId = userId, challengeId = challengeId))
}
