package com.cleevio.fortraders.adapter.input.rest.dto

import com.cleevio.fortraders.application.module.user.query.AdminSearchUsersQuery
import com.cleevio.fortraders.domain.model.user.constant.UserRole
import com.cleevio.fortraders.domain.model.user.constant.UserState
import java.time.Instant
import java.util.UUID

data class AdminSearchUsersRequest(
    val createdAtFrom: Instant?,
    val createdAtTo: Instant?,
    val states: Set<UserState>?,
    val labels: Set<UUID>?,
    val fulltext: String?,
    val roles: Set<UserRole>?,
) {

    fun toFilter() = AdminSearchUsersQuery.Filter(
        createdAtFrom = createdAtFrom,
        createdAtTo = createdAtTo,
        states = states,
        labels = labels,
        fulltext = fulltext,
        roles = roles
    )
}
