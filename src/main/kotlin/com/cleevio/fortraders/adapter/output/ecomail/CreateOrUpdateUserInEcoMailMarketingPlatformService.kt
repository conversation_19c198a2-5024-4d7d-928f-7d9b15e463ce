package com.cleevio.fortraders.adapter.output.ecomail

import com.cleevio.fortraders.application.module.marketing.port.output.CreateOrUpdateUserInMarketingPlatform
import com.cleevio.fortraders.infrastructure.config.logger
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.Instant

@Service
class CreateOrUpdateUserInEcoMailMarketingPlatformService(
    private val ecoMailConnector: EcoMailConnector,
) : CreateOrUpdateUserInMarketingPlatform {
    private val logger = logger()

    override fun invoke(
        email: String,
        firstName: String?,
        lastName: String?,
        streetAddress: String?,
        city: String?,
        countryIsoCode: String?,
        postCode: String?,
        phoneNumber: String?,
        numberOfActiveChallenges: Int,
        userOrdersRevenueTotal: BigDecimal,
        paidOrdersCount: Long,
        firstPaidOrderCreatedAt: Instant?,
        latestPaidOrderCreatedAt: Instant?,
        unpaidOrdersCount: Long,
        firstUnpaidOrderCreatedAt: Instant?,
        latestUnpaidOrderCreatedAt: Instant?,
    ) {
        runCatching {
            ecoMailConnector.createOrUpdateListMember(
                email = email,
                firstName = firstName,
                lastName = lastName,
                streetAddress = streetAddress,
                city = city,
                countryIsoCode = countryIsoCode,
                postCode = postCode,
                phoneNumber = phoneNumber,
                numberOfActiveChallenges = numberOfActiveChallenges,
                userOrdersRevenueTotal = userOrdersRevenueTotal,
                paidOrdersCount = paidOrdersCount,
                firstPaidOrderCreatedAt = firstPaidOrderCreatedAt,
                latestPaidOrderCreatedAt = latestPaidOrderCreatedAt,
                unpaidOrdersCount = unpaidOrdersCount,
                firstUnpaidOrderCreatedAt = firstUnpaidOrderCreatedAt,
                latestUnpaidOrderCreatedAt = latestUnpaidOrderCreatedAt
            )
        }.onFailure {
            logger.error("Failed to update list member in EcoMail", it)
        }
    }
}
