package com.cleevio.fortraders.adapter.output.firebase

import com.cleevio.fortraders.application.module.user.port.output.UpdateFirebaseUser
import com.cleevio.fortraders.domain.model.user.constant.UserRole
import com.cleevio.fortraders.domain.model.user.constant.UserState
import org.springframework.stereotype.Service

@Service
class UpdateFirebaseUserService(
    private val firebaseService: FirebaseService,
) : UpdateFirebaseUser {
    override fun invoke(firebaseId: String, role: UserRole?, password: String?, state: UserState): Result<Unit> = runCatching {
        firebaseService.updateUser(
            firebaseId = firebaseId,
            role = role,
            isDisabled = state == UserState.BLOCKED,
            password = password
        )
    }
}
