package com.cleevio.fortraders.adapter.output.paypal

import com.cleevio.fortraders.application.module.transaction.port.output.CancelSubscription
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service

@Service
@ConditionalOnProperty("fortraders.transaction.card-payment-provider", havingValue = "PAYPAL")
class PaypalCancelSubscriptionService : CancelSubscription {
    override fun invoke(subscriptionId: String): Result<Unit> {
        error("PayPal subscription cancellation is not implemented yet.")
    }
}
