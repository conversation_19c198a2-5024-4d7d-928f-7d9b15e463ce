package com.cleevio.fortraders.adapter.output.dxfeed

import com.cleevio.fortraders.adapter.output.dxfeed.constant.DxFeedSubscriptionStatus
import com.cleevio.fortraders.application.module.tradingaccount.port.output.CancelFeedSubscription
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("!test")
class CancelDxFeedSubscriptionService(
    @Value("\${integration.dx-feed.l1-subscription-id}")
    private val l1SubscriptionId: String,

    private val dxFeedConnector: DxFeedConnector,
) : CancelFeedSubscription {
    override val supportedPlatform = PlatformType.DX_TRADE

    override fun invoke(email: String): Result<Unit> = runCatching {
        val accountId = dxFeedConnector.getAccountByEmail(email = email)?.id ?: error("Account not found")
        val subscriptions = dxFeedConnector.getSubscriptions(accountId = accountId)

        subscriptions.firstOrNull { it.name == l1SubscriptionId && it.status == DxFeedSubscriptionStatus.ACTIVE }?.let {
            dxFeedConnector.cancelSubscription(
                accountId = accountId,
                subscriptionId = it.name
            )
        }
    }
}
