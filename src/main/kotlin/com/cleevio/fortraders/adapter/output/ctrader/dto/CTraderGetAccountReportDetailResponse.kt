package com.cleevio.fortraders.adapter.output.ctrader.dto

import com.cleevio.fortraders.application.common.converter.EpochMillisecondsToInstantConverter
import com.cleevio.fortraders.application.common.util.ONE_HUNDRED
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import java.math.BigDecimal
import java.time.Instant

class CTraderGetAccountReportDetailResponse(
    @JsonProperty("login") val accountId: String,
    balance: BigDecimal,
    equity: BigDecimal,
    @JsonProperty("registrationTimestamp")
    @JsonDeserialize(converter = EpochMillisecondsToInstantConverter::class) val createdAt: Instant,
) {
    val balance: BigDecimal = balance.divide(ONE_HUNDRED)
    val equity: BigDecimal = equity.divide(ONE_HUNDRED)
}
