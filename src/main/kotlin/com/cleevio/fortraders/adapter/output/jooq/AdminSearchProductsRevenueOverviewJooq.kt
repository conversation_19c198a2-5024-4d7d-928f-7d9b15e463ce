package com.cleevio.fortraders.adapter.output.jooq

import com.cleevio.fortraders.application.common.util.decimalToPercentage
import com.cleevio.fortraders.application.common.util.safeDivide
import com.cleevio.fortraders.application.common.util.toEndOfTheDay
import com.cleevio.fortraders.application.common.util.toStartOfTheDay
import com.cleevio.fortraders.application.module.report.port.output.AdminSearchProductsRevenueOverview
import com.cleevio.fortraders.application.module.report.query.AdminSearchProductsRevenueOverviewQuery
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeType
import com.cleevio.fortraders.domain.model.payout.constant.PayoutState
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import com.cleevio.fortraders.public.tables.references.ORDER
import com.cleevio.fortraders.public.tables.references.PAYOUT
import org.jooq.DSLContext
import org.jooq.impl.DSL.avg
import org.jooq.impl.DSL.coalesce
import org.jooq.impl.DSL.count
import org.jooq.impl.DSL.multiset
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDate

@Service
class AdminSearchProductsRevenueOverviewJooq(private val context: DSLContext) : AdminSearchProductsRevenueOverview {
    @Transactional(readOnly = true)
    override fun invoke(
        dateFrom: LocalDate?,
        dateTo: LocalDate?,
        challengeTypes: Set<ChallengeType>?,
    ): AdminSearchProductsRevenueOverviewQuery.Result {
        val dateFromStartOfTheDay = dateFrom?.toStartOfTheDay()
        val dateToEndOfTheDay = dateTo?.toEndOfTheDay()

        val payoutStats = multiset(
            select(
                PAYOUT.tradingAccount.order.challenge.STARTING_BALANCE,
                PAYOUT.tradingAccount.order.challengePlan.STEPS,
                PAYOUT.tradingAccount.order.challengePlan.CATEGORY,
                count(),
                sumOrZero(PAYOUT.AMOUNT_TOTAL),
                sumOrZero(PAYOUT.BONUS_AMOUNT),
                sumOrZero(PAYOUT.AMOUNT_AFTER_SPLIT),
                avg(PAYOUT.AMOUNT_TOTAL),
                coalesce(avg(PAYOUT.BONUS_AMOUNT).filterWhere(PAYOUT.BONUS_AMOUNT.isNotNull), BigDecimal.ZERO),
                avg(PAYOUT.AMOUNT_AFTER_SPLIT)
            )
                .from(PAYOUT)
                .join(PAYOUT.tradingAccount).onKey()
                .join(PAYOUT.tradingAccount.order).onKey()
                .join(PAYOUT.tradingAccount.order.challenge).onKey()
                .join(PAYOUT.tradingAccount.order.challengePlan).onKey()
                .where(
                    listOfNotNull(
                        PAYOUT.STATE.eq(PayoutState.APPROVED),
                        dateFromStartOfTheDay?.let { PAYOUT.CREATED_AT.greaterOrEqual(it) },
                        dateToEndOfTheDay?.let { PAYOUT.CREATED_AT.lessOrEqual(it) },
                        PAYOUT.tradingAccount.order.challenge.TYPE.inOrNullIfNullOrEmpty(challengeTypes)
                    )
                )
                .groupBy(
                    PAYOUT.tradingAccount.order.challengePlan.STEPS,
                    PAYOUT.tradingAccount.order.challengePlan.CATEGORY,
                    PAYOUT.tradingAccount.order.challenge.STARTING_BALANCE
                )
        )

        val transactionStats = multiset(
            select(
                ORDER.challenge.STARTING_BALANCE,
                ORDER.challengePlan.STEPS,
                ORDER.challengePlan.CATEGORY,
                sumOrZero(ORDER.transaction.AMOUNT)
            )
                .from(ORDER)
                .join(ORDER.transaction).onKey()
                .join(ORDER.challenge).onKey()
                .join(ORDER.challengePlan).onKey()
                .where(
                    listOfNotNull(
                        ORDER.transaction.TYPE.`in`(TransactionType.CHALLENGE_ORDER, TransactionType.CREDIT_USAGE),
                        ORDER.transaction.STATUS.eq(TransactionStatus.COMPLETED),
                        ORDER.PAID_AT.isNotNull,
                        dateFromStartOfTheDay?.let { ORDER.PAID_AT.greaterOrEqual(it) },
                        dateToEndOfTheDay?.let { ORDER.PAID_AT.lessOrEqual(it) },
                        ORDER.challenge.TYPE.inOrNullIfNullOrEmpty(challengeTypes)
                    )
                )
                .groupBy(
                    ORDER.challengePlan.STEPS,
                    ORDER.challengePlan.CATEGORY,
                    ORDER.challenge.STARTING_BALANCE
                ).orderBy(
                    ORDER.challengePlan.STEPS,
                    ORDER.challengePlan.CATEGORY,
                    ORDER.challenge.STARTING_BALANCE
                )
        )

        return context
            .select(
                payoutStats,
                transactionStats
            )
            .fetchSingle()
            .map { result ->
                var payoutsTotal = BigDecimal.ZERO
                var payoutsCount = 0L
                var bonusAmountsTotal = BigDecimal.ZERO
                var amountsAfterSplitTotal = BigDecimal.ZERO

                val challengeRevenueTotalByStepId = result[transactionStats]
                    .associateBy {
                        Triple(
                            it[ORDER.challenge.STARTING_BALANCE]!!,
                            it[ORDER.challengePlan.STEPS]!!,
                            it[ORDER.challengePlan.CATEGORY]!!
                        )
                    }
                    .mapValues { (_, value) ->
                        value.value4()
                    }

                val revenueTotal = challengeRevenueTotalByStepId.values.sumOf { it }

                result[payoutStats].forEach {
                    val challengePayoutsCount = it.value4()!!.toLong()
                    val challengeBonusAmountsTotal = it.value6()
                    val challengeAmountsAfterSplitTotal = it.value7()
                    val challengePayoutsTotal = it.value5()
                    payoutsTotal += challengePayoutsTotal
                    payoutsCount += challengePayoutsCount
                    bonusAmountsTotal += challengeBonusAmountsTotal
                    amountsAfterSplitTotal += challengeAmountsAfterSplitTotal
                }

                val payoutStatsByStepId = result[payoutStats].associateBy {
                    Triple(
                        it[PAYOUT.tradingAccount.order.challenge.STARTING_BALANCE]!!,
                        it[PAYOUT.tradingAccount.order.challengePlan.STEPS]!!,
                        it[PAYOUT.tradingAccount.order.challengePlan.CATEGORY]!!
                    )
                }

                val allStepIds = (challengeRevenueTotalByStepId.keys + payoutStatsByStepId.keys).toSet()

                val mappedProductRevenues = allStepIds.map { stepId ->
                    val (challengeStartingBalance, challengeSteps, challengeCategory) = stepId
                    val revenueTotalForStep = challengeRevenueTotalByStepId[stepId] ?: BigDecimal.ZERO

                    val payoutStat = payoutStatsByStepId[stepId]
                    val challengePayoutsCount = payoutStat?.value4()?.toLong() ?: 0L
                    val challengeBonusAmountsTotal = payoutStat?.value6() ?: BigDecimal.ZERO
                    val challengeAmountsAfterSplitTotal = payoutStat?.value7() ?: BigDecimal.ZERO
                    val challengePayoutsTotal = payoutStat?.value5() ?: BigDecimal.ZERO
                    val challengeAveragePayout = payoutStat?.value8() ?: BigDecimal.ZERO
                    val challengeAverageBonusAmount = payoutStat?.value9() ?: BigDecimal.ZERO
                    val challengeAverageAfterSplitAmount = payoutStat?.value10() ?: BigDecimal.ZERO

                    AdminSearchProductsRevenueOverviewQuery.ProductRevenueDetail(
                        product = AdminSearchProductsRevenueOverviewQuery.ProductDetail(
                            startingBalance = challengeStartingBalance,
                            steps = challengeSteps,
                            category = challengeCategory
                        ),
                        revenueTotal = revenueTotalForStep,
                        bonusAmountsTotal = challengeBonusAmountsTotal,
                        amountsAfterSplitTotal = challengeAmountsAfterSplitTotal,
                        payoutsTotal = challengePayoutsTotal,
                        payoutsCount = challengePayoutsCount,
                        averageBonusAmount = challengeAverageBonusAmount,
                        averageAfterSplitAmount = challengeAverageAfterSplitAmount,
                        averagePayout = challengeAveragePayout,
                        percentageOfPayoutsTotalFromRevenueTotal = challengePayoutsTotal.safeDivide(
                            revenueTotalForStep
                        ).decimalToPercentage(),
                        percentageOfAllPayouts = challengePayoutsTotal.safeDivide(payoutsTotal).decimalToPercentage()
                    )
                }

                AdminSearchProductsRevenueOverviewQuery.Result(
                    revenueTotal = revenueTotal,
                    bonusAmountsTotal = bonusAmountsTotal,
                    amountsAfterSplitTotal = amountsAfterSplitTotal,
                    payoutsTotal = payoutsTotal,
                    payoutsCount = payoutsCount,
                    averageBonusAmount = bonusAmountsTotal.safeDivide(payoutsCount.toBigDecimal()),
                    averageAfterSplitAmount = amountsAfterSplitTotal.safeDivide(payoutsCount.toBigDecimal()),
                    averagePayout = payoutsTotal.safeDivide(payoutsCount.toBigDecimal()),
                    percentageOfPayoutsTotalFromRevenueTotal = payoutsTotal.safeDivide(revenueTotal).decimalToPercentage(),
                    productRevenues = mappedProductRevenues
                )
            }
    }
}
