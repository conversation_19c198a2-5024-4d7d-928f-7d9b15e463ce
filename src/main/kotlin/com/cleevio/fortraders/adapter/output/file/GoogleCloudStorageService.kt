package com.cleevio.fortraders.adapter.output.file

import com.cleevio.fortraders.application.module.file.port.output.FileStorageService
import com.cleevio.fortraders.domain.model.file.constant.FileType
import com.google.auth.oauth2.GoogleCredentials
import com.google.cloud.storage.BlobId
import com.google.cloud.storage.BlobInfo
import com.google.cloud.storage.Storage
import com.google.cloud.storage.StorageOptions
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Service
import java.io.InputStream
import java.net.URLConnection
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.util.UUID
import java.util.concurrent.TimeUnit

@Service
@ConditionalOnProperty("fortraders.storage.type", havingValue = "GOOGLE_CLOUD_STORAGE")
class GoogleCloudStorageService(
    @Value("\${fortraders.storage.google-cloud-storage.bucket-name}") private val bucketName: String,
    @Value("\${fortraders.storage.google-cloud-storage.storage-path}") private val storagePath: String,
    private val googleCloudStorage: Storage,
) : FileStorageService {

    override fun save(fileName: String, fileType: FileType, file: InputStream) {
        val blobId = BlobId.of(bucketName, getFullBlobName(fileType = fileType, fileName = fileName))
        val blobInfo = BlobInfo.newBuilder(blobId)
            .setContentType(URLConnection.guessContentTypeFromName(fileName))
            .build()
        googleCloudStorage.create(blobInfo, file.readBytes())
    }

    override fun delete(fileName: String, fileType: FileType): Boolean {
        return googleCloudStorage.delete(bucketName, getFullBlobName(fileType = fileType, fileName = fileName))
    }

    override fun getFileUrl(fileId: UUID, fileExtension: String, fileType: FileType): String {
        val fullBlobName = getFullBlobName(fileName = "$fileId.$fileExtension", fileType = fileType)
        return if (fileType.isPrivate) {
            getSignedUrl(bucketName = bucketName, blobName = fullBlobName)
        } else {
            getPublicFirebaseUrl(bucketName = bucketName, blobName = fullBlobName)
        }
    }

    private fun getSignedUrl(bucketName: String, blobName: String): String {
        val blobUrl = googleCloudStorage.get(bucketName, blobName) ?: error("Blob not found")
        return googleCloudStorage.signUrl(
            blobUrl.asBlobInfo(),
            1,
            TimeUnit.HOURS,
            Storage.SignUrlOption.withV4Signature()
        ).toExternalForm()
    }

    private fun getFullBlobName(fileName: String, fileType: FileType): String {
        val publicPrefix = if (fileType.isPrivate) "" else "public/"
        return "$publicPrefix${fileType.name.lowercase()}/$fileName"
    }

    private fun getPublicFirebaseUrl(bucketName: String, blobName: String): String {
        val encodedBlobName = URLEncoder.encode(blobName, StandardCharsets.UTF_8)
        return "$storagePath/v0/b/$bucketName/o/$encodedBlobName?alt=media"
    }
}

@Configuration
private class GoogleCloudStorageConfig(
    @Value("\${firebase.credentials}") private val firebaseCredentials: String,
) {

    @Bean
    fun googleCloudStorage(): Storage {
        val credentials = GoogleCredentials.fromStream(firebaseCredentials.byteInputStream())
        return StorageOptions.newBuilder().setCredentials(credentials).build().service
    }
}
