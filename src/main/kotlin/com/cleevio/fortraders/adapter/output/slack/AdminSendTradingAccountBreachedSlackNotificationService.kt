package com.cleevio.fortraders.adapter.output.slack

import com.cleevio.fortraders.application.module.notification.port.output.AdminSendTradingAccountBreachedNotification
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
class AdminSendTradingAccountBreachedSlackNotificationService(
    private val slackConnector: SlackConnector,
) : AdminSendTradingAccountBreachedNotification {
    override fun invoke(
        tradingAccountId: UUID,
        accountId: String,
        daysSinceAccountCreation: Int,
        platform: PlatformType,
        challengeName: String,
        challengePlanName: String,
        breachType: BreachType,
        userFullName: String,
        email: String,
        challengeStepNumber: Int,
        challengeStepType: ChallengeStepType,
        city: String,
        postCode: String,
        countryName: String,
        countryIsoCode: String,
        userOrdersTotal: Int,
        userOrdersRevenueTotal: BigDecimal,
    ) {
        slackConnector.sendAccountBreachedMessage(
            tradingAccountId = tradingAccountId,
            accountId = accountId,
            daysSinceAccountCreation = daysSinceAccountCreation,
            platform = platform,
            challengeName = challengeName,
            challengePlanName = challengePlanName,
            breachType = breachType,
            userFullName = userFullName,
            email = email,
            challengeStepNumber = challengeStepNumber,
            challengeStepType = challengeStepType,
            city = city,
            postCode = postCode,
            countryName = countryName,
            countryIsoCode = countryIsoCode,
            userOrdersTotal = userOrdersTotal,
            userOrdersRevenueTotal = userOrdersRevenueTotal
        )
    }
}
