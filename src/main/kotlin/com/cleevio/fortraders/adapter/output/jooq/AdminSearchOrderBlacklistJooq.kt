package com.cleevio.fortraders.adapter.output.jooq

import com.cleevio.fortraders.application.common.util.trimOrNull
import com.cleevio.fortraders.application.module.orderblacklist.port.output.AdminSearchOrderBlacklist
import com.cleevio.fortraders.application.module.orderblacklist.query.AdminSearchOrderBlacklistQuery
import com.cleevio.fortraders.public.tables.references.ORDER_BLACKLIST
import org.jooq.DSLContext
import org.jooq.impl.DSL.or
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminSearchOrderBlacklistJooq(private val context: DSLContext) : AdminSearchOrderBlacklist {

    @Transactional(readOnly = true)
    override fun invoke(
        pageable: Pageable,
        userEmails: Set<String>?,
        fulltext: String?,
    ): Page<AdminSearchOrderBlacklistQuery.Result> {
        val filterConditions = listOfNotNull(
            ORDER_BLACKLIST.EMAIL.inOrNullIfNullOrEmpty(userEmails),
            fulltext.trimOrNull()?.let {
                or(
                    ORDER_BLACKLIST.EMAIL.likeIgnoreCaseWithPattern(it),
                    ORDER_BLACKLIST.ID.likeIgnoreCaseWithPattern(it)
                )
            }
        )

        val count = context.fetchCount(ORDER_BLACKLIST, filterConditions)

        return context.select(
            ORDER_BLACKLIST.ID,
            ORDER_BLACKLIST.EMAIL,
            ORDER_BLACKLIST.NOTE
        )
            .from(ORDER_BLACKLIST)
            .where(filterConditions)
            .sort(pageable.sort, ORDER_BLACKLIST)
            .page(pageable)
            .fetch()
            .map {
                AdminSearchOrderBlacklistQuery.Result(
                    id = it[ORDER_BLACKLIST.ID]!!,
                    email = it[ORDER_BLACKLIST.EMAIL]!!,
                    note = it[ORDER_BLACKLIST.NOTE]
                )
            }
            .let {
                PageImpl(it, pageable, count.toLong())
            }
    }
}
