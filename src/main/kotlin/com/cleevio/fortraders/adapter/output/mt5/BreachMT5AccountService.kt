package com.cleevio.fortraders.adapter.output.mt5

import com.cleevio.fortraders.application.module.tradingaccount.port.output.BreachTradingAccount
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("!test")
class BreachMT5AccountService(
    private val mT5Connector: MT5Connector,
) : BreachTradingAccount {
    override val supportedPlatform = PlatformType.META_TRADER_5

    override fun invoke(accountId: String): Result<Unit> = runCatching {
        mT5Connector.restrictAccount(accountId = accountId)
        mT5Connector.closeAllPositionsAndOrders(accountId = accountId)
    }
}
