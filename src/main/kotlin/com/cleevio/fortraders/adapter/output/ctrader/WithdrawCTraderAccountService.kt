package com.cleevio.fortraders.adapter.output.ctrader

import com.cleevio.fortraders.application.module.tradingaccount.port.output.WithdrawTradingAccount
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
@Profile("!test")
class WithdrawCTraderAccountService(
    private val cTraderConnector: CTraderConnector,
) : WithdrawTradingAccount {
    override val supportedPlatform = PlatformType.C_TRADER

    override fun invoke(accountId: String, amount: BigDecimal, comment: String?): Result<Unit> = runCatching {
        cTraderConnector.withdraw(
            accountId = accountId,
            amount = amount,
            comment = comment
        )
    }
}
