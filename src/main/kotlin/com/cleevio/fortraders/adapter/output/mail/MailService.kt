package com.cleevio.fortraders.adapter.output.mail

import com.cleevio.fortraders.adapter.output.mail.config.MailConfiguration
import com.cleevio.fortraders.application.common.util.replaceMailVariables
import com.cleevio.fortraders.domain.model.mailtemplate.constant.MailType
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.mail.javamail.MimeMessageHelper
import org.springframework.stereotype.Service

@Service
class MailService(
    private val mailSender: JavaMailSender,
    private val mailConfiguration: MailConfiguration,
) {
    fun sendEmail(
        email: String,
        bcc: List<String> = listOf(),
        subject: String,
        htmlBody: String,
        mailType: MailType,
        mailVariables: Map<String, Any?>,
    ) {
        mailSender.send { mimeMessage ->
            MimeMessageHelper(mimeMessage, true, "UTF-8").apply {
                setFrom(mailConfiguration.fromAddress)
                addTo(email)
                setSubject(
                    subject.replaceMailVariables(mailVariables)
                )
                setText(
                    htmlBody.replaceMailVariables(mailVariables),
                    true
                )
                if (bcc.isNotEmpty()) {
                    setBcc(bcc.toTypedArray())
                }
            }
        }
    }
}
