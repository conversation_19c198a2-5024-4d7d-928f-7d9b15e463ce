package com.cleevio.fortraders.adapter.output.tradelocker

import com.cleevio.fortraders.application.module.tradingaccount.port.output.GetAccountBalances
import com.cleevio.fortraders.application.module.tradingaccount.port.output.dto.AccountBalanceResult
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("!test")
class GetTradeLockerAccountBalancesService(
    private val tradeLockerConnector: TradeLockerConnector,
) : GetAccountBalances {
    override val supportedPlatform = PlatformType.TRADE_LOCKER

    override fun invoke(accountIds: Set<String>): Result<List<AccountBalanceResult>> = runCatching {
        tradeLockerConnector.getAccountReports(accountIds)
            .map {
                AccountBalanceResult(
                    accountId = it.accountId,
                    balance = it.balance,
                    equity = it.equity
                )
            }
            .filter { it.isValid() }
    }
}
