package com.cleevio.fortraders.adapter.output.dxfeed

import com.cleevio.fortraders.adapter.output.BaseConnector
import com.cleevio.fortraders.adapter.output.dxfeed.constant.DxFeedAllowedUserType
import com.cleevio.fortraders.adapter.output.dxfeed.dto.DxFeedActivateSubscriptionResponse
import com.cleevio.fortraders.adapter.output.dxfeed.dto.DxFeedGetAccountResponse
import com.cleevio.fortraders.adapter.output.dxfeed.dto.DxFeedGetSubscriptionsDetailResponse
import com.fasterxml.jackson.databind.SerializationFeature
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.UUID

@Component
class DxFeedConnector(
    @Value("\${integration.dx-feed.base-url}")
    baseUrl: String,

    @Value("\${integration.dx-feed.api-key}")
    private val apiKey: String,
) : BaseConnector(
    baseUrl = baseUrl,
    restClientCustomizer = {
        it.defaultHeader("Authorization", "Bearer $apiKey")
        it.messageConverters { converters ->
            converters.addFirst(
                MappingJackson2HttpMessageConverter().also { converter ->
                    converter.objectMapper
                        .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
                }
            )
        }
    }
) {
    fun createAccount(
        email: String,
        firstName: String,
        lastName: String,
        country: String?,
        address: String?,
        city: String?,
        zip: String?,
        phone: String?,
    ): UUID = restClient.post()
        .uriAndHeaders("/api/v1/accounts")
        .jsonBody(
            DxFeedCreateAccountRequest(
                email = email,
                metadata = DxFeedCreateAccountMetadataRequest(
                    firstName = firstName,
                    lastName = lastName,
                    country = country,
                    address = address,
                    city = city,
                    zip = zip,
                    phone = phone
                )
            )
        )
        .retrieveResponseWithErrorHandler<DxFeedCreateAccountResponse>()
        .accountId

    fun getAccountById(accountId: UUID): DxFeedGetAccountResponse? = restClient.get()
        .uriAndHeaders("/api/v1/accounts/$accountId")
        .retrieveResponseWithErrorHandler<DxFeedGetAccountResponse>(excludedStatusCode = HttpStatus.NOT_FOUND)

    fun getAccountByEmail(email: String): DxFeedGetAccountResponse? = restClient.get()
        .uriAndHeaders("/api/v1/accounts/by-email/$email")
        .retrieveResponseWithErrorHandler<DxFeedGetAccountResponse>(excludedStatusCode = HttpStatus.NOT_FOUND)

    fun resetPassword(accountId: UUID) {
        restClient.post()
            .uriAndHeaders("/api/v1/accounts/$accountId/reset-password")
            .retrieveWithErrorHandler()
    }

    fun changeEmail(accountId: UUID, email: String) {
        restClient.post()
            .uriAndHeaders("/api/v1/accounts/$accountId/change-email")
            .jsonBody(
                DxFeedChangeEmailRequest(
                    email = email
                )
            )
            .retrieveWithErrorHandler()
    }

    fun activateSubscription(
        accountId: UUID,
        feedName: String,
        endDate: Instant,
        successRedirectUrl: String?,
    ): DxFeedActivateSubscriptionResponse = restClient.post()
        .uriAndHeaders("/api/v2/accounts/$accountId/subscriptions")
        .jsonBody(
            DxFeedActivateSubscriptionRequest(
                subscriptions = listOf(
                    DxFeedActivateSubscriptionDetailRequest(
                        feedName = feedName,
                        endDate = endDate
                    )
                ),
                successRedirectUrl = successRedirectUrl
            )
        )
        .retrieveResponseWithErrorHandler<DxFeedActivateSubscriptionResponse>()

    fun confirmSubscription(accountId: UUID, confirmationId: UUID) {
        restClient.post()
            .uriAndHeaders("/api/v2/accounts/$accountId/subscriptions/confirm")
            .jsonBody(
                DxFeedConfirmSubscriptionRequest(
                    confirmationId = confirmationId
                )
            )
            .retrieveWithErrorHandler()
    }

    fun getSubscriptions(accountId: UUID): List<DxFeedGetSubscriptionsDetailResponse> = restClient.get()
        .uriAndHeaders("/api/v1/accounts/$accountId/subscriptions")
        .retrieveResponseWithErrorHandler<DxFeedGetSubscriptionsResponse>()
        .subscriptions

    fun cancelSubscription(accountId: UUID, subscriptionId: String) {
        restClient.post()
            .uriAndHeaders("/api/v1/accounts/$accountId/subscriptions/cancel")
            .jsonBody(
                DxFeedCancelSubscriptionRequest(
                    subscriptions = listOf(subscriptionId)
                )
            )
            .retrieveWithErrorHandler()
    }
}

private data class DxFeedCreateAccountRequest(
    val email: String,
    val metadata: DxFeedCreateAccountMetadataRequest,
    val allowedStatuses: DxFeedAllowedUserType = DxFeedAllowedUserType.ALL,
)

private data class DxFeedCreateAccountMetadataRequest(
    val firstName: String,
    val lastName: String,
    val country: String?,
    val state: String? = null,
    val address: String?,
    val city: String?,
    val zip: String?,
    val phone: String?,
    val companyName: String? = null,
    val jobTitle: String? = null,
)

private data class DxFeedCreateAccountResponse(
    val accountId: UUID,
)

private data class DxFeedChangeEmailRequest(
    val email: String,
)

private data class DxFeedActivateSubscriptionRequest(
    val subscriptions: List<DxFeedActivateSubscriptionDetailRequest>,
    val successRedirectUrl: String?,
)

private data class DxFeedActivateSubscriptionDetailRequest(
    val feedName: String,
    val endDate: Instant,
)

private data class DxFeedConfirmSubscriptionRequest(
    val confirmationId: UUID,
)

private data class DxFeedGetSubscriptionsResponse(
    val subscriptions: List<DxFeedGetSubscriptionsDetailResponse>,
)

private data class DxFeedCancelSubscriptionRequest(
    val subscriptions: List<String>,
)
