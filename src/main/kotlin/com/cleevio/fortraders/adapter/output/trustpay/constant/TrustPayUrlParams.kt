package com.cleevio.fortraders.adapter.output.trustpay.constant

class TrustPayUrlParams {
    companion object {
        const val ACCOUNT_ID = "AccountId"
        const val AMOUNT = "Amount"
        const val CURRENCY = "Currency"
        const val REFERENCE = "Reference"
        const val PAYMENT_TYPE = "PaymentType"
        const val BILLING_CITY = "BillingCity"
        const val BILLING_COUNTRY = "BillingCountry"
        const val BILLING_POSTCODE = "BillingPostcode"
        const val BILLING_STREET = "BillingStreet"
        const val CARD_HOLDER = "CardHolder"
        const val EMAIL = "Email"
        const val SIGNATURE = "Signature"
        const val RESULT_CODE = "ResultCode"
        const val TYPE = "Type"
        const val PAYMENT_REQUEST_ID = "PaymentRequestId"
        const val CARD_ID = "CardId"
        const val CARD_MASK = "CardMask"
        const val CARD_EXPIRATION = "CardExpiration"
        const val AUTH_NUMBER = "AuthNumber"
    }
}
