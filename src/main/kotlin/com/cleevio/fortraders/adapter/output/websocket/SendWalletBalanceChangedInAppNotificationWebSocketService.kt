package com.cleevio.fortraders.adapter.output.websocket

import com.cleevio.fortraders.adapter.output.websocket.constant.WebSocketMessageType
import com.cleevio.fortraders.adapter.output.websocket.dto.WebSocketMessage
import com.cleevio.fortraders.adapter.output.websocket.dto.WebSocketWalletBalanceChangedResponse
import com.cleevio.fortraders.application.module.notification.port.output.SendWalletBalanceChangedInAppNotification
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

@Service
class SendWalletBalanceChangedInAppNotificationWebSocketService(
    private val webSocketMessagingService: WebSocketMessagingService,
) : SendWalletBalanceChangedInAppNotification {

    override fun invoke(userId: UUID, balance: BigDecimal, updatedAt: Instant) {
        webSocketMessagingService.sendMessageToUser(
            user = userId.toString(),
            destination = "/queue/info",
            message = WebSocketMessage(
                type = WebSocketMessageType.WALLET_BALANCE_CHANGED,
                data = WebSocketWalletBalanceChangedResponse(
                    balance = balance,
                    updatedAt = updatedAt
                )
            )
        )
    }
}
