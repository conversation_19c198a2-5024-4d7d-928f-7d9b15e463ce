package com.cleevio.fortraders.adapter.output.tap

import com.cleevio.fortraders.application.module.transaction.port.output.CreatePaymentIntent
import com.cleevio.fortraders.application.module.transaction.port.output.CreatePaymentIntentResult
import com.cleevio.fortraders.domain.model.transaction.constant.Currency
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionPaymentMethod
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
@ConditionalOnProperty("fortraders.transaction.card-payment-provider", havingValue = "TAP")
class TapCreatePaymentIntentService(
    private val tapConnector: TapConnector,
) : CreatePaymentIntent {
    override val paymentMethod = TransactionPaymentMethod.CREDIT_CARD

    override fun invoke(
        transactionId: UUID,
        amount: BigDecimal,
        currency: Currency,
        city: String,
        countryIsoCode: String,
        postCode: String,
        street: String,
        email: String,
        firstName: String?,
        lastName: String?,
        isSubscription: Boolean,
        fullPhoneNumber: String?,
    ): Result<CreatePaymentIntentResult> = runCatching {
        val chargeId = tapConnector.createCharge(
            transactionId = transactionId,
            amount = amount,
            currency = currency,
            email = email,
            firstName = requireNotNull(firstName),
            lastName = requireNotNull(lastName),
            street = street,
            city = city,
            countryIsoCode = countryIsoCode,
            postCode = postCode,
            fullPhoneNumber = fullPhoneNumber
        )

        CreatePaymentIntentResult(
            paymentId = chargeId,
            paymentSecret = null,
            paymentGatewayProvider = PaymentGatewayProvider.TAP
        )
    }
}
