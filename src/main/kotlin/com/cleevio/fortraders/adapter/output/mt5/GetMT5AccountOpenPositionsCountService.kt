package com.cleevio.fortraders.adapter.output.mt5

import com.cleevio.fortraders.application.module.tradingaccount.port.output.GetTradingAccountOpenPositionsCount
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("!test")
class GetMT5AccountOpenPositionsCountService(
    private val mt5Connector: MT5Connector,
) : GetTradingAccountOpenPositionsCount() {
    override val supportedPlatform = PlatformType.META_TRADER_5

    override fun invoke(accountId: String): Int = mt5Connector.getOpenPositions(accountId = accountId).size

    override fun invoke(accountIds: Set<String>): Result<Map<String, Int>> = runCatching {
        mt5Connector.getNumberOfOpenPositionsByAccountIds(
            accountIds = accountIds
        )
    }
}
