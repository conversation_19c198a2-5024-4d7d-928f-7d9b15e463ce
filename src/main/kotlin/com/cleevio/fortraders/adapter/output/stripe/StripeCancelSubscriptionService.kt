package com.cleevio.fortraders.adapter.output.stripe

import com.cleevio.fortraders.application.module.transaction.port.output.CancelSubscription
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service

@Service
@ConditionalOnProperty("fortraders.transaction.card-payment-provider", havingValue = "STRIPE")
class StripeCancelSubscriptionService : CancelSubscription {
    override fun invoke(subscriptionId: String): Result<Unit> {
        error("Stripe subscription cancellation is not implemented yet.")
    }
}
