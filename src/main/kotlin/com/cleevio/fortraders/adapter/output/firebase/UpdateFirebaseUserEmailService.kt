package com.cleevio.fortraders.adapter.output.firebase

import com.cleevio.fortraders.application.module.user.port.output.UpdateFirebaseUserEmail
import org.springframework.stereotype.Service

@Service
class UpdateFirebaseUserEmailService(
    private val firebaseService: FirebaseService,
) : UpdateFirebaseUserEmail {
    override fun invoke(firebaseId: String, email: String): Result<Unit> = runCatching {
        firebaseService.updateUserEmail(
            firebaseId = firebaseId,
            email = email
        )
    }
}
