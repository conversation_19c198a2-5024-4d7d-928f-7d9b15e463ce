package com.cleevio.fortraders.adapter.output.ctrader

import com.cleevio.fortraders.application.module.tradingaccount.port.output.UpdateTradingAccountGroup
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("!test")
class UpdateCTraderAccountGroupService(
    private val cTraderConnector: CTraderConnector,
) : UpdateTradingAccountGroup {
    override val supportedPlatform = PlatformType.C_TRADER

    override fun invoke(accountId: String, tradingGroup: String): Result<Unit> = runCatching {
        cTraderConnector.updateTradingGroup(
            accountId = accountId,
            tradingGroup = tradingGroup
        )
    }
}
