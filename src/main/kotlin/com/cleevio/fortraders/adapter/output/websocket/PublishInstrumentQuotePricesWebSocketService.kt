package com.cleevio.fortraders.adapter.output.websocket

import com.cleevio.fortraders.adapter.output.websocket.constant.WebSocketMessageType
import com.cleevio.fortraders.adapter.output.websocket.dto.WebSocketInstrumentQuotePriceResponse
import com.cleevio.fortraders.adapter.output.websocket.dto.WebSocketMessage
import com.cleevio.fortraders.application.module.instrument.port.output.PublishInstrumentQuotePrices
import com.cleevio.fortraders.application.module.instrument.port.output.model.InstrumentQuotePriceModel
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class PublishInstrumentQuotePricesWebSocketService(
    private val webSocketMessagingService: WebSocketMessagingService,
) : PublishInstrumentQuotePrices {

    override fun invoke(tournamentId: UUID, instrumentPrices: List<InstrumentQuotePriceModel>) {
        webSocketMessagingService.sendMessage(
            destination = "/topic/tournaments/$tournamentId",
            message = WebSocketMessage(
                type = WebSocketMessageType.INSTRUMENT_PRICES_CHANGE,
                data = instrumentPrices.map {
                    WebSocketInstrumentQuotePriceResponse(
                        instrumentId = it.instrumentId,
                        instrumentType = it.instrumentType,
                        ticker = it.ticker,
                        askPrice = it.askPrice,
                        bidPrice = it.bidPrice,
                        timestamp = it.timestamp
                    )
                }
            )
        )
    }
}
