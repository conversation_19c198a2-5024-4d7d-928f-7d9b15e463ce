package com.cleevio.fortraders.adapter.output.jooq

import com.cleevio.fortraders.application.common.util.trimOrNull
import com.cleevio.fortraders.application.module.affiliatepayout.port.output.AdminSearchAffiliatePayouts
import com.cleevio.fortraders.application.module.affiliatepayout.query.AdminSearchAffiliatePayoutsQuery
import com.cleevio.fortraders.domain.model.affiliatepayout.constant.AffiliatePayoutState
import com.cleevio.fortraders.public.tables.references.AFFILIATE_PAYOUT
import org.jooq.DSLContext
import org.jooq.impl.DSL.or
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Service
class AdminSearchAffiliatePayoutsJooq(private val context: DSLContext) : AdminSearchAffiliatePayouts {

    @Transactional(readOnly = true)
    override fun invoke(
        pageable: Pageable,
        createdAtFrom: Instant?,
        createdAtTo: Instant?,
        states: Set<AffiliatePayoutState>?,
        fulltext: String?,
    ): Page<AdminSearchAffiliatePayoutsQuery.Result> {
        val filterConditions = listOfNotNull(
            AFFILIATE_PAYOUT.STATE.inOrNullIfNullOrEmpty(states),
            createdAtFrom?.let { AFFILIATE_PAYOUT.CREATED_AT.greaterOrEqual(it) },
            createdAtTo?.let { AFFILIATE_PAYOUT.CREATED_AT.lessOrEqual(it) },
            fulltext.trimOrNull()?.let {
                or(
                    AFFILIATE_PAYOUT.ID.likeIgnoreCaseWithPattern(it),
                    AFFILIATE_PAYOUT.affiliate.user.FULL_NAME.likeIgnoreCaseWithPattern(it),
                    AFFILIATE_PAYOUT.affiliate.user.EMAIL.likeIgnoreCaseWithPattern(it)
                )
            }
        )

        val count = context.fetchCount(AFFILIATE_PAYOUT, filterConditions)
        val isUserEmailOrderBlacklisted = isUserEmailOrderBlacklisted(AFFILIATE_PAYOUT.affiliate.user.EMAIL)

        return context.select(
            AFFILIATE_PAYOUT.ID,
            AFFILIATE_PAYOUT.STATE,
            AFFILIATE_PAYOUT.AMOUNT,
            AFFILIATE_PAYOUT.CREATED_AT,
            AFFILIATE_PAYOUT.UPDATED_AT,
            AFFILIATE_PAYOUT.affiliate.ID,
            AFFILIATE_PAYOUT.affiliate.STATE,
            AFFILIATE_PAYOUT.affiliate.COMMISSION_BALANCE,
            AFFILIATE_PAYOUT.affiliate.user.ID,
            AFFILIATE_PAYOUT.affiliate.user.FIRST_NAME,
            AFFILIATE_PAYOUT.affiliate.user.LAST_NAME,
            AFFILIATE_PAYOUT.affiliate.user.EMAIL,
            isUserEmailOrderBlacklisted
        )
            .from(AFFILIATE_PAYOUT)
            .where(filterConditions)
            .sort(pageable.sort, AFFILIATE_PAYOUT)
            .page(pageable)
            .fetch()
            .map {
                AdminSearchAffiliatePayoutsQuery.Result(
                    id = it[AFFILIATE_PAYOUT.ID]!!,
                    state = it[AFFILIATE_PAYOUT.STATE]!!,
                    amount = it[AFFILIATE_PAYOUT.AMOUNT]!!,
                    createdAt = it[AFFILIATE_PAYOUT.CREATED_AT]!!,
                    updatedAt = it[AFFILIATE_PAYOUT.UPDATED_AT]!!,
                    user = AdminSearchAffiliatePayoutsQuery.UserDetail(
                        id = it[AFFILIATE_PAYOUT.affiliate.user.ID]!!,
                        email = it[AFFILIATE_PAYOUT.affiliate.user.EMAIL]!!,
                        firstName = it[AFFILIATE_PAYOUT.affiliate.user.FIRST_NAME],
                        lastName = it[AFFILIATE_PAYOUT.affiliate.user.LAST_NAME],
                        blacklisted = it[isUserEmailOrderBlacklisted]!!,
                        affiliate = AdminSearchAffiliatePayoutsQuery.AffiliateDetail(
                            id = it[AFFILIATE_PAYOUT.affiliate.ID]!!,
                            state = it[AFFILIATE_PAYOUT.affiliate.STATE]!!,
                            commissionBalance = it[AFFILIATE_PAYOUT.affiliate.COMMISSION_BALANCE]!!
                        )
                    )
                )
            }
            .let {
                PageImpl(it, pageable, count.toLong())
            }
    }
}
