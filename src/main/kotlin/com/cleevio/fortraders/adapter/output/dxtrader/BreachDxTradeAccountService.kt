package com.cleevio.fortraders.adapter.output.dxtrader

import com.cleevio.fortraders.application.module.tradingaccount.port.output.BreachTradingAccount
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("!test")
class BreachDxTradeAccountService(
    private val dxTradeTradingConnector: DxTradeTradingConnector,
    private val dxTradeAdministrativeConnector: DxTradeAdministrativeConnector,
) : BreachTradingAccount {
    override val supportedPlatform = PlatformType.DX_TRADE

    override fun invoke(accountId: String): Result<Unit> = runCatching {
        dxTradeTradingConnector.closeAllPositionsAndOpenOrders(accountId = accountId)
        dxTradeAdministrativeConnector.restrictAccount(accountId = accountId)
    }
}
