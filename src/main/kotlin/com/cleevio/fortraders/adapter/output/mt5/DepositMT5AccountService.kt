package com.cleevio.fortraders.adapter.output.mt5

import com.cleevio.fortraders.application.module.tradingaccount.port.output.DepositTradingAccount
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
@Profile("!test")
class DepositMT5AccountService(private val mt5Connector: MT5Connector) : DepositTradingAccount {
    override val supportedPlatform = PlatformType.META_TRADER_5

    override fun invoke(accountId: String, amount: BigDecimal, comment: String?): Result<Unit> = runCatching {
        mt5Connector.deposit(
            accountId = accountId,
            amount = amount,
            comment = comment
        )
    }
}
