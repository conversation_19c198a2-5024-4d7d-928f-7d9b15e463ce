package com.cleevio.fortraders.adapter.output.websocket.dto

import com.cleevio.fortraders.domain.model.tournamenttrade.constant.TournamentTradeType
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class WebSocketTournamentTradeClosedResponse(
    val instrumentId: UUID,
    val firstName: String?,
    val lastName: String?,
    val type: TournamentTradeType,
    val closePrice: BigDecimal,
    val profitPercentage: BigDecimal,
    val leverage: Int,
    val createdAt: Instant,
    val closedAt: Instant,
)
