package com.cleevio.fortraders.adapter.output.websocket

import com.cleevio.fortraders.adapter.output.websocket.dto.WebSocketMessage
import com.cleevio.fortraders.infrastructure.config.logger
import org.springframework.messaging.simp.SimpMessagingTemplate
import org.springframework.stereotype.Service

@Service
class WebSocketMessagingService(
    private val simpMessagingTemplate: SimpMessagingTemplate,
) {
    private val logger = logger()

    init {
        simpMessagingTemplate.sendTimeout = 1000L
    }

    fun sendMessage(destination: String, message: WebSocketMessage<*>) {
        runCatching {
            simpMessagingTemplate.convertAndSend(destination, message)
        }.onFailure { logger.error("Failed to send message to $destination", it) }
    }

    fun sendMessageToUser(user: String, destination: String, message: WebSocketMessage<*>) {
        runCatching {
            simpMessagingTemplate.convertAndSendToUser(user, destination, message)
        }.onFailure { logger.error("Failed to send user message to $destination", it) }
    }
}
