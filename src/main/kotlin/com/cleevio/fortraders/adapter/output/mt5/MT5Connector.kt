package com.cleevio.fortraders.adapter.output.mt5

import com.cleevio.fortraders.adapter.output.BaseConnector
import com.cleevio.fortraders.adapter.output.mt5.dto.MT5GetAccountDetailResponse
import com.cleevio.fortraders.adapter.output.mt5.dto.MT5GetDealResponse
import com.cleevio.fortraders.adapter.output.mt5.dto.MT5GetOpenPositionResponse
import com.cleevio.fortraders.adapter.output.mt5.dto.MT5GetOrderResponse
import com.cleevio.fortraders.domain.model.trade.constant.TradeSide
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.DeserializationFeature
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.MediaType
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.retry.annotation.Retryable
import org.springframework.stereotype.Component
import org.springframework.web.client.RestClient
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit

@Component
class MT5Connector(
    @Value("\${integration.mt5.base-url}")
    baseUrl: String,

    @Value("\${integration.mt5.username}")
    private val userName: String,

    @Value("\${integration.mt5.password}")
    private val password: String,
) : BaseConnector(
    baseUrl = baseUrl,
    restClientCustomizer = mt5RestClientCustomizer
) {
    private val tradingGroups: List<String> by lazy { loadGroups(loadGroupsTotal()) }

    @Retryable
    fun getAccountIdsWithClosingDealsFrom(startingFrom: Instant): Set<String> = executeWithLogin {
        restClient.get()
            .uri {
                it.path("/api/deal/get_batch")
                    .queryParam("group", tradingGroups.joinToString(","))
                    .queryParam("from", startingFrom.epochSecond)
                    .queryParam("to", Instant.now().plus(1L, ChronoUnit.DAYS).epochSecond)
                    .build()
            }
            .accept(MediaType.APPLICATION_JSON)
            .retrieveWrappedContent<List<MT5GetDealMinimalResponse>>()
            .asSequence()
            .filter { it.entry.isClosingEntry() }
            .map { it.accountId }
            .toSet()
    }

    @Retryable
    fun getOpeningOrClosingDeals(accountIds: Set<String>): List<MT5GetDealResponse> = executeWithLogin {
        restClient.post()
            .uriAndHeaders(
                path = "/api/deal/get_batch",
                queryParams = mapOf(
                    "from" to listOf(1L),
                    "to" to listOf(Instant.now().plus(1L, ChronoUnit.DAYS).epochSecond)
                )
            )
            .jsonBody(
                MT5AccountIdsFilterRequest(
                    accountIds = accountIds
                )
            )
            .retrieveWrappedContent<List<MT5GetDealResponse>>()
            .filter { (it.action == BUY_ACTION || it.action == SELL_ACTION) && it.accountId in accountIds }
    }

    @Retryable
    fun getNumberOfOpenPositionsByAccountIds(accountIds: Set<String>): Map<String, Int> =
        getOpenPositionsForAccounts<MT5GetOpenPositionMinimalResponse>(accountIds = accountIds)
            .filter { it.accountId in accountIds }
            .groupingBy { it.accountId }
            .eachCount()

    @Retryable
    fun getOpenPositions(accountId: String): List<MT5GetOpenPositionResponse> =
        getOpenPositionsForAccounts<MT5GetOpenPositionResponse>(accountIds = setOf(accountId))
            .filter { it.accountId == accountId }

    @Retryable
    fun closeAllPositionsAndOrders(accountId: String) {
        getOrders(accountId).forEach {
            closeOrder(
                accountId = accountId,
                orderId = it.orderId,
                symbol = it.symbol,
                type = it.type
            )
        }
        getOpenPositions(accountId).forEach {
            closePosition(
                accountId = accountId,
                positionId = it.positionId,
                volume = it.volume,
                side = it.tradeSide.toOpposite(),
                symbol = it.symbol,
                price = it.price
            )
        }
    }

    @Retryable
    fun closePosition(accountId: String, positionId: String, symbol: String, volume: Long, side: TradeSide, price: BigDecimal) {
        executeWithLogin {
            restClient.post()
                .uri {
                    it.path("/api/dealer/send_request")
                        .build()
                }
                .accept(MediaType.APPLICATION_JSON)
                .body(
                    MT5ClosePositionRequest(
                        positionId = positionId,
                        login = accountId,
                        symbol = symbol,
                        volume = volume.toString(),
                        type = side.toAction().toString(),
                        price = price
                    )
                )
                .callAndVerifyReturnCode()
        }
    }

    @Retryable
    fun closeOrder(accountId: String, orderId: String, symbol: String, type: Int) {
        executeWithLogin {
            restClient.post()
                .uri {
                    it.path("/api/dealer/send_request")
                        .build()
                }
                .accept(MediaType.APPLICATION_JSON)
                .body(
                    MT5CloseOrderRequest(
                        orderId = orderId,
                        login = accountId,
                        symbol = symbol,
                        type = type
                    )
                )
                .callAndVerifyReturnCode()
        }
    }

    @Retryable
    fun getOrders(accountId: String): List<MT5GetOrderResponse> = executeWithLogin {
        restClient.get()
            .uri {
                it.path("/api/order/get_batch")
                    .queryParam("login", accountId)
                    .build()
            }
            .accept(MediaType.APPLICATION_JSON)
            .retrieveWrappedContent<List<MT5GetOrderResponse>>()
            .filter { it.accountId == accountId }
    }

    @Retryable
    fun createAccount(
        login: String,
        name: String,
        password: String,
        leverage: Int,
        comment: String?,
        tradingGroup: String,
    ): String = executeWithLogin {
        restClient.get()
            .uri {
                it.path("/api/user/add")
                    .queryParam("login", login)
                    .queryParam("pass_main", password)
                    .queryParam("pass_investor", password)
                    .queryParam("group", tradingGroup)
                    .queryParam("name", name)
                    .queryParam("leverage", leverage)

                comment?.let { _ ->
                    it.queryParam("comment", comment)
                }

                it.build()
            }
            .accept(MediaType.APPLICATION_JSON)
            .retrieveWrappedContent<MT5CreateUserResponse>()
            .login
    }

    @Retryable
    fun updateTradingGroup(accountId: String, tradingGroup: String) {
        updateAccount(accountId = accountId, tradingGroup = tradingGroup)
    }

    fun deposit(accountId: String, amount: BigDecimal, comment: String?) {
        executeWithLogin {
            restClient.get()
                .uri {
                    it.path("/api/trade/balance")
                        .queryParam("type", 2) // deposit
                        .queryParam("login", accountId)
                        .queryParam("balance", amount)

                    comment?.let { _ ->
                        it.queryParam("comment", comment)
                    }

                    it.build()
                }
                .accept(MediaType.APPLICATION_JSON)
                .callAndVerifyReturnCode()
        }
    }

    @Retryable
    fun restrictAccount(accountId: String) {
        updateAccount(accountId = accountId, rights = RESTRICTED_ACCOUNT_RIGHTS)
    }

    @Retryable
    fun activateAccount(accountId: String) {
        updateAccount(accountId = accountId, rights = NORMAL_ACCOUNT_RIGHTS)
    }

    @Retryable
    fun getGroups(): List<String> = tradingGroups

    @Retryable
    fun getAccountDetails(accountIds: Set<String>): List<MT5GetAccountDetailResponse> = executeWithLogin {
        restClient
            .get()
            .uri {
                it.path("/api/user/account/get_batch")
                    .queryParam("login", accountIds.joinToString(","))
                    .build()
            }
            .accept(MediaType.APPLICATION_JSON)
            .retrieveWrappedContent<List<MT5GetAccountDetailResponse>>()
            .filter { it.accountId in accountIds }
    }

    fun login() {
        val authStartResponse = startAuth()
        completeAuth(
            serverRandomAnswer = hashServerRandomAnswer(
                password = password,
                serverRandom = authStartResponse.serverRandom
            ),
            cliRandom = generateCliRandom()
        )
    }

    private inline fun <reified T> getOpenPositionsForAccounts(accountIds: Set<String>): List<T> = executeWithLogin {
        restClient.post()
            .uriAndHeaders("/api/position/get_batch")
            .jsonBody(
                MT5AccountIdsFilterRequest(
                    accountIds = accountIds
                )
            )
            .retrieveWrappedContent<List<T>>()
    }

    private fun loadGroups(groupsTotal: Long): List<String> = executeWithLogin {
        restClient.get()
            .uri {
                it.path("/api/group/next")
                    .queryParam("index", 0)
                    .queryParam("count", groupsTotal)
                    .build()
            }
            .accept(MediaType.APPLICATION_JSON)
            .retrieveWrappedContent<List<MT5GroupResponse>>()
            .map { it.group }
    }

    private fun loadGroupsTotal(): Long = executeWithLogin {
        restClient
            .get()
            .uri {
                it.path("/api/group/total")
                    .build()
            }
            .accept(MediaType.APPLICATION_JSON)
            .retrieveWrappedContent<MT5GroupTotalResponse>()
            .group
    }

    private fun updateAccount(accountId: String, rights: Int? = null, tradingGroup: String? = null) {
        executeWithLogin {
            restClient.get()
                .uri {
                    it.path("/api/user/update")
                        .queryParam("login", accountId)

                    rights?.let { _ ->
                        it.queryParam("rights", rights)
                    }

                    tradingGroup?.let { _ ->
                        it.queryParam("group", tradingGroup)
                    }

                    it.build()
                }
                .accept(MediaType.APPLICATION_JSON)
                .callAndVerifyReturnCode()
        }
    }

    private fun startAuth(): MT5AuthStartResponse = restClient.get()
        .uri {
            it.path("/api/auth/start")
                .queryParam("version", "4040")
                .queryParam("agent", "test")
                .queryParam("type", "manager")
                .queryParam("login", userName)
                .build()
        }
        .accept(MediaType.APPLICATION_JSON)
        .retrieveResponseWithErrorHandler<MT5AuthStartResponse>()

    private fun completeAuth(serverRandomAnswer: String, cliRandom: String): MT5AuthAnswerResponse = restClient.get()
        .uri {
            it.path("/api/auth/answer")
                .queryParam("srv_rand_answer", serverRandomAnswer)
                .queryParam("cli_rand", cliRandom)
                .build()
        }
        .accept(MediaType.APPLICATION_JSON)
        .retrieveResponseWithErrorHandler<MT5AuthAnswerResponse>()

    private fun <T> executeWithLogin(logic: () -> T): T = executeWithRetry(
        retryOnStatusCode = 403,
        retryLogic = { login() },
        logic = logic
    )

    private fun RestClient.RequestHeadersSpec<*>.callAndVerifyReturnCode() {
        retrieveResponseWithErrorHandler<MT5IgnoredResponse>().let {
            if (it.returnCode != SUCCESS_RETURN_CODE) {
                error("Request failed with return code: ${it.returnCode}")
            }
        }
    }

    private inline fun <reified C> RestClient.RequestHeadersSpec<*>.retrieveWrappedContent(): C =
        retrieveResponseWithErrorHandler<MT5ResponseWrapper<C>>()
            .let {
                if (it.returnCode != SUCCESS_RETURN_CODE) {
                    error("Request failed with return code: ${it.returnCode}")
                }
                return it.content ?: error("Response content is null")
            }
}

private data class MT5ResponseWrapper<T>(
    @JsonProperty("retcode") val returnCode: String,
    @JsonProperty("answer") val content: T?,
)

private data class MT5IgnoredResponse(
    @JsonProperty("retcode") val returnCode: String,
)

private data class MT5AuthStartResponse(
    @JsonProperty("retcode") val returnCode: String,
    @JsonProperty("version_access") val versionAccess: String,
    @JsonProperty("srv_rand") val serverRandom: String,
)

private data class MT5AuthAnswerResponse(
    @JsonProperty("retcode") val returnCode: String,
    @JsonProperty("cli_rand_answer") val cliRandomAnswer: String,
)

private data class MT5AccountIdsFilterRequest(
    @JsonProperty("login") val accountIds: Set<String>,
)

private data class MT5CreateUserResponse(
    @JsonProperty("Login") val login: String,
)

private data class MT5GroupResponse(
    @JsonProperty("Group") val group: String,
)

private data class MT5GroupTotalResponse(
    @JsonProperty("total") val group: Long,
)

private data class MT5ClosePositionRequest(
    @JsonProperty("Position") val positionId: String,
    @JsonProperty("Login") val login: String,
    @JsonProperty("Symbol") val symbol: String,
    @JsonProperty("Volume") val volume: String,
    @JsonProperty("Type") val type: String,
    @JsonProperty("PriceOrder") val price: BigDecimal,
    @JsonProperty("Action") val action: String = "200",
)

private data class MT5CloseOrderRequest(
    @JsonProperty("Order") val orderId: String,
    @JsonProperty("Login") val login: String,
    @JsonProperty("Symbol") val symbol: String,
    @JsonProperty("Type") val type: Int,
    @JsonProperty("Action") val action: String = "204",
)

private data class MT5GetDealMinimalResponse(
    @JsonProperty("Login") val accountId: String,
    @JsonProperty("Entry") val entry: Int,
)

private data class MT5GetOpenPositionMinimalResponse(
    @JsonProperty("Login") val accountId: String,
)

private val mt5RestClientCustomizer: (RestClient.Builder) -> Unit = { builder ->
    builder.messageConverters {
        it.addFirst(
            MappingJackson2HttpMessageConverter().also { converter ->
                converter.objectMapper
                    .configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true)
                    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            }
        )
    }
}

private const val SUCCESS_RETURN_CODE = "0 Done"
private const val NORMAL_ACCOUNT_RIGHTS = 0x963
private const val RESTRICTED_ACCOUNT_RIGHTS = 0x907

private const val BUY_ACTION = 0
private const val SELL_ACTION = 1
