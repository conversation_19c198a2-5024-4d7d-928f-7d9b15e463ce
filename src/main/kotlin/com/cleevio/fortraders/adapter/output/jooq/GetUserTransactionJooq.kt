package com.cleevio.fortraders.adapter.output.jooq

import com.cleevio.fortraders.application.module.transaction.port.output.GetUserTransaction
import com.cleevio.fortraders.application.module.transaction.query.GetUserTransactionQuery
import com.cleevio.fortraders.domain.model.transaction.exception.TransactionNotFoundException
import com.cleevio.fortraders.public.tables.references.TRANSACTION
import org.jooq.DSLContext
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class GetUserTransactionJooq(private val context: DSLContext) : GetUserTransaction {

    @Transactional(readOnly = true)
    override fun invoke(userId: UUID, transactionId: UUID): GetUserTransactionQuery.Result {
        return context
            .select(
                TRANSACTION.ID,
                TRANSACTION.TYPE,
                TRANSACTION.AMOUNT,
                TRANSACTION.STATUS,
                TRANSACTION.NOTE,
                TRANSACTION.CREATED_AT,
                TRANSACTION.UPDATED_AT,
                TRANSACTION.order.ID,
                TRANSACTION.order.CHALLENGE_PLAN_ID,
                TRANSACTION.order.PRODUCT_NAME,
                TRANSACTION.order.PRICE,
                TRANSACTION.order.STARTING_PRICE,
                TRANSACTION.order.discountCode.CODE
            )
            .from(TRANSACTION)
            .join(TRANSACTION.wallet).onKey()
            .leftJoin(TRANSACTION.order).onKey()
            .leftJoin(TRANSACTION.order.discountCode).onKey()
            .where(
                TRANSACTION.wallet.USER_ID.eq(userId),
                TRANSACTION.ID.eq(transactionId)
            )
            .fetchOne()
            ?.map {
                GetUserTransactionQuery.Result(
                    id = it[TRANSACTION.ID]!!,
                    type = it[TRANSACTION.TYPE]!!,
                    amount = it[TRANSACTION.AMOUNT]!!,
                    status = it[TRANSACTION.STATUS]!!,
                    note = it[TRANSACTION.NOTE],
                    createdAt = it[TRANSACTION.CREATED_AT]!!,
                    updatedAt = it[TRANSACTION.UPDATED_AT]!!,
                    order = it[TRANSACTION.order.ID]?.let { _ ->
                        GetUserTransactionQuery.OrderDetail(
                            id = it[TRANSACTION.order.ID]!!,
                            challengePlanId = it[TRANSACTION.order.CHALLENGE_PLAN_ID]!!,
                            productName = it[TRANSACTION.order.PRODUCT_NAME]!!,
                            startingPrice = it[TRANSACTION.order.STARTING_PRICE]!!,
                            price = it[TRANSACTION.order.PRICE]!!,
                            discountCodeUsed = it[TRANSACTION.order.discountCode.CODE]
                        )
                    }
                )
            } ?: throw TransactionNotFoundException()
    }
}
