package com.cleevio.fortraders.adapter.output.jooq

import com.cleevio.fortraders.application.module.instrument.port.output.AdminSearchInstruments
import com.cleevio.fortraders.application.module.instrument.query.AdminSearchInstrumentsQuery
import com.cleevio.fortraders.domain.model.instrument.constant.InstrumentType
import com.cleevio.fortraders.public.tables.references.INSTRUMENT
import org.jooq.DSLContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminSearchInstrumentsJooq(private val context: DSLContext) : AdminSearchInstruments {

    @Transactional(readOnly = true)
    override fun invoke(pageable: Pageable, types: Set<InstrumentType>?): Page<AdminSearchInstrumentsQuery.Result> {
        val filterConditions = listOfNotNull(
            INSTRUMENT.TYPE.inOrNullIfNullOrEmpty(types),
            INSTRUMENT.DELETED_AT.isNull
        )
        val count = context.fetchCount(INSTRUMENT, filterConditions)

        return context.select(
            INSTRUMENT.ID,
            INSTRUMENT.TYPE,
            INSTRUMENT.NAME,
            INSTRUMENT.TICKER,
            INSTRUMENT.SPREAD_PERCENTAGE,
            INSTRUMENT.CREATED_AT,
            INSTRUMENT.UPDATED_AT
        )
            .from(INSTRUMENT)
            .where(filterConditions)
            .sort(pageable.sort, INSTRUMENT)
            .page(pageable)
            .fetch()
            .map {
                AdminSearchInstrumentsQuery.Result(
                    id = it[INSTRUMENT.ID]!!,
                    type = it[INSTRUMENT.TYPE]!!,
                    name = it[INSTRUMENT.NAME]!!,
                    ticker = it[INSTRUMENT.TICKER]!!,
                    spreadPercentage = it[INSTRUMENT.SPREAD_PERCENTAGE]!!,
                    createdAt = it[INSTRUMENT.CREATED_AT]!!,
                    updatedAt = it[INSTRUMENT.UPDATED_AT]!!
                )
            }
            .let {
                PageImpl(it, pageable, count.toLong())
            }
    }
}
