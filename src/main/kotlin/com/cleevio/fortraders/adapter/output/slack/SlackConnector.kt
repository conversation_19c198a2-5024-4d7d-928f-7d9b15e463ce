package com.cleevio.fortraders.adapter.output.slack

import com.cleevio.fortraders.adapter.output.RateLimiter
import com.cleevio.fortraders.application.common.service.AppUrlService
import com.cleevio.fortraders.application.common.util.formatUSD
import com.cleevio.fortraders.domain.model.breach.constant.BreachType
import com.cleevio.fortraders.domain.model.challengestep.constant.ChallengeStepType
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.infrastructure.config.logger
import com.slack.api.Slack
import com.slack.api.methods.MethodsClient
import com.slack.api.methods.request.chat.ChatPostMessageRequest
import com.slack.api.model.block.Blocks.divider
import com.slack.api.model.block.Blocks.section
import com.slack.api.model.block.SectionBlock.SectionBlockBuilder
import com.slack.api.model.block.composition.BlockCompositions.markdownText
import org.redisson.api.RedissonClient
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Duration
import java.util.UUID

@Component
class SlackConnector(
    redissonClient: RedissonClient,

    @Value("\${integration.slack.bot-token}")
    slackBotToken: String,

    @Value("\${integration.slack.orders-channel-id}")
    private val slackOrdersChannelId: String,

    @Value("\${integration.slack.funded-account-channel-id}")
    private val slackFundedAccountChannelId: String,

    @Value("\${integration.slack.payouts-channel-id}")
    private val slackPayoutsChannelId: String,

    @Value("\${integration.slack.affiliate-payouts-channel-id}")
    private val slackAffiliatePayoutsChannelId: String,

    @Value("\${integration.slack.account-breach-channel-id}")
    private val slackAccountBreachChannelId: String,

    private val appUrlService: AppUrlService,
) : RateLimiter(
    redissonClient = redissonClient,
    rate = 1,
    rateInterval = Duration.ofSeconds(1)
) {
    private val logger = logger()
    private val slackClient: MethodsClient = Slack.getInstance().methods(slackBotToken)

    fun sendPaidOrderMessage(
        orderId: UUID,
        orderName: String,
        orderPrice: BigDecimal,
        creditsUsed: BigDecimal,
        affiliateCodeUsed: String?,
        discountCodeUsed: String?,
        paymentProvider: String,
        platformType: PlatformType,
        userFullName: String,
        email: String,
        city: String,
        postCode: String,
        countryName: String,
        countryIsoCode: String,
        userOrdersTotal: Int,
        userOrdersRevenueTotal: BigDecimal,
        ordersThisDay: Int,
        ordersRevenueThisDay: BigDecimal,
        ordersThisMonth: Int,
        ordersRevenueThisMonth: BigDecimal,
        isRecurringPayment: Boolean = false,
    ) {
        val creditsText = if (creditsUsed > BigDecimal.ZERO) " (incl. ${creditsUsed.formatUSD()} credits)" else ""
        val headerText = "Price: ${orderPrice.formatUSD()}$creditsText," +
            " Today: ${ordersRevenueThisDay.formatUSD()} (count: $ordersThisDay)"
        val messageLines = mutableListOf(
            "*$headerText*",
            "———",
            "• $orderName (Order ID: <${appUrlService.createAdminOrderUrl(orderId)}|$orderId>)",
            "• $userFullName, $city $postCode, $countryName :flag-$countryIsoCode:",
            "• $email",
            "• ${orderPrice.formatUSD()}$creditsText - $paymentProvider",
            "• ${platformType.toHumanReadableFormat()}"
        )

        discountCodeUsed?.let {
            messageLines.add("• Discount code used: $it")
        }
        affiliateCodeUsed?.let {
            messageLines.add("• Affiliate code used: $it")
        }
        messageLines.add("• User has $userOrdersTotal orders with total revenue of ${userOrdersRevenueTotal.formatUSD()}")
        messageLines.add("This month: ${ordersRevenueThisMonth.formatUSD()} (count: $ordersThisMonth)")

        sendSlackMessage(
            channelId = slackOrdersChannelId,
            markdownText = messageLines.joinToString("\n"),
            notificationHeader = headerText
        )
    }

    fun sendProfitTargetHitMessage(
        tradingAccountId: UUID,
        accountId: String,
        platform: PlatformType,
        challengeName: String,
        challengePlanName: String,
        durationFoChallengeInDays: Int,
        userFullName: String,
        email: String,
        city: String,
        postCode: String,
        countryName: String,
        countryIsoCode: String,
        isManualUpgrade: Boolean,
        userOrdersTotal: Int,
        userOrdersRevenueTotal: BigDecimal,
    ) {
        sendSlackMessage(
            channelId = slackFundedAccountChannelId,
            markdownText = """
                *<${appUrlService.createAdminTradingAccountDetail(tradingAccountId)}|$accountId> (${platform.name})*
                *$challengeName - $challengePlanName*
                • ${if (isManualUpgrade) "Manual upgrade" else "User passed challenge"}
                • $userFullName, $city $postCode, $countryName :flag-$countryIsoCode:
                • $email
                • Challenge completed in $durationFoChallengeInDays days
                • User has $userOrdersTotal orders with total revenue of ${userOrdersRevenueTotal.formatUSD()}
            """.trimIndent()
        )
    }

    fun sendAccountBreachedMessage(
        tradingAccountId: UUID,
        accountId: String,
        daysSinceAccountCreation: Int,
        platform: PlatformType,
        challengeName: String,
        challengePlanName: String,
        breachType: BreachType,
        userFullName: String,
        email: String,
        challengeStepNumber: Int,
        challengeStepType: ChallengeStepType,
        city: String,
        postCode: String,
        countryName: String,
        countryIsoCode: String,
        userOrdersTotal: Int,
        userOrdersRevenueTotal: BigDecimal,
    ) {
        sendSlackMessage(
            channelId = slackAccountBreachChannelId,
            markdownText = """
                *<${appUrlService.createAdminTradingAccountDetail(tradingAccountId)}|$accountId> (${platform.name})*
                *$challengeName - $challengePlanName* ${challengeStepType.toHumanReadableFormat()} step $challengeStepNumber
                • Breach reason - ${breachType.name}
                • $userFullName, $city $postCode, $countryName :flag-$countryIsoCode:
                • $email
                • Account active for $daysSinceAccountCreation days
                • User has $userOrdersTotal orders with total revenue of ${userOrdersRevenueTotal.formatUSD()}
            """.trimIndent()
        )
    }

    fun sendPayoutCreatedMessage(
        payoutId: UUID,
        userFullName: String,
        email: String,
        amount: BigDecimal,
        profitSplit: Int,
        city: String,
        postCode: String,
        countryName: String,
        countryIsoCode: String,
        approvedPayoutsTotal: Int,
        approvedPayoutsSum: BigDecimal,
        userOrdersRevenueTotal: BigDecimal,
    ) {
        val notificationHeader = listOf(
            "Amount: ${amount.formatUSD()} ($profitSplit% split); Client: $userFullName",
            "Address: $city $postCode, $countryName :flag-$countryIsoCode:",
            "Email: $email"
        ).joinToString("\n")

        sendSlackMessage(
            channelId = slackPayoutsChannelId,
            markdownText = listOf(
                notificationHeader,
                "———",
                "$approvedPayoutsTotal total approved payouts with sum of ${approvedPayoutsSum.formatUSD()}",
                "User total revenue: ${userOrdersRevenueTotal.formatUSD()}"
            ).joinToString("\n"),
            notificationHeader = notificationHeader
        )
    }

    fun sendAffiliatePayoutCreatedMessage(
        payoutId: UUID,
        userFullName: String,
        amount: BigDecimal,
        city: String,
        postCode: String,
        countryName: String,
        countryIsoCode: String,
    ) {
        sendSlackMessage(
            channelId = slackAffiliatePayoutsChannelId,
            markdownText = """
                • ${amount.formatUSD()} (AffiliatePayoutID: <${appUrlService.createAdminAffiliatePayoutUrl(payoutId)}|$payoutId>)
                • $userFullName, $city $postCode, $countryName :flag-$countryIsoCode:
            """.trimIndent()
        )
    }

    private fun sendSlackMessage(channelId: String, markdownText: String, notificationHeader: String? = null) {
        runCatching {
            executeWithRateLimit {
                slackClient.chatPostMessage(
                    ChatPostMessageRequest.builder()
                        .channel(channelId)
                        .text(notificationHeader ?: markdownText)
                        .blocks(
                            listOf(
                                section { s: SectionBlockBuilder -> s.text(markdownText(markdownText)) },
                                divider()
                            )
                        )
                        .build()
                )
            }.let {
                if (!it.isOk) {
                    logger.error("Slack message sending failed: ${it.error}")
                    return
                }
            }
        }.onFailure {
            logger.error("Slack message sending failed!", it)
        }
    }
}
