package com.cleevio.fortraders.adapter.output.dxtrader.dto

import com.cleevio.fortraders.adapter.output.dxtrader.constant.DxTradeCashTransactionType
import com.cleevio.fortraders.domain.model.futureorder.constant.FutureTradeSide
import com.cleevio.fortraders.domain.model.futureorder.constant.FutureTradeType
import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal
import java.time.Instant

data class DxTradeGetOrderHistoryResponse(
    @JsonProperty("account") override val accountIdWithClearingCode: String,
    val orderId: String,
    val type: FutureTradeType,
    val instrument: String,
    val side: FutureTradeSide,
    val transactionTime: Instant,
    val legs: List<DxTradeOrderLegResponse>,
    val cashTransactions: List<DxTradeOrderCashTransactionResponse>,
) : DxTradeBaseAccountResponse() {
    val accountId: String = calculateAccountId()
}

data class DxTradeOrderLegResponse(
    val filledQuantity: Int,
    val averagePrice: BigDecimal,
)

data class DxTradeOrderCashTransactionResponse(
    val type: DxTradeCashTransactionType,
    val value: BigDecimal,
)
