package com.cleevio.fortraders.adapter.output.tap

import com.cleevio.fortraders.application.module.transaction.port.output.ExecutePayment
import com.cleevio.fortraders.application.module.transaction.port.output.ExecutePaymentResult
import com.cleevio.fortraders.domain.model.transaction.constant.Currency
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
@ConditionalOnProperty("fortraders.transaction.card-payment-provider", havingValue = "TAP")
class TapExecutePaymentService : ExecutePayment {
    override fun invoke(
        paymentId: String,
        transactionId: UUID,
        amount: BigDecimal,
        currency: Currency,
        userFullName: String,
        email: String,
        isSubscription: Boolean,
    ): Result<ExecutePaymentResult> = runCatching {
        error("Tap does not need to execute payment, it is done automatically")
    }
}
