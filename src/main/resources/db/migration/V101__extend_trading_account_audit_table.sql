ALTER TABLE audit.trading_account_audit
    ADD COLUMN trading_group TEXT,
    ADD COLUMN leverage INTEGER,
    ADD COLUMN inactivity_period_days INTEGER,
    ADD COLUMN starting_balance NUMERIC,
    ADD COLUMN liquidate_friday BOOLEAN,
    ADD COLUMN daily_drawdown INTEGER,
    ADD COLUMN max_drawdown INTEGER,
    ADD COLUMN min_volume NUMERIC,
    ADD COLUMN max_volume NUMERIC,
    ADD COLUMN profit_target INTEGER,
    ADD COLUMN min_profitable_trading_days INTEGER,
    ADD COLUMN max_trading_days INTEGER,
    ADD COLUMN initial_withdrawal_delay_days INTEGER,
    ADD COLUMN profit_split INTEGER;
