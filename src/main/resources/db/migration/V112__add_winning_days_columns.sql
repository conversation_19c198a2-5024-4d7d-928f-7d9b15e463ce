ALTER TABLE "trading_account"
    ADD COLUMN "minimum_winning_days"       INTEGER,
    ADD COLUMN "winning_day_minimum_amount" NUMERIC,
    DROP COLUMN "min_volume",
    DROP COLUMN "max_volume";

ALTER TABLE audit.trading_account_audit
    ADD COLUMN "minimum_winning_days"       INTEGER,
    ADD COLUMN "winning_day_minimum_amount" NUMERIC,
    DROP COLUMN "min_volume",
    DROP COLUMN "max_volume";

ALTER TABLE "challenge_plan"
    ADD COLUMN "minimum_winning_days"       INTEGER,
    ADD COLUMN "winning_day_minimum_amount" NUMERIC;
