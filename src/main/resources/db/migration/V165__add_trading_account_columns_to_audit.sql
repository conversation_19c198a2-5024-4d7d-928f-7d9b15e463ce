ALTER TABLE "audit"."trading_account_audit"
    ADD COLUMN "account_id" TEXT,
    ADD COLUMN "platform" TEXT,
    ADD COLUMN "password" TEXT;

UPDATE "audit"."trading_account_audit" taa
SET "account_id" = (SELECT "account_id" FROM "trading_account" ta WHERE ta.id = taa.id),
    "platform" = (SELECT "platform" FROM "trading_account" ta WHERE ta.id = taa.id),
    "password" = (SELECT "password" FROM "trading_account" ta WHERE ta.id = taa.id);
