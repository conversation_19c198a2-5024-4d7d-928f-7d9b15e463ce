CREATE TABLE order_challenge_step_setting_movement
(
    id                                 UUID PRIMARY KEY,
    order_id                           UUID        NOT NULL,
    challenge_step_setting_movement_id UUID        NOT NULL,
    created_at                         TIMESTAMPTZ NOT NULL,
    created_by                         <PERSON><PERSON><PERSON>,

    CONSTRAINT "d1d3b8eb1cf84a5fb096_fk" FOREIGN KEY (order_id) REFERENCES "order" (id),
    CONSTRAINT "6968cd56c3c8409b8795_fk" FOREIGN KEY (challenge_step_setting_movement_id) REFERENCES "challenge_step_setting_movement" (id)
);

CREATE INDEX "530ba60ee58e4512b59d_ix" ON order_challenge_step_setting_movement (order_id);
CREATE INDEX "badb77c9349a4b378d23_ix" ON order_challenge_step_setting_movement (challenge_step_setting_movement_id);
CREATE UNIQUE INDEX "a5dd6699d4be4e29aead_ui" ON order_challenge_step_setting_movement (order_id, challenge_step_setting_movement_id);

ALTER TABLE "order"
    DROP COLUMN profit_target,
    DROP COLUMN max_drawdown,
    DROP COLUMN daily_drawdown,
    DROP COLUMN payouts_type,
    DROP COLUMN profit_split,
    DROP COLUMN refund;

ALTER TABLE "trading_account"
    ADD COLUMN payouts_type TEXT,
    ALTER COLUMN "daily_drawdown" DROP NOT NULL;
